// 明細相關功能
let detailRowCount = 0;

// 切換明細區域顯示
function toggleDetailSection(isAutoRow=true) {
    const detailSection = document.getElementById('detailSection');
    const isChecked = document.getElementById('detailSwitch').checked;
    detailSection.classList.toggle('hidden', !isChecked);
    if(isAutoRow){
        // 如果開啟且沒有任何明細，自動新增一列
        if (isChecked && document.getElementById('detailTableBody').children.length === 0) {
            addDetailRow();
        }
    }
}

// 重置明細區域
function resetDetailSection() {
    const detailSwitch = document.getElementById('detailSwitch');
    const detailSection = document.getElementById('detailSection');
    const detailTableBody = document.getElementById('detailTableBody');
    
    // 關閉開關
    detailSwitch.checked = false;
    // 隱藏明細區域
    detailSection.classList.add('hidden');
    // 清空明細表
    detailTableBody.innerHTML = '';
}

// 新增明細列
function addDetailRow() {
    const tbody = document.getElementById('detailTableBody');
    const currentRows = tbody.getElementsByTagName('tr');
    const newRowNumber = currentRows.length + 1;
    
    const tr = document.createElement('tr');
    tr.className = 'hover:bg-gray-50';
    tr.innerHTML = `
        <td class="px-4 py-2">${newRowNumber}</td>
        <td class="px-4 py-2">
            <input type="text" name="detail_item_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   required>
        </td>
        <td class="px-4 py-2">
            <input type="number" name="detail_price_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   onchange="calculateTotal(${newRowNumber})" required>
        </td>
        <td class="px-4 py-2">
            <input type="number" name="detail_quantity_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   onchange="calculateTotal(${newRowNumber})" required>
        </td>
        <td class="px-4 py-2">
            <input type="text" name="detail_unit_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   required>
        </td>
        <td class="px-4 py-2">
            <input type="number" name="detail_total_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   readonly>
        </td>
        <td class="px-4 py-2">
            <button type="button" onclick="deleteDetailRow(this)" 
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                刪除
            </button>
        </td>
    `;
    tbody.appendChild(tr);
}

// 刪除明細列
function deleteDetailRow(button) {
    const row = button.closest('tr');
    row.remove();
    
    // 重新編號並更新輸入欄位的名稱
    const tbody = document.getElementById('detailTableBody');
    const rows = tbody.getElementsByTagName('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const rowNum = i + 1;
        const row = rows[i];
        
        // 更新編號
        row.cells[0].textContent = rowNum;
        
        // 更新各輸入欄位的 name 屬性
        const inputs = row.getElementsByTagName('input');
        inputs[0].name = `detail_item_${rowNum}`;
        inputs[1].name = `detail_price_${rowNum}`;
        inputs[2].name = `detail_quantity_${rowNum}`;
        inputs[3].name = `detail_unit_${rowNum}`;
        inputs[4].name = `detail_total_${rowNum}`;
        
        // 更新 onchange 事件處理器
        inputs[1].setAttribute('onchange', `calculateTotal(${rowNum})`);
        inputs[2].setAttribute('onchange', `calculateTotal(${rowNum})`);
    }
    
    // 更新總金額
    updateTotalAmount();
}

// 計算複價
function calculateTotal(rowNumber) {
    const priceInput = document.getElementsByName(`detail_price_${rowNumber}`)[0];
    const quantityInput = document.getElementsByName(`detail_quantity_${rowNumber}`)[0];
    const totalInput = document.getElementsByName(`detail_total_${rowNumber}`)[0];
    
    const price = parseFloat(priceInput.value) || 0;
    const quantity = parseFloat(quantityInput.value) || 0;
    const total = price * quantity;
    
    totalInput.value = total.toFixed(2);
    
    // 更新總金額
    updateTotalAmount();
}

// 更新總金額
function updateTotalAmount() {
    const tbody = document.getElementById('detailTableBody');
    const rows = tbody.getElementsByTagName('tr');
    let totalAmount = 0;
    
    for (const row of rows) {
        const totalInput = row.querySelector('input[name^="detail_total_"]');
        totalAmount += parseFloat(totalInput.value) || 0;
    }
    
    // 更新表單中的金額欄位
    document.getElementById('amount').value = totalAmount.toFixed(0);
    // 觸發 input 事件以觸發稅額計算
    document.getElementById('amount').dispatchEvent(new Event('input', { bubbles: true }));
} 

// 使用資料自動輸入新增明細列
function keyDetailRowWithData(detail) {
    const tbody = document.getElementById('detailTableBody');
    const currentRows = tbody.getElementsByTagName('tr');
    const newRowNumber = currentRows.length + 1;
    
    const tr = document.createElement('tr');
    tr.className = 'hover:bg-gray-50';
    tr.innerHTML = `
        <td class="px-4 py-2">${newRowNumber}</td>
        <td class="px-4 py-2">
            <input type="text" name="detail_item_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   value="${detail.item}" required>
        </td>
        <td class="px-4 py-2">
            <input type="number" name="detail_price_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   value="${detail.price}" onchange="calculateTotal(${newRowNumber})" required>
        </td>
        <td class="px-4 py-2">
            <input type="number" name="detail_quantity_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   value="${detail.quantity}" onchange="calculateTotal(${newRowNumber})" required>
        </td>
        <td class="px-4 py-2">
            <input type="text" name="detail_unit_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   value="${detail.unit}" required>
        </td>
        <td class="px-4 py-2">
            <input type="number" name="detail_total_${newRowNumber}" 
                   class="w-full px-3 py-1.5 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
                   value="${detail.total}" readonly>
        </td>
        <td class="px-4 py-2">
            <button type="button" onclick="deleteDetailRow(this)" 
                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                刪除
            </button>
        </td>
    `;
    tbody.appendChild(tr);
}