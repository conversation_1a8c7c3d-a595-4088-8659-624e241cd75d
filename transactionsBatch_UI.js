/**
 * @file transactionsBatch_UI.js
 * @description 批次交易 UI 管理模組
 * 負責處理批次交易介面的動態生成、更新和互動
 */

/**
 * 更新表格標題
 */
function updateTableHeaders() {
    const headerRow = document.getElementById('batchTableHeader');
    
    // 清除現有的動態標題（保留 # 和操作欄）
    const staticHeaders = headerRow.querySelectorAll('th');
    for (let i = staticHeaders.length - 1; i > 0; i--) {
        if (i < staticHeaders.length - 1) { // 保留最後一個操作欄
            staticHeaders[i].remove();
        }
    }
    
    // 定義所有可能的欄位
    const allFields = [
        { key: 'transactionType', label: '交易類型', required: true },
        { key: 'account', label: '主要帳戶', required: true },
        { key: 'paymentStatus', label: '到帳情形', required: true },
        { key: 'paymentDate', label: '付款日期', required: false },
        { key: 'invoiceDate', label: '發票日期', required: false },
        { key: 'entity', label: '交易對象', required: true },
        { key: 'paymentDescription', label: '交易項目', required: true },
        { key: 'amount', label: '金額', required: true },
        { key: 'taxType', label: '稅別', required: true },
        { key: 'taxAmount', label: '稅額', required: false },
        { key: 'invoiceNumber', label: '發票號碼', required: false },
        { key: 'fee', label: '手續費', required: false },
        { key: 'status', label: '狀態', required: true },
        { key: 'notes', label: '備註', required: false }
    ];
    
    // 插入動態標題（在操作欄之前）
    const operationHeader = headerRow.lastElementChild;
    
    allFields.forEach(field => {
        if (!commonFields[field.key]) { // 只顯示非共同欄位
            const th = document.createElement('th');
            th.className = 'min-w-32';
            th.innerHTML = field.required ? 
                `${field.label}<span class="text-red-500">*</span>` : 
                field.label;
            headerRow.insertBefore(th, operationHeader);
        }
    });
}

/**
 * 新增批次資料行
 */
function addBatchRow() {
    const tbody = document.getElementById('batchTableBody');
    const rowIndex = currentRowIndex++;
    
    const row = document.createElement('tr');
    row.id = `batchRow_${rowIndex}`;
    row.dataset.rowIndex = rowIndex;
    
    // 行號欄
    const indexCell = document.createElement('td');
    indexCell.className = 'text-center font-medium';
    indexCell.textContent = rowIndex + 1;
    row.appendChild(indexCell);
    
    // 動態欄位
    addDynamicCells(row, rowIndex);
    
    // 操作欄
    const actionCell = document.createElement('td');
    actionCell.className = 'text-center';
    actionCell.innerHTML = `
        <button type="button" onclick="removeBatchRow(${rowIndex})" 
            class="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600">
            <i class="fas fa-trash"></i>
        </button>
    `;
    row.appendChild(actionCell);
    
    tbody.appendChild(row);
    
    // 初始化該行的事件監聽
    initializeRowEventListeners(rowIndex);
    
    // 更新行數統計
    updateRowCount();
    
    // 初始化該行的資料
    batchTransactionData[rowIndex] = createEmptyTransactionData();
}

/**
 * 添加動態欄位到行
 */
function addDynamicCells(row, rowIndex) {
    const allFields = [
        { key: 'transactionType', type: 'radio' },
        { key: 'account', type: 'select' },
        { key: 'paymentStatus', type: 'select' },
        { key: 'paymentDate', type: 'date' },
        { key: 'invoiceDate', type: 'date' },
        { key: 'entity', type: 'entity-search' },
        { key: 'paymentDescription', type: 'payment-description' },
        { key: 'amount', type: 'number' },
        { key: 'taxType', type: 'select' },
        { key: 'taxAmount', type: 'number' },
        { key: 'invoiceNumber', type: 'text' },
        { key: 'fee', type: 'fee' },
        { key: 'status', type: 'select' },
        { key: 'notes', type: 'textarea' }
    ];
    
    allFields.forEach(field => {
        if (!commonFields[field.key]) { // 只顯示非共同欄位
            const cell = document.createElement('td');
            cell.appendChild(createFieldElement(field, rowIndex));
            row.appendChild(cell);
        }
    });
}

/**
 * 創建欄位元素
 */
function createFieldElement(field, rowIndex) {
    const fieldId = `batch_${field.key}_${rowIndex}`;
    
    switch (field.type) {
        case 'radio':
            return createTransactionTypeRadio(fieldId, rowIndex);
        case 'select':
            return createSelectElement(field.key, fieldId, rowIndex);
        case 'date':
            return createDateElement(fieldId);
        case 'number':
            return createNumberElement(fieldId, field.key);
        case 'text':
            return createTextElement(fieldId);
        case 'textarea':
            return createTextareaElement(fieldId);
        case 'entity-search':
            return createEntitySearchElement(fieldId, rowIndex);
        case 'payment-description':
            return createPaymentDescriptionElement(fieldId, rowIndex);
        case 'fee':
            return createFeeElement(fieldId, rowIndex);
        default:
            return createTextElement(fieldId);
    }
}

/**
 * 創建交易類型單選按鈕
 */
function createTransactionTypeRadio(fieldId, rowIndex) {
    const container = document.createElement('div');
    container.className = 'flex flex-col space-y-1';
    
    const types = [
        { value: 'expense', label: '支出', color: 'red' },
        { value: 'income', label: '收入', color: 'blue' },
        { value: 'transfer', label: '轉移', color: 'green' }
    ];
    
    types.forEach(type => {
        const wrapper = document.createElement('div');
        wrapper.className = 'flex items-center';
        
        const input = document.createElement('input');
        input.type = 'radio';
        input.id = `${fieldId}_${type.value}`;
        input.name = `${fieldId}`;
        input.value = type.value;
        input.className = 'mr-1';
        if (type.value === 'expense') input.checked = true; // 預設選擇支出
        
        const label = document.createElement('label');
        label.htmlFor = input.id;
        label.textContent = type.label;
        label.className = `text-xs text-${type.color}-600`;
        
        wrapper.appendChild(input);
        wrapper.appendChild(label);
        container.appendChild(wrapper);
    });
    
    return container;
}

/**
 * 創建下拉選單元素
 */
function createSelectElement(fieldKey, fieldId, rowIndex) {
    const select = document.createElement('select');
    select.id = fieldId;
    select.className = 'w-full p-1 text-xs border border-gray-300 rounded';
    
    // 根據欄位類型添加選項
    switch (fieldKey) {
        case 'account':
            select.innerHTML = '<option value="">選擇帳戶</option>';
            // 複製共同欄位的選項
            const commonAccountSelect = document.getElementById('common_accountSelect');
            for (let i = 1; i < commonAccountSelect.options.length; i++) {
                const option = commonAccountSelect.options[i].cloneNode(true);
                select.appendChild(option);
            }
            break;
            
        case 'paymentStatus':
            select.innerHTML = `
                <option value="">選擇狀態</option>
                <option value="same_day">同日收付款</option>
                <option value="receivable">應收付款</option>
                <option value="prepayment">暫收付款</option>
                <option value="different_day">非同日收款</option>
            `;
            break;
            
        case 'taxType':
            select.innerHTML = '<option value="">選擇稅別</option>';
            // 複製共同欄位的選項
            const commonTaxSelect = document.getElementById('common_taxTypeSelect');
            for (let i = 1; i < commonTaxSelect.options.length; i++) {
                const option = commonTaxSelect.options[i].cloneNode(true);
                select.appendChild(option);
            }
            break;
            
        case 'status':
            select.innerHTML = `
                <option value="">選擇狀態</option>
                <option value="completed">已完成</option>
                <option value="pending">未撥款</option>
            `;
            break;
    }
    
    return select;
}

/**
 * 創建日期輸入元素
 */
function createDateElement(fieldId) {
    const input = document.createElement('input');
    input.type = 'date';
    input.id = fieldId;
    input.className = 'w-full p-1 text-xs border border-gray-300 rounded';
    
    // 設定預設值為今天
    const today = new Date().toISOString().split('T')[0];
    input.value = today;
    
    return input;
}

/**
 * 創建數字輸入元素
 */
function createNumberElement(fieldId, fieldKey) {
    const input = document.createElement('input');
    input.type = 'number';
    input.id = fieldId;
    input.className = 'w-full p-1 text-xs border border-gray-300 rounded';
    input.min = '0';
    
    if (fieldKey === 'amount' || fieldKey === 'fee') {
        input.step = '0.01';
        input.placeholder = '0.00';
    } else {
        input.placeholder = '0';
    }
    
    return input;
}

/**
 * 創建文字輸入元素
 */
function createTextElement(fieldId) {
    const input = document.createElement('input');
    input.type = 'text';
    input.id = fieldId;
    input.className = 'w-full p-1 text-xs border border-gray-300 rounded';
    
    return input;
}

/**
 * 創建文字區域元素
 */
function createTextareaElement(fieldId) {
    const textarea = document.createElement('textarea');
    textarea.id = fieldId;
    textarea.className = 'w-full p-1 text-xs border border-gray-300 rounded';
    textarea.rows = 2;
    
    return textarea;
}

/**
 * 創建交易對象搜尋元素
 */
function createEntitySearchElement(fieldId, rowIndex) {
    const container = document.createElement('div');
    container.className = 'relative';

    const input = document.createElement('input');
    input.type = 'text';
    input.id = fieldId;
    input.className = 'w-full p-1 text-xs border border-gray-300 rounded';
    input.placeholder = '搜尋交易對象';

    const resultsDiv = document.createElement('div');
    resultsDiv.id = `${fieldId}_results`;
    resultsDiv.className = 'absolute z-10 w-full bg-white shadow-lg rounded-md mt-1 hidden max-h-32 overflow-y-auto';

    const hiddenIdInput = document.createElement('input');
    hiddenIdInput.type = 'hidden';
    hiddenIdInput.id = `${fieldId}_id`;

    const hiddenTypeInput = document.createElement('input');
    hiddenTypeInput.type = 'hidden';
    hiddenTypeInput.id = `${fieldId}_type`;

    container.appendChild(input);
    container.appendChild(resultsDiv);
    container.appendChild(hiddenIdInput);
    container.appendChild(hiddenTypeInput);

    // 添加搜尋事件監聽
    input.addEventListener('input', function() {
        handleEntitySearch(this.value, rowIndex);
    });

    return container;
}

/**
 * 創建交易項目選單元素
 */
function createPaymentDescriptionElement(fieldId, rowIndex) {
    const container = document.createElement('div');
    container.className = 'relative';

    const button = document.createElement('button');
    button.type = 'button';
    button.id = `${fieldId}_btn`;
    button.className = 'w-full text-left p-1 text-xs border border-gray-300 rounded bg-white hover:bg-gray-50';
    button.innerHTML = '<span>選擇交易項目</span><i class="fas fa-chevron-down float-right mt-0.5"></i>';

    const dropdown = document.createElement('div');
    dropdown.id = `${fieldId}_dropdown`;
    dropdown.className = 'absolute z-10 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-32 overflow-y-auto';

    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.id = fieldId;

    container.appendChild(button);
    container.appendChild(dropdown);
    container.appendChild(hiddenInput);

    // 添加點擊事件監聽
    button.addEventListener('click', function() {
        togglePaymentDescriptionDropdown(rowIndex);
    });

    return container;
}

/**
 * 創建手續費元素
 */
function createFeeElement(fieldId, rowIndex) {
    const container = document.createElement('div');
    container.className = 'flex items-center space-x-1';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.id = `${fieldId}_toggle`;
    checkbox.className = 'text-xs';

    const input = document.createElement('input');
    input.type = 'number';
    input.id = fieldId;
    input.className = 'flex-1 p-1 text-xs border border-gray-300 rounded';
    input.min = '0';
    input.step = '0.01';
    input.placeholder = '0.00';
    input.disabled = true;

    checkbox.addEventListener('change', function() {
        input.disabled = !this.checked;
        if (!this.checked) {
            input.value = '';
        }
    });

    container.appendChild(checkbox);
    container.appendChild(input);

    return container;
}

/**
 * 移除批次資料行
 */
function removeBatchRow(rowIndex) {
    const row = document.getElementById(`batchRow_${rowIndex}`);
    if (row) {
        row.remove();

        // 從資料陣列中移除
        delete batchTransactionData[rowIndex];

        // 更新行號
        updateRowNumbers();

        // 更新行數統計
        updateRowCount();
    }
}

/**
 * 更新行號顯示
 */
function updateRowNumbers() {
    const rows = document.querySelectorAll('#batchTableBody tr');
    rows.forEach((row, index) => {
        const indexCell = row.querySelector('td:first-child');
        if (indexCell) {
            indexCell.textContent = index + 1;
        }
    });
}

/**
 * 清空所有行
 */
function clearAllRows() {
    if (confirm('確定要清空所有交易資料嗎？')) {
        const tbody = document.getElementById('batchTableBody');
        tbody.innerHTML = '';
        batchTransactionData = [];
        currentRowIndex = 0;
        updateRowCount();

        // 添加一行新的空白行
        addBatchRow();
    }
}

/**
 * 更新所有批次資料行
 */
function updateAllBatchRows() {
    const rows = document.querySelectorAll('#batchTableBody tr');
    rows.forEach(row => {
        const rowIndex = row.dataset.rowIndex;
        if (rowIndex !== undefined) {
            updateBatchRowFromCommonFields(parseInt(rowIndex));
        }
    });
}

/**
 * 根據共同欄位更新特定行
 */
function updateBatchRowFromCommonFields(rowIndex) {
    Object.keys(commonFields).forEach(fieldKey => {
        const commonElement = document.getElementById(`common_${fieldKey}`);
        const batchElement = document.getElementById(`batch_${fieldKey}_${rowIndex}`);

        if (commonElement && batchElement) {
            // 根據欄位類型處理值的複製
            if (commonElement.type === 'radio') {
                const checkedRadio = document.querySelector(`input[name="common_${fieldKey}"]:checked`);
                if (checkedRadio) {
                    const batchRadio = document.querySelector(`input[name="batch_${fieldKey}_${rowIndex}"][value="${checkedRadio.value}"]`);
                    if (batchRadio) {
                        batchRadio.checked = true;
                    }
                }
            } else {
                batchElement.value = commonElement.value;
            }
        }
    });
}

/**
 * 初始化行事件監聽器
 */
function initializeRowEventListeners(rowIndex) {
    // 金額變更時自動計算稅額
    const amountInput = document.getElementById(`batch_amount_${rowIndex}`);
    const taxTypeSelect = document.getElementById(`batch_taxType_${rowIndex}`);
    const taxAmountInput = document.getElementById(`batch_taxAmount_${rowIndex}`);

    if (amountInput && taxTypeSelect && taxAmountInput) {
        const calculateTax = () => {
            const amount = parseFloat(amountInput.value) || 0;
            const selectedOption = taxTypeSelect.options[taxTypeSelect.selectedIndex];
            const taxRate = parseFloat(selectedOption.dataset.rate) || 0;

            if (amount > 0 && taxRate > 0) {
                // 使用與原系統相同的稅額計算邏輯
                const amountNoTax = Math.round(amount / (1 + Number(taxRate)));
                const taxAmount = amount - amountNoTax;
                taxAmountInput.value = taxAmount.toFixed(2);
            } else {
                taxAmountInput.value = '';
            }
        };

        amountInput.addEventListener('input', calculateTax);
        taxTypeSelect.addEventListener('change', calculateTax);
    }
}

/**
 * 創建空的交易資料物件
 */
function createEmptyTransactionData() {
    return {
        transactionType: 'expense',
        accountId: '',
        paymentStatus: '',
        paymentDate: '',
        invoiceDate: '',
        entityId: '',
        entityType: '',
        paymentDescription: '',
        amount: 0,
        taxTypeId: '',
        taxAmount: 0,
        invoiceNumber: '',
        fee: 0,
        transactionStatus: '',
        notes: '',
        tags: []
    };
}

// 匯出函式供其他模組使用
window.addBatchRow = addBatchRow;
window.removeBatchRow = removeBatchRow;
window.clearAllRows = clearAllRows;
window.updateTableHeaders = updateTableHeaders;
window.updateAllBatchRows = updateAllBatchRows;
