/**
 * @file transactionCreate_EntitySearch_Module.js
 * @description 這個檔案負責管理 交易對象(搜尋 帳戶 員工 客戶 供應商等 及 新增臨時對象)資料表操作
 * */


class EntitySearchManager {
    #accounts;
    #employees;
    #entities;
    //建立Id MAP
    #accountsMap;
    #employeesMap;
    #entitiesMap;

    constructor(accounts, employees, entities) {
        this.#accounts = accounts;
        this.#employees = employees;
        this.#entities = entities;
        this.#accountsMap = new Map(accounts.map(account => [account.id, account]));
        this.#employeesMap = new Map(employees.map(employee => [employee.id, employee]));
        this.#entitiesMap = new Map(entities.map(entity => [entity.id, entity]));
    }
    /**私有 */
    
    /**私有 */
    // 輔助函數：搜尋帳戶
    async #searchAccounts(query) {
        const accounts = this.#accounts;
        return accounts.filter(account =>
            account.name.toLowerCase().includes(query.toLowerCase()) ||
            account.accountNumber.toLowerCase().includes(query.toLowerCase())
        );
    }
    
    /**私有 */
    // 輔助函數：搜尋員工
    async #searchEmployees(query) {
        const employees =  this.#employees;
        return employees.filter(employee =>
            employee.name.toLowerCase().includes(query.toLowerCase()) ||
            (employee.employeeId && employee.employeeId.toLowerCase().includes(query.toLowerCase())) ||
            (employee.position && employee.position.toLowerCase().includes(query.toLowerCase()))
        );
    }

    /**私有 */
    // 輔助函數：搜尋 對象資料表 @param {*} type // array of types ['client', 'supplier', 'temporary']
    async #searchEntities(query, type = []) {
        const entities = this.#entities;
        let filteredEntities = [];

        // 如果type為空，則搜尋所有類型
        if (type.length === 0) {
            filteredEntities = entities.filter(entity => entity.type === type);
        }else{
            filteredEntities = entities.filter(entity => type.includes(entity.type));
        }

        // 搜尋名稱或編號
        filteredEntities = filteredEntities.filter(entity =>
            entity.name.toLowerCase().includes(query.toLowerCase()) ||
            (entity.code && entity.code.toLowerCase().includes(query.toLowerCase()))
        );

        return filteredEntities;
    }


    /**公開方法 */
    // 搜尋所有可能的交易對象（包括帳戶和其他對象）
    async searchAllEntities(query,type = 'all') {
        try {
            // 並行搜尋帳戶、員工和 對象(客戶、供應商、臨時對象)
            const [accounts, entities, employees] = await Promise.all([
                type === 'all' || type === 'transfer' ? this.#searchAccounts(query) : [],
                type === 'all' ? this.#searchEntities(query) : type === 'income'? this.#searchEntities(query,['client', 'temporary']) : type === 'expense' ? this.#searchEntities(query,['supplier', 'temporary']) : [],
                type === 'all' || type === 'expense' ? this.#searchEmployees(query) : []
            ]);

            // 整合並格式化結果
            const formattedResults = [
                ...accounts.map(account => ({
                    id: `${account.id}`,
                    name: account.name,
                    type: 'account',
                    originalData: account
                })),
                ...entities.map(entity => ({
                    id: `${entity.id}`,
                    name: entity.name,
                    type: entity.type,
                    originalData: entity
                })),
                ...employees.map(employee => ({
                    id: `${employee.id}`,
                    name: employee.name,
                    type: 'employee',
                    originalData: employee
                }))
            ];

            return formattedResults;
        } catch (error) {
            console.error('搜尋交易對象時發生錯誤:', error);
            throw error;
        }
    }

    /**公開方法 */
    // 根據ID和類型獲取交易對象
    getEntity(entityId, entityType) {
        let entity = null;
        let selectedEntity;
        if (entityType === 'account') {
            // 如果是帳戶類型，從帳戶表中獲取
            entity = this.#accountsMap.get(entityId);
        } else if (entityType === 'employee') {
            // 如果是員工類型，從員工表中獲取
            entity = this.#employeesMap.get(entityId);
        } else {
            // 從對象(客戶、供應商、臨時對象)表中獲取
            entity = this.#entitiesMap.get(entityId);
        }

        if (entity) {
            selectedEntity = {
                id: entityId,
                name: entity.name,
                type: entityType !== 'employee' && entityType !== 'account' ? entity.type : entityType,
                originalData: entity
            };
        }

        if (selectedEntity) {
            return selectedEntity;
        } else {
            console.error('找不到指定的交易對象');
            return null;
        }
    }

    /**私有方法 */
    // 生成臨時對象代碼
    #generateTemporaryCode() {
        return 'TMP' + Date.now().toString().slice(-6);
    }

    /**公開方法 */
    // 創建一個臨時交易對象
    async createTemporaryEntity(name) {
        const tempCode = this.#generateTemporaryCode();
        const temporaryEntity = {
            name: name,
            code: tempCode,
            type: 'temporary'
        };

        // 新增到資料庫
        let id = await addTemporaryEntity(temporaryEntity);

        if(!id){
            console.error('新增臨時交易對象失敗');
            return null;
        }else{
            //更新MAP及對象資料表
            this.#entitiesMap.set(id, temporaryEntity);
            this.#entities.push(temporaryEntity);
        }

        // 創建一個臨時交易對象
        const newEntity = {
            id: id,
            name: name,
            type: 'temporary',
            originalData: temporaryEntity
        };

        return newEntity;
    }
}

//-------------- UI 處理常式 --------------


//載入初始化交易對象欄位
async function loadEntitySearchOptions() {
    const {entitySearchInput} = getTransactionFormCard_DOMElements();
    // 調整輸入框樣式
    entitySearchInput.style.width = 'auto';
    entitySearchInput.style.minWidth = '60px';
    entitySearchInput.style.flex = '1';
    entitySearchInput.style.padding = '0';
    entitySearchInput.style.border = 'none';
    entitySearchInput.style.boxShadow = 'none';
    // 包裝容器
    const wrapper = document.createElement('div');
    wrapper.className = 'flex flex-wrap items-center gap-1 mt-1 p-2 shadow-sm border border-gray-300 rounded-md focus-within:border-blue-500 focus-within:border-2 bg-white';
    entitySearchInput.parentNode.insertBefore(wrapper, entitySearchInput);
    wrapper.appendChild(selectedEntityContainer);
    wrapper.appendChild(entitySearchInput);

    // 初始化交易對象管理器
    const accounts = await getAccountsAll();
    const employees = await getEmployeesAll();
    const entities = await getEntitiesAll();
    // 將交易對象管理器暴露到全局作用域
    window.entitySearchManager = new EntitySearchManager(accounts, employees, entities);

}

//初始化監聽交易對象搜尋框
function load_EntitySearch_EventListener(){
    const {entitySearchInput} = getTransactionFormCard_DOMElements();
    // 輸入框-監聽點擊事件
    entitySearchInput.addEventListener('focus', async function () {
        //取得目前交易類型
        const selectedTransactionType = document.querySelector('input[name="transactionType"]:checked').value;
        const results = await entitySearchManager.searchAllEntities('', selectedTransactionType);
        displaySearchResults(results.slice(0, 30));
    });
    
    // 輸入框-監聽輸入事件
    entitySearchInput.addEventListener('input', async function () {
        //取得目前交易類型
        const selectedTransactionType = document.querySelector('input[name="transactionType"]:checked').value;
        const query = this.value.trim();
        if (query.length > 0) {
            //console.log('query', query);
            //console.log('selectedTransactionType', selectedTransactionType);
            const results = await entitySearchManager.searchAllEntities(query,selectedTransactionType);
            displaySearchResults(results);
        } else {
            const results = await entitySearchManager.searchAllEntities('', selectedTransactionType);
            displaySearchResults(results.slice(0, 5));
        }
    });

    
    // 點擊外部關閉搜尋結果
    document.addEventListener('mousedown', function(e) {
        // 檢查點擊是否在搜尋結果區域外
        if (searchResults && !searchResults.contains(e.target) && !entitySearchInput.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });

}


// 顯示搜尋結果
function displaySearchResults(results) {
    const entityInput = document.getElementById('entitySearch');
    const searchResults = document.getElementById('searchResults');

    searchResults.innerHTML = '';
    searchResults.classList.remove('hidden');

    // 添加搜尋結果容器樣式
    searchResults.className = 'absolute w-full bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto';

    if (!results || results.length === 0) {
        // 顯示新增臨時對象選項
        const addNewItem = document.createElement('div');
        addNewItem.className = 'p-3 hover:bg-gray-100 cursor-pointer flex items-center text-blue-600';
        addNewItem.innerHTML = `
            <i class="fas fa-plus-circle mr-2"></i>
            <span>新增臨時對象</span>
        `;
        addNewItem.onclick = async () => {
            const name = entityInput.value.trim();
            
            try {
                // 創建臨時交易對象
                const newEntity = await entitySearchManager.createTemporaryEntity(name);
                // 選擇新建的臨時對象
                selectEntity(newEntity);

                // 清空並隱藏搜尋結果
                searchResults.innerHTML = '';
                searchResults.classList.add('hidden');
            } catch (error) {
                console.error('創建臨時對象時發生錯誤:', error);
                alert('創建臨時對象失敗，請稍後再試');
            }
        };
        searchResults.appendChild(addNewItem);
        return;
    }

    // 添加搜尋結果標題
    const header = document.createElement('div');
    header.className = 'px-3 py-2 text-sm text-gray-500 bg-gray-50 border-b';
    header.textContent = `找到 ${results.length} 個結果`;
    searchResults.appendChild(header);

    results.forEach(result => {
        const item = document.createElement('div');
        item.className = 'p-3 hover:bg-gray-100 cursor-pointer border-b last:border-b-0';
        item.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="font-medium">${result.name}</span>
                </div>
                <span class="text-sm text-gray-500 ml-2">${getEntityTypeLabel(result.type)}</span>
            </div>
            ${result.code ? `<div class="text-sm text-gray-500 mt-1">${result.code}</div>` : ''}
        `;
        item.onclick = () => selectEntity(result);
        searchResults.appendChild(item);
    });
}

let selectedEntity = null; //全局變數，用來儲存已選中的交易對象

// 選擇交易對象
function selectEntity(entity) {
    const entityInput = document.getElementById('entitySearch');
    const searchResults = document.getElementById('searchResults');
    const selectedEntityContainer = document.getElementById('selectedEntityContainer');
    const selectedEntityIdInput = document.getElementById('selectedEntityId');
    const selectedEntityTypeInput = document.getElementById('selectedEntityType');

    if(entity.id && entity.type && entity.name){//判斷entity是否包含id, name,type的三個屬性
        //滿足三個屬性時，直接選取
        selectedEntity = entity;
    }else if(entity.id && entity.type){//判斷entity至少包含id, type的二個屬性
        //滿足二個屬性時，由entitySearchManager取得完整的entity資料
        selectedEntity = entitySearchManager.getEntity(entity.id, entity.type);
    }else{
        return;
    }
    
    selectedEntityIdInput.value = selectedEntity.id;
    selectedEntityTypeInput.value = selectedEntity.type;
    entityInput.value = '';
    searchResults.classList.add('hidden');

    // 隱藏輸入框
    entityInput.classList.add('hidden');

    // 清空並更新選中的交易對象顯示
    selectedEntityContainer.innerHTML = '';
    const badge = createEntityBadge(selectedEntity);
    selectedEntityContainer.appendChild(badge);
}

// 創建交易對象標籤
function createEntityBadge(entity) {
    const entityInput = document.getElementById('entitySearch');
    const selectedEntityContainer = document.getElementById('selectedEntityContainer');
    const selectedEntityIdInput = document.getElementById('selectedEntityId');
    const selectedEntityTypeInput = document.getElementById('selectedEntityType');

    const badge = document.createElement('div');
    badge.className = 'inline-flex items-center bg-gray-100 text-blue-800 rounded-full px-3 py-1 text-sm font-medium';

    // 添加實體名稱
    const nameSpan = document.createElement('span');
    nameSpan.textContent = entity.name;
    badge.appendChild(nameSpan);

    // 添加類型標籤
    const typeSpan = document.createElement('span');
    typeSpan.className = 'ml-2 bg-blue-200 text-blue-700 px-2 rounded-full text-xs';
    typeSpan.textContent = getEntityTypeLabel(entity.type);
    badge.appendChild(typeSpan);

    const deleteButton = document.createElement('button');
    deleteButton.className = 'ml-2 text-blue-400 hover:text-blue-900';
    deleteButton.innerHTML = '&times;';
    deleteButton.type = 'button';//確保它是按鈕而不是提交表單
    deleteButton.onclick = function () {
        selectedEntityContainer.removeChild(badge);
        selectedEntity = null;
        selectedEntityIdInput.value = '';
        selectedEntityTypeInput.value = '';
        // 顯示輸入框
        entityInput.classList.remove('hidden');
    };

    badge.appendChild(deleteButton);
    return badge;
}
