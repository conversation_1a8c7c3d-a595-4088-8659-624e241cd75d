<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次新增交易 - 益芯能源工程-管理系統</title>
    <link rel="icon" href="../../common/img/logo.png" type="image/x-icon">

    <!-- 模組導入映射設定 -->
    <script type="importmap">
    {
        "imports": {
        "@common/": "/common/"
        }
    }
    </script>

    <!-- 樣式庫載入 -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Firebase 相關庫載入 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-analytics-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-auth-compat.js"></script>

    <!-- 自定義工具庫載入 -->
    <script src="../../common/firebaseAPI/auth.js"></script>
    <script src="../../common/db/db.js"></script>
    <script src="../../common/db/preload.js"></script>
    <script src="../../common/utils/CommonUtils.js"></script>
    <script src="../../common/utils/pageTransfer.js"></script>
    <script src="../../common/utils/ModalUtils.js"></script>
    <script src="../../common/utils/DatabaseErrors.js"></script>
</head>

<body class="bg-gray-100">
    <!-- 頁面頂部導航 -->
    <div id="navbar-container"></div>

    <main class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">批次新增交易</h1>
            <p class="text-gray-600">一次性填寫多筆交易資訊</p>
        </div>

        <!-- 共同屬性區域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">共同屬性設定</h2>
                <div class="flex gap-2">
                    <button id="minimalPropsBtn" class="px-3 py-1 bg-blue-100 rounded text-sm">最小共同屬性</button>
                    <button id="standardPropsBtn" class="px-3 py-1 bg-blue-100 rounded text-sm">標準模式</button>
                    <button id="maximalPropsBtn" class="px-3 py-1 bg-blue-100 rounded text-sm">最大共同屬性</button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <!-- 共同屬性選擇區 -->
                <div class="col-span-1 border-r pr-4">
                    <h3 class="font-bold mb-2 text-sm text-gray-700">選擇共同屬性</h3>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="transactionType" checked>
                            <span class="ml-2 text-sm">交易類型</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="accountId" checked>
                            <span class="ml-2 text-sm">我方主要帳戶</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="paymentDate" checked>
                            <span class="ml-2 text-sm">收款/付款日期</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="paymentStatus" checked>
                            <span class="ml-2 text-sm">帳款到帳情形</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="invoiceDate">
                            <span class="ml-2 text-sm">憑證/發票日期</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="expectedPaymentDate">
                            <span class="ml-2 text-sm">預計收/付款日期</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="taxTypeId" checked>
                            <span class="ml-2 text-sm">稅別</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="paymentDescription">
                            <span class="ml-2 text-sm">交易描述</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="common-property-toggle" data-field="entityId">
                            <span class="ml-2 text-sm">交易對象</span>
                        </label>
                    </div>
                </div>

                <!-- 共同屬性表單區 -->
                <div class="col-span-2" id="commonPropertiesForm">
                    <!-- 這裡將動態生成共同屬性表單 -->
                </div>
            </div>
        </div>

        <!-- 批次交易表格 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">批次交易明細</h2>
                <div class="flex gap-2">
                    <button id="addRowBtn" class="px-3 py-1 bg-green-500 text-white rounded">
                        <i class="fas fa-plus mr-1"></i>新增一行
                    </button>
                    <button id="copyLastRowBtn" class="px-3 py-1 bg-blue-500 text-white rounded">
                        <i class="fas fa-copy mr-1"></i>複製上一行
                    </button>
                </div>
            </div>

            <div class="overflow-visible">
                <table class="w-full border-collapse" id="batchTable">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="p-2 border">序號</th>
                            <th class="p-2 border">金額 (含稅)</th>
                            <!-- 其他欄位將根據共同屬性動態生成 -->
                            <th class="p-2 border">備註</th>
                            <th class="p-2 border">操作</th>
                        </tr>
                    </thead>
                    <tbody id="batchTableBody">
                        <!-- 初始行將在JS中添加 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 批次操作按鈕 -->
        <div class="flex justify-between items-center">
            <button id="cancelBtn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                取消
            </button>
            <div>
                <button id="previewBtn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mr-2">
                    預覽
                </button>
                <button id="saveAllBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                    批次儲存
                </button>
            </div>
        </div>

        <!-- 預覽模態框 -->
        <div id="previewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white p-6 rounded-lg w-3/4 max-h-3/4 overflow-auto">
                <h3 class="text-xl font-bold mb-4">交易預覽</h3>
                <div id="previewContent" class="mb-4">
                    <!-- 預覽內容將在JS中生成 -->
                </div>
                <div class="text-right">
                    <button id="closePreviewBtn" class="px-4 py-2 bg-gray-300 rounded">關閉</button>
                </div>
            </div>
        </div>
    </main>

    <!-- 頁面底部 -->
    <div id="footer-container"></div>


    <!-- 載入現有功能模組 -->
    <script src="transactionCreate_Service.js"></script>
    <script src="transactionCreate_JournalManagement_Module.js"></script>
    <script src="transactionCreate_PaymentDescriptionMegaMenu_Module.js"></script>
    <script src="transactionCreate_EntitySearch_Module.js"></script>

    <!-- 批次交易專用模組 -->
    <script src="transactionBatchCreate_Module.js"></script>
</body>

</html>