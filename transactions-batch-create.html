<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次交易輸入 - 財務管理系統</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 自定義樣式 -->
    <style>
        .btn-check-group input[type="radio"]:checked + label {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .common-field-checkbox:checked + label {
            background-color: #10b981;
            color: white;
        }
        
        .batch-table th, .batch-table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
        }
        
        .batch-table th {
            background-color: #f9fafb;
            font-weight: 600;
        }
        
        .error-row {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
        }
        
        .success-row {
            background-color: #f0fdf4;
            border-left: 4px solid #22c55e;
        }
    </style>

    <!-- 自定義工具庫載入 -->
    <script src="../../common/firebaseAPI/auth.js"></script> <!-- 認證相關功能 -->
    <script src="../../common/db/db.js"></script> <!-- 資料庫操作功能 -->
    <script src="../../common/db/preload.js"></script> <!-- 資料預載功能 -->
    <script src="../../common/utils/CommonUtils.js"></script> <!-- 通用工具函數 -->
    <script src="../../common/utils/pageTransfer.js"></script> <!-- 頁面轉換工具 -->
    <script src="../../common/utils/ModalUtils.js"></script> <!-- 模態框工具 -->
    <script src="../../common/utils/DatabaseErrors.js"></script> <!-- 資料庫錯誤處理 -->
</head>

<body class="bg-gray-100">
    <!-- 導航欄 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">批次交易輸入</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button type="button" onclick="handleCancel()" 
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        <i class="fas fa-arrow-left mr-2"></i>返回
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <main class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">批次交易輸入</h1>
            <p class="text-gray-600">一次性輸入多筆交易資料，提高資料輸入效率</p>
        </div>

        <!-- 共同屬性設定區域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-cog mr-2 text-blue-500"></i>共同屬性設定
                </h2>
                <button type="button" id="toggleCommonFields" onclick="toggleCommonFieldsSection()"
                    class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-chevron-up" id="toggleIcon"></i>
                </button>
            </div>
            
            <div id="commonFieldsSection" class="space-y-6">
                <!-- 欄位選擇區域 -->
                <div class="border-b pb-4">
                    <h3 class="text-lg font-medium text-gray-700 mb-3">選擇共同欄位</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                        <!-- 交易類型 -->
                        <div class="flex items-center">
                            <input type="checkbox" id="commonField_transactionType" class="common-field-checkbox sr-only" 
                                onchange="toggleCommonField('transactionType')">
                            <label for="commonField_transactionType" 
                                class="flex items-center px-3 py-2 border rounded cursor-pointer hover:bg-gray-50">
                                <i class="fas fa-exchange-alt mr-2 text-gray-500"></i>交易類型
                            </label>
                        </div>
                        
                        <!-- 我方主要帳戶 -->
                        <div class="flex items-center">
                            <input type="checkbox" id="commonField_account" class="common-field-checkbox sr-only" 
                                onchange="toggleCommonField('account')">
                            <label for="commonField_account" 
                                class="flex items-center px-3 py-2 border rounded cursor-pointer hover:bg-gray-50">
                                <i class="fas fa-university mr-2 text-gray-500"></i>主要帳戶
                            </label>
                        </div>
                        
                        <!-- 帳款到帳情形 -->
                        <div class="flex items-center">
                            <input type="checkbox" id="commonField_paymentStatus" class="common-field-checkbox sr-only" 
                                onchange="toggleCommonField('paymentStatus')">
                            <label for="commonField_paymentStatus" 
                                class="flex items-center px-3 py-2 border rounded cursor-pointer hover:bg-gray-50">
                                <i class="fas fa-clock mr-2 text-gray-500"></i>到帳情形
                            </label>
                        </div>
                        
                        <!-- 收款/付款日期 -->
                        <div class="flex items-center">
                            <input type="checkbox" id="commonField_paymentDate" class="common-field-checkbox sr-only" 
                                onchange="toggleCommonField('paymentDate')">
                            <label for="commonField_paymentDate" 
                                class="flex items-center px-3 py-2 border rounded cursor-pointer hover:bg-gray-50">
                                <i class="fas fa-calendar mr-2 text-gray-500"></i>付款日期
                            </label>
                        </div>
                        
                        <!-- 憑證/發票日期 -->
                        <div class="flex items-center">
                            <input type="checkbox" id="commonField_invoiceDate" class="common-field-checkbox sr-only" 
                                onchange="toggleCommonField('invoiceDate')">
                            <label for="commonField_invoiceDate" 
                                class="flex items-center px-3 py-2 border rounded cursor-pointer hover:bg-gray-50">
                                <i class="fas fa-receipt mr-2 text-gray-500"></i>發票日期
                            </label>
                        </div>
                        
                        <!-- 稅別 -->
                        <div class="flex items-center">
                            <input type="checkbox" id="commonField_taxType" class="common-field-checkbox sr-only" 
                                onchange="toggleCommonField('taxType')">
                            <label for="commonField_taxType" 
                                class="flex items-center px-3 py-2 border rounded cursor-pointer hover:bg-gray-50">
                                <i class="fas fa-percentage mr-2 text-gray-500"></i>稅別
                            </label>
                        </div>
                        
                        <!-- 手續費 -->
                        <div class="flex items-center">
                            <input type="checkbox" id="commonField_fee" class="common-field-checkbox sr-only" 
                                onchange="toggleCommonField('fee')">
                            <label for="commonField_fee" 
                                class="flex items-center px-3 py-2 border rounded cursor-pointer hover:bg-gray-50">
                                <i class="fas fa-coins mr-2 text-gray-500"></i>手續費
                            </label>
                        </div>
                        
                        <!-- 狀態 -->
                        <div class="flex items-center">
                            <input type="checkbox" id="commonField_status" class="common-field-checkbox sr-only" 
                                onchange="toggleCommonField('status')">
                            <label for="commonField_status" 
                                class="flex items-center px-3 py-2 border rounded cursor-pointer hover:bg-gray-50">
                                <i class="fas fa-flag mr-2 text-gray-500"></i>狀態
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 共同屬性值設定區域 -->
                <div id="commonFieldsForm" class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-700 mb-3">設定共同屬性值</h3>

                    <!-- 交易類型 -->
                    <div id="commonForm_transactionType" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">交易類型</label>
                        <div class="btn-check-group">
                            <div class="flex flex-row space-x-2">
                                <div>
                                    <input type="radio" id="common_transactionType_expense" name="common_transactionType" value="expense"
                                        class="hidden peer" checked>
                                    <label for="common_transactionType_expense"
                                        class="inline-flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-red-500 peer-checked:bg-red-400 peer-checked:border-2 peer-checked:text-white hover:border-red-200">
                                        <div class="block">
                                            <div class="w-full text-sm font-semibold">支出</div>
                                        </div>
                                    </label>
                                </div>
                                <div>
                                    <input type="radio" id="common_transactionType_income" name="common_transactionType" value="income"
                                        class="hidden peer">
                                    <label for="common_transactionType_income"
                                        class="inline-flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-blue-500 peer-checked:bg-blue-400 peer-checked:border-2 peer-checked:text-white hover:border-blue-200">
                                        <div class="block">
                                            <div class="w-full text-sm font-semibold">收入</div>
                                        </div>
                                    </label>
                                </div>
                                <div>
                                    <input type="radio" id="common_transactionType_transfer" name="common_transactionType" value="transfer"
                                        class="hidden peer">
                                    <label for="common_transactionType_transfer"
                                        class="inline-flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-green-500 peer-checked:bg-green-400 peer-checked:border-2 peer-checked:text-white hover:border-green-200">
                                        <div class="block">
                                            <div class="w-full text-sm font-semibold">轉移</div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 我方主要帳戶 -->
                    <div id="commonForm_account" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">我方主要帳戶</label>
                        <select id="common_accountSelect"
                            class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">請選擇帳戶</option>
                            <!-- 帳戶選項將從基礎資料動態載入 -->
                        </select>
                    </div>

                    <!-- 帳款到帳情形 -->
                    <div id="commonForm_paymentStatus" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">帳款到帳情形</label>
                        <select id="common_paymentStatusSelect"
                            class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">請選擇狀態</option>
                            <option value="same_day">同日收付款（現金基礎）</option>
                            <option value="receivable">應收付款（權責基礎）（已開立發票未收款）</option>
                            <option value="prepayment">暫收付款（權責基礎）（未開立發票已收款）</option>
                            <option value="different_day">非同日收款（現金基礎）(補未紀錄之已收款項)</option>
                        </select>
                    </div>

                    <!-- 收款/付款日期 -->
                    <div id="commonForm_paymentDate" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">收款/付款日期</label>
                        <input type="date" id="common_paymentDate"
                            class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>

                    <!-- 憑證/發票日期 -->
                    <div id="commonForm_invoiceDate" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">憑證/發票日期</label>
                        <input type="date" id="common_invoiceDate"
                            class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>

                    <!-- 稅別 -->
                    <div id="commonForm_taxType" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">稅別</label>
                        <select id="common_taxTypeSelect"
                            class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">請選擇稅別</option>
                            <!-- 稅別選項將從基礎資料動態載入 -->
                        </select>
                    </div>

                    <!-- 手續費 -->
                    <div id="commonForm_fee" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">手續費</label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="common_feeToggle" class="mr-2">
                                <span class="text-sm">啟用手續費</span>
                            </label>
                            <div class="flex-1">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" id="common_fee" min="0" step="0.01" placeholder="0.00" disabled
                                        class="pl-7 p-2 w-full border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 狀態 -->
                    <div id="commonForm_status" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">狀態</label>
                        <select id="common_transactionStatusSelect"
                            class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">請選擇狀態</option>
                            <option value="completed">已完成</option>
                            <option value="pending">未撥款</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批次交易資料區域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-table mr-2 text-green-500"></i>批次交易資料
                </h2>
                <div class="flex space-x-2">
                    <button type="button" onclick="addBatchRow()"
                        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        <i class="fas fa-plus mr-2"></i>新增交易
                    </button>
                    <button type="button" onclick="clearAllRows()"
                        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                        <i class="fas fa-trash mr-2"></i>清空全部
                    </button>
                </div>
            </div>

            <!-- 批次資料表格 -->
            <div class="overflow-x-auto">
                <table id="batchTransactionTable" class="batch-table w-full">
                    <thead>
                        <tr id="batchTableHeader">
                            <th class="w-12">#</th>
                            <!-- 動態欄位標題將在此插入 -->
                            <th class="w-20">操作</th>
                        </tr>
                    </thead>
                    <tbody id="batchTableBody">
                        <!-- 批次交易資料行將在此動態生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 批次操作按鈕 -->
            <div class="mt-6 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    <span>總計：</span>
                    <span id="totalRowsCount" class="font-semibold">0</span>
                    <span>筆交易</span>
                </div>
                <div class="flex space-x-3">
                    <button type="button" onclick="validateAllTransactions()"
                        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        <i class="fas fa-check-circle mr-2"></i>驗證資料
                    </button>
                    <button type="button" onclick="submitBatchTransactions()"
                        class="px-6 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 font-semibold">
                        <i class="fas fa-save mr-2"></i>批次儲存
                    </button>
                </div>
            </div>
        </div>

        <!-- 處理結果區域 -->
        <div id="resultSection" class="bg-white rounded-lg shadow-md p-6 hidden">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">
                <i class="fas fa-chart-bar mr-2 text-indigo-500"></i>處理結果
            </h2>

            <!-- 結果統計 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 text-2xl mr-3"></i>
                        <div>
                            <div class="text-sm text-green-600">成功</div>
                            <div id="successCount" class="text-2xl font-bold text-green-700">0</div>
                        </div>
                    </div>
                </div>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-times-circle text-red-500 text-2xl mr-3"></i>
                        <div>
                            <div class="text-sm text-red-600">失敗</div>
                            <div id="failureCount" class="text-2xl font-bold text-red-700">0</div>
                        </div>
                    </div>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-list text-blue-500 text-2xl mr-3"></i>
                        <div>
                            <div class="text-sm text-blue-600">總計</div>
                            <div id="totalCount" class="text-2xl font-bold text-blue-700">0</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 詳細結果列表 -->
            <div id="resultDetails" class="space-y-2">
                <!-- 詳細結果將在此動態顯示 -->
            </div>

            <!-- 結果操作按鈕 -->
            <div class="mt-6 flex justify-end space-x-3">
                <button type="button" onclick="exportResults()"
                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    <i class="fas fa-download mr-2"></i>匯出結果
                </button>
                <button type="button" onclick="resetBatchForm()"
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-redo mr-2"></i>重新開始
                </button>
            </div>
        </div>
    </main>

    <!-- 載入現有模組 -->
    <script src="transactionCreate_EntitySearch_Module.js"></script>
    <script src="transactionCreate_PaymentStatus_Module.js"></script>
    <script src="transactionCreate_TransactionType_Module.js"></script>
    <script src="transactionCreate_PaymentDescriptionMegaMenu_Module.js"></script>
    <script src="tagManager.js"></script>
    <script src="transactionCreate_JournalManagement_Module.js"></script>
    <script src="transactionCreate_Service.js"></script>
    <script src="transactionCreate_UI.js"></script>

    <!-- 批次交易專用模組 -->
    <script src="transactionsBatch_Controller.js"></script>
    <script src="transactionsBatch_UI.js"></script>
    <script src="transactionsBatch_Service.js"></script>

</body>
</html>
