/**
 * @file transactionsBatch_PaymentDescription.js
 * @description 批次交易項目選單處理模組
 * 負責處理交易項目的載入、顯示和選擇功能
 */

/**
 * 切換行交易項目下拉選單
 */
async function toggleRowPaymentDescriptionDropdown(rowIndex) {
    const dropdown = document.getElementById(`batch_paymentDescription_${rowIndex}_dropdown`);
    if (!dropdown) return;
    
    if (dropdown.classList.contains('hidden')) {
        // 載入交易項目選項
        await loadRowPaymentDescriptionOptions(rowIndex);
        dropdown.classList.remove('hidden');
    } else {
        dropdown.classList.add('hidden');
    }
}

/**
 * 載入行交易項目選項
 */
async function loadRowPaymentDescriptionOptions(rowIndex) {
    try {
        const dropdown = document.getElementById(`batch_paymentDescription_${rowIndex}_dropdown`);
        if (!dropdown) return;
        
        // 獲取當前交易類型
        const transactionTypeRadio = document.querySelector(`input[name="batch_transactionType_${rowIndex}"]:checked`);
        let transactionType = transactionTypeRadio ? transactionTypeRadio.value : 'expense';
        
        // 如果交易類型是共同欄位，從共同欄位獲取
        if (commonFields.transactionType) {
            const commonTransactionTypeRadio = document.querySelector(`input[name="common_transactionType"]:checked`);
            if (commonTransactionTypeRadio) {
                transactionType = commonTransactionTypeRadio.value;
            }
        }
        
        // 載入交易分類和項目
        const categories = await getTransactionCategoriesAll();
        const items = await getTransactionCategoryItemsAll();
        
        dropdown.innerHTML = '';
        
        // 創建搜尋框
        const searchContainer = document.createElement('div');
        searchContainer.className = 'p-2 border-b';
        
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = '搜尋交易項目...';
        searchInput.className = 'w-full p-1 text-xs border border-gray-300 rounded';
        
        searchContainer.appendChild(searchInput);
        dropdown.appendChild(searchContainer);
        
        // 創建選項容器
        const optionsContainer = document.createElement('div');
        optionsContainer.className = 'max-h-48 overflow-y-auto';
        dropdown.appendChild(optionsContainer);
        
        // 根據交易類型篩選分類
        const filteredCategories = categories.filter(cat => cat.type === transactionType);
        
        // 渲染分類和項目
        renderPaymentDescriptionOptions(optionsContainer, filteredCategories, items, rowIndex);
        
        // 添加搜尋功能
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterPaymentDescriptionOptions(optionsContainer, filteredCategories, items, searchTerm, rowIndex);
        });
        
    } catch (error) {
        console.error('載入交易項目選項失敗:', error);
    }
}

/**
 * 渲染交易項目選項
 */
function renderPaymentDescriptionOptions(container, categories, items, rowIndex, searchTerm = '') {
    container.innerHTML = '';
    
    categories.forEach(category => {
        // 獲取該分類下的項目
        const categoryItems = items.filter(item => item.categoryId === category.id);
        
        // 如果有搜尋詞，篩選項目
        const filteredItems = searchTerm ? 
            categoryItems.filter(item => 
                item.accountingName.toLowerCase().includes(searchTerm) ||
                item.accountingCode.toLowerCase().includes(searchTerm)
            ) : categoryItems;
        
        // 如果沒有符合的項目，跳過此分類
        if (filteredItems.length === 0) return;
        
        // 分類標題
        const categoryHeader = document.createElement('div');
        categoryHeader.className = 'px-3 py-2 bg-gray-100 text-xs font-medium text-gray-700 sticky top-0';
        categoryHeader.textContent = category.name;
        container.appendChild(categoryHeader);
        
        // 添加該分類下的項目
        filteredItems.forEach(item => {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer text-xs border-b border-gray-100';
            
            // 高亮搜尋詞
            let displayName = item.accountingName;
            let displayCode = item.accountingCode;
            
            if (searchTerm) {
                displayName = highlightSearchTerm(displayName, searchTerm);
                displayCode = highlightSearchTerm(displayCode, searchTerm);
            }
            
            itemDiv.innerHTML = `
                <div class="font-medium">${displayName}</div>
                <div class="text-gray-500">${displayCode}</div>
            `;
            
            itemDiv.onclick = () => selectRowPaymentDescription(rowIndex, item.accountingCode, item.accountingName);
            container.appendChild(itemDiv);
        });
    });
    
    // 如果沒有結果
    if (container.children.length === 0) {
        const noResult = document.createElement('div');
        noResult.className = 'px-3 py-4 text-xs text-gray-500 text-center';
        noResult.textContent = searchTerm ? '找不到相符的交易項目' : '沒有可用的交易項目';
        container.appendChild(noResult);
    }
}

/**
 * 篩選交易項目選項
 */
function filterPaymentDescriptionOptions(container, categories, items, searchTerm, rowIndex) {
    renderPaymentDescriptionOptions(container, categories, items, rowIndex, searchTerm);
}

/**
 * 高亮搜尋詞
 */
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
}

/**
 * 選擇行交易項目
 */
function selectRowPaymentDescription(rowIndex, code, name) {
    const button = document.getElementById(`batch_paymentDescription_${rowIndex}_btn`);
    const hiddenInput = document.getElementById(`batch_paymentDescription_${rowIndex}`);
    const dropdown = document.getElementById(`batch_paymentDescription_${rowIndex}_dropdown`);
    
    if (button && hiddenInput) {
        button.querySelector('span').textContent = name;
        button.title = `${name} (${code})`;
        hiddenInput.value = code;
    }
    
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

/**
 * 更新所有行的交易項目選單（當共同交易類型變更時）
 */
function updateAllRowsPaymentDescription() {
    const rows = document.querySelectorAll('#batchTableBody tr');
    rows.forEach(row => {
        const rowIndex = row.dataset.rowIndex;
        if (rowIndex !== undefined && !commonFields.paymentDescription) {
            // 清空當前選擇
            const button = document.getElementById(`batch_paymentDescription_${rowIndex}_btn`);
            const hiddenInput = document.getElementById(`batch_paymentDescription_${rowIndex}`);
            
            if (button && hiddenInput) {
                button.querySelector('span').textContent = '選擇交易項目';
                button.title = '';
                hiddenInput.value = '';
            }
        }
    });
}

/**
 * 創建空的交易資料物件
 */
function createEmptyTransactionData() {
    return {
        transactionType: 'expense',
        accountId: '',
        paymentStatus: '',
        paymentDate: '',
        invoiceDate: '',
        expectedPaymentDate: '',
        entityId: '',
        entityType: '',
        paymentDescription: '',
        amount: 0,
        taxTypeId: '',
        taxAmount: 0,
        invoiceNumber: '',
        fee: 0,
        transactionStatus: '',
        notes: '',
        tags: []
    };
}

// 匯出函式供其他模組使用
window.toggleRowPaymentDescriptionDropdown = toggleRowPaymentDescriptionDropdown;
window.loadRowPaymentDescriptionOptions = loadRowPaymentDescriptionOptions;
window.selectRowPaymentDescription = selectRowPaymentDescription;
window.updateAllRowsPaymentDescription = updateAllRowsPaymentDescription;
window.createEmptyTransactionData = createEmptyTransactionData;
