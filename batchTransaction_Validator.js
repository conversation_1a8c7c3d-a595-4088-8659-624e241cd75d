/**
 * @file batchTransaction_Validator.js
 * @description 批次交易資料驗證模組
 * 負責驗證批次交易資料的完整性和正確性
 */

/**
 * 批次驗證器類別
 */
class BatchValidator {
    constructor() {
        this.validationRules = this.initializeValidationRules();
    }

    /**
     * 初始化驗證規則
     * @returns {Object} 驗證規則物件
     */
    initializeValidationRules() {
        return {
            // 必填欄位
            required: [
                'entityId',
                'paymentDescription', 
                'amount'
            ],
            
            // 數值欄位
            numeric: [
                'amount',
                'taxAmount'
            ],
            
            // 最小值限制
            minValue: {
                'amount': 0.01
            },
            
            // 業務邏輯驗證
            business: [
                'validateTaxAmount',
                'validatePaymentStatus',
                'validateTransactionType'
            ]
        };
    }

    /**
     * 驗證所有交易資料
     * @returns {Array} 驗證結果陣列
     */
    async validateAll() {
        const validationResults = [];
        const rows = document.querySelectorAll('#batchTableBody tr');
        
        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const rowIndex = parseInt(row.dataset.rowIndex);
            const result = await this.validateSingleRow(rowIndex, i + 1);
            validationResults.push(result);
        }
        
        return validationResults;
    }

    /**
     * 驗證單行資料
     * @param {number} rowIndex - 行索引
     * @param {number} displayIndex - 顯示索引
     * @returns {Object} 驗證結果
     */
    async validateSingleRow(rowIndex, displayIndex) {
        const errors = [];
        const warnings = [];
        
        try {
            // 收集該行的資料
            const rowData = batchDataManager.collectRowDataFromForm(rowIndex);
            const batchSettings = batchDataManager.getBatchSettings();
            
            // 必填欄位驗證
            this.validateRequiredFields(rowData, batchSettings, errors);
            
            // 數值欄位驗證
            this.validateNumericFields(rowData, errors);
            
            // 業務邏輯驗證
            await this.validateBusinessLogic(rowData, batchSettings, errors, warnings);
            
            return {
                rowIndex,
                displayIndex,
                isValid: errors.length === 0,
                errors,
                warnings,
                data: rowData
            };
            
        } catch (error) {
            console.error(`驗證第 ${displayIndex} 行時發生錯誤:`, error);
            return {
                rowIndex,
                displayIndex,
                isValid: false,
                errors: ['資料驗證時發生系統錯誤'],
                warnings: [],
                data: null
            };
        }
    }

    /**
     * 驗證必填欄位
     * @param {Object} rowData - 行資料
     * @param {Object} batchSettings - 批次設定
     * @param {Array} errors - 錯誤陣列
     */
    validateRequiredFields(rowData, batchSettings, errors) {
        // 檢查交易對象
        if (!rowData.entityId) {
            errors.push('交易對象為必填');
        }
        
        // 檢查交易項目
        if (!rowData.paymentDescription) {
            errors.push('交易項目為必填');
        }
        
        // 檢查金額
        if (!rowData.amount || rowData.amount <= 0) {
            errors.push('金額必須大於0');
        }
        
        // 檢查批次設定
        if (!batchSettings.accountId) {
            errors.push('請選擇主要帳戶');
        }
        
        if (!batchSettings.paymentStatus) {
            errors.push('請選擇到帳情形');
        }
        
        if (!batchSettings.taxTypeId) {
            errors.push('請選擇稅別');
        }
        
        if (!batchSettings.transactionStatus) {
            errors.push('請選擇狀態');
        }
    }

    /**
     * 驗證數值欄位
     * @param {Object} rowData - 行資料
     * @param {Array} errors - 錯誤陣列
     */
    validateNumericFields(rowData, errors) {
        // 檢查金額格式
        if (isNaN(rowData.amount) || rowData.amount < 0) {
            errors.push('金額格式不正確');
        }
        
        // 檢查稅額格式
        if (rowData.taxAmount && (isNaN(rowData.taxAmount) || rowData.taxAmount < 0)) {
            errors.push('稅額格式不正確');
        }
    }

    /**
     * 驗證業務邏輯
     * @param {Object} rowData - 行資料
     * @param {Object} batchSettings - 批次設定
     * @param {Array} errors - 錯誤陣列
     * @param {Array} warnings - 警告陣列
     */
    async validateBusinessLogic(rowData, batchSettings, errors, warnings) {
        // 驗證稅額計算
        await this.validateTaxAmount(rowData, batchSettings, warnings);
        
        // 驗證交易對象存在性
        await this.validateEntityExists(rowData, errors);
        
        // 驗證交易項目存在性
        await this.validatePaymentDescriptionExists(rowData, errors);
    }

    /**
     * 驗證稅額計算
     * @param {Object} rowData - 行資料
     * @param {Object} batchSettings - 批次設定
     * @param {Array} warnings - 警告陣列
     */
    async validateTaxAmount(rowData, batchSettings, warnings) {
        if (rowData.amount > 0 && batchSettings.taxTypeId) {
            try {
                const taxTypes = await getTaxTypesRatesAll();
                const taxType = taxTypes.find(t => t.id == batchSettings.taxTypeId);
                
                if (taxType && taxType.rate) {
                    const expectedAmountNoTax = Math.round(rowData.amount / (1 + Number(taxType.rate)));
                    const expectedTaxAmount = rowData.amount - expectedAmountNoTax;
                    const actualTaxAmount = rowData.taxAmount || 0;
                    
                    if (Math.abs(expectedTaxAmount - actualTaxAmount) > 0.01) {
                        warnings.push(`稅額可能不正確，預期: ${expectedTaxAmount.toFixed(2)}，實際: ${actualTaxAmount.toFixed(2)}`);
                    }
                }
            } catch (error) {
                console.warn('驗證稅額時發生錯誤:', error);
            }
        }
    }

    /**
     * 驗證交易對象存在性
     * @param {Object} rowData - 行資料
     * @param {Array} errors - 錯誤陣列
     */
    async validateEntityExists(rowData, errors) {
        if (rowData.entityId && rowData.entityType) {
            try {
                let entityExists = false;
                
                if (rowData.entityType === 'employee') {
                    const employees = await getEmployeesAll();
                    entityExists = employees.some(emp => emp.id == rowData.entityId);
                } else {
                    const entities = await getEntitiesAll();
                    entityExists = entities.some(entity => entity.id == rowData.entityId);
                }
                
                if (!entityExists) {
                    errors.push('選擇的交易對象不存在');
                }
            } catch (error) {
                console.warn('驗證交易對象時發生錯誤:', error);
            }
        }
    }

    /**
     * 驗證交易項目存在性
     * @param {Object} rowData - 行資料
     * @param {Array} errors - 錯誤陣列
     */
    async validatePaymentDescriptionExists(rowData, errors) {
        if (rowData.paymentDescription) {
            try {
                const items = await getTransactionCategoryItemsAll();
                const itemExists = items.some(item => item.accountingCode == rowData.paymentDescription);
                
                if (!itemExists) {
                    errors.push('選擇的交易項目不存在');
                }
            } catch (error) {
                console.warn('驗證交易項目時發生錯誤:', error);
            }
        }
    }

    /**
     * 顯示驗證結果
     * @param {Array} results - 驗證結果陣列
     */
    displayValidationResults(results) {
        const validCount = results.filter(r => r.isValid).length;
        const invalidCount = results.length - validCount;
        
        // 更新表格行的樣式
        results.forEach(result => {
            const row = document.getElementById(`batchRow_${result.rowIndex}`);
            if (row) {
                row.classList.remove('error-row', 'success-row');
                
                if (result.isValid) {
                    row.classList.add('success-row');
                } else {
                    row.classList.add('error-row');
                    
                    // 標記錯誤的輸入欄位
                    this.markErrorFields(result.rowIndex, result.errors);
                }
                
                // 設定提示訊息
                if (result.errors.length > 0) {
                    row.title = '錯誤: ' + result.errors.join(', ');
                } else if (result.warnings.length > 0) {
                    row.title = '警告: ' + result.warnings.join(', ');
                } else {
                    row.title = '';
                }
            }
        });
        
        // 顯示總結訊息
        if (invalidCount > 0) {
            alert(`驗證完成！\n有效資料: ${validCount} 筆\n無效資料: ${invalidCount} 筆\n\n請檢查標示為紅色的行並修正錯誤。`);
        } else {
            alert(`驗證完成！所有 ${validCount} 筆資料都有效，可以進行批次儲存。`);
        }
    }

    /**
     * 標記錯誤欄位
     * @param {number} rowIndex - 行索引
     * @param {Array} errors - 錯誤陣列
     */
    markErrorFields(rowIndex, errors) {
        // 清除之前的錯誤標記
        const row = document.getElementById(`batchRow_${rowIndex}`);
        if (row) {
            const inputs = row.querySelectorAll('.batch-input');
            inputs.forEach(input => input.classList.remove('error-input'));
        }
        
        // 根據錯誤類型標記對應欄位
        errors.forEach(error => {
            if (error.includes('交易對象')) {
                const input = document.getElementById(`entity_${rowIndex}`);
                if (input) input.classList.add('error-input');
            }
            
            if (error.includes('交易項目')) {
                const button = document.getElementById(`paymentDescription_${rowIndex}_btn`);
                if (button) button.classList.add('error-input');
            }
            
            if (error.includes('金額')) {
                const input = document.getElementById(`amount_${rowIndex}`);
                if (input) input.classList.add('error-input');
            }
        });
    }
}

/**
 * 顯示驗證結果（全域函式）
 * @param {Array} results - 驗證結果陣列
 */
function displayValidationResults(results) {
    if (batchValidator) {
        batchValidator.displayValidationResults(results);
    }
}
