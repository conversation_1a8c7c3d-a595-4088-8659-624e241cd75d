<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重複欄位修正驗證</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🔧 重複欄位修正驗證</h1>
        
        <!-- 問題說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-red-600">❌ 發現的問題</h2>
            <div class="space-y-4">
                <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h3 class="font-medium text-red-800 mb-2">重複欄位問題</h3>
                    <p class="text-red-700 text-sm mb-3">
                        在兩列式佈局中，某些欄位可能在上下列中重複出現，導致表格混亂和功能異常。
                    </p>
                    <div class="text-xs text-red-600">
                        <strong>根本原因：</strong>第1616行的欄位陣列生成邏輯有重複添加的問題
                    </div>
                </div>
                
                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h3 class="font-medium text-yellow-800 mb-2">原始問題代碼</h3>
                    <pre class="text-xs text-yellow-700 bg-white p-2 rounded border overflow-x-auto"><code>// 問題代碼：重複添加欄位
individualPropertyFields = [
    'amount', 'taxAmount', 'invoiceNumber', 'fee', 'tags', 
    ...individualFields,  // individualFields 已經包含了上述欄位
    'notes'
];</code></pre>
                </div>
            </div>
        </div>
        
        <!-- 修正方案 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-green-600">✅ 修正方案</h2>
            <div class="space-y-4">
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-medium text-green-800 mb-2">使用 Set 去重</h3>
                    <pre class="text-sm text-green-700 bg-white p-3 rounded border overflow-x-auto"><code>// 修正後：使用 Set 去重
individualPropertyFields = [...new Set(individualFields)];</code></pre>
                    <p class="text-green-700 text-sm mt-2">
                        使用 JavaScript 的 Set 資料結構自動去除重複欄位，確保每個欄位只出現一次。
                    </p>
                </div>
                
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 class="font-medium text-blue-800 mb-2">修正邏輯說明</h3>
                    <ol class="text-blue-700 text-sm space-y-1">
                        <li>1. <code>allFields</code> 包含所有可用欄位</li>
                        <li>2. 未勾選為共同屬性的欄位被添加到 <code>individualFields</code></li>
                        <li>3. 日期群組欄位根據條件添加到 <code>individualFields</code></li>
                        <li>4. 使用 <code>Set</code> 去除 <code>individualFields</code> 中的重複項</li>
                        <li>5. 生成去重後的 <code>individualPropertyFields</code></li>
                    </ol>
                </div>
            </div>
        </div>
        
        <!-- 測試指引 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibent mb-4">🧪 測試指引</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 1: 預設狀態檢查</h3>
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <ol class="text-sm text-blue-700 space-y-1">
                            <li>1. 開啟批次交易頁面</li>
                            <li>2. 檢查表格標題的兩列佈局</li>
                            <li>3. 確認第一列和第二列沒有重複欄位</li>
                            <li>4. 記錄所有顯示的欄位名稱</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 2: 動態變更測試</h3>
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <ol class="text-sm text-green-700 space-y-1">
                            <li>1. 勾選/取消不同的共同屬性</li>
                            <li>2. 觀察表格重建後的欄位分配</li>
                            <li>3. 確認沒有欄位在上下列重複</li>
                            <li>4. 測試多種欄位組合</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 3: 控制台驗證</h3>
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <ol class="text-sm text-yellow-700 space-y-1">
                            <li>1. 按F12開啟開發者工具</li>
                            <li>2. 執行欄位檢查命令</li>
                            <li>3. 驗證沒有重複欄位</li>
                            <li>4. 檢查陣列長度是否正確</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 4: 功能完整性</h3>
                    <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <ol class="text-sm text-purple-700 space-y-1">
                            <li>1. 測試每個欄位的輸入功能</li>
                            <li>2. 確認特殊功能正常（標籤、搜尋等）</li>
                            <li>3. 驗證事件監聽器正確綁定</li>
                            <li>4. 測試表格的增刪功能</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 驗證工具 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">🛠️ 驗證工具</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    開啟批次頁面
                </button>
                <button onclick="showValidationCommands()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    <i class="fas fa-code mr-2"></i>
                    驗證命令
                </button>
                <button onclick="showExpectedResults()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    <i class="fas fa-check-circle mr-2"></i>
                    預期結果
                </button>
                <button onclick="openDebugTool()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    <i class="fas fa-bug mr-2"></i>
                    調試工具
                </button>
            </div>
            
            <!-- 驗證命令 -->
            <div id="validationCommands" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg hidden">
                <h3 class="font-medium text-green-800 mb-3">💻 在批次頁面控制台中執行</h3>
                <div class="space-y-2">
                    <div class="p-2 bg-white border rounded">
                        <code class="text-sm text-gray-700">
                            // 檢查重複欄位<br>
                            const first = window.tableFieldsLayout?.firstRowFields || [];<br>
                            const second = window.tableFieldsLayout?.secondRowFields || [];<br>
                            const duplicates = first.filter(field => second.includes(field));<br>
                            console.log('重複欄位:', duplicates);<br>
                            console.log('重複數量:', duplicates.length);
                        </code>
                    </div>
                    <div class="p-2 bg-white border rounded">
                        <code class="text-sm text-gray-700">
                            // 檢查欄位總數<br>
                            const allFields = [...first, ...second];<br>
                            const uniqueFields = [...new Set(allFields)];<br>
                            console.log('總欄位數:', allFields.length);<br>
                            console.log('唯一欄位數:', uniqueFields.length);<br>
                            console.log('是否有重複:', allFields.length !== uniqueFields.length);
                        </code>
                    </div>
                </div>
            </div>
            
            <!-- 預期結果 -->
            <div id="expectedResults" class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg hidden">
                <h3 class="font-medium text-yellow-800 mb-3">🎯 預期結果</h3>
                <ul class="text-sm text-yellow-700 space-y-1">
                    <li>• <strong>重複欄位數量:</strong> 0</li>
                    <li>• <strong>總欄位數 = 唯一欄位數:</strong> true</li>
                    <li>• <strong>第一列和第二列:</strong> 沒有交集</li>
                    <li>• <strong>所有欄位功能:</strong> 正常運作</li>
                    <li>• <strong>表格佈局:</strong> 整齊美觀</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function openBatchPage() {
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function showValidationCommands() {
            const element = document.getElementById('validationCommands');
            element.classList.toggle('hidden');
        }
        
        function showExpectedResults() {
            const element = document.getElementById('expectedResults');
            element.classList.toggle('hidden');
        }
        
        function openDebugTool() {
            window.open('debug-field-layout.html', '_blank');
        }
        
        // 頁面載入時顯示修正完成訊息
        document.addEventListener('DOMContentLoaded', function() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>重複欄位問題已修正完成！</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 5000);
        });
    </script>
</body>
</html>
