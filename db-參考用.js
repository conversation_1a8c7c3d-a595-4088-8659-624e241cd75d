//import { DatabaseError, ValidationError, NotFoundError, PermissionError, DuplicateError, Error<PERSON>andler } from '../utils/DatabaseErrors.js';

// 資料庫名稱和版本
const DB_NAME = 'YIXIN_financeDB_OFFLINE';
const DB_VERSION = 3;



// 資料表名稱
const STORE_NAMES = {
    accountTypes: 'accountTypes',
    accountingItems: 'accountingItems',
    accounts: 'accounts',
    advances: 'advances',
    advanceDetails: 'advanceDetails',
    advance_types: 'advance_types',
    banks: 'banks',
    departments: 'departments',
    employees: 'employees',
    entities: 'entities',
    entity_subtypes: 'entity_subtypes',
    entity_types: 'entity_types',
    paymentMethods: 'paymentMethods',
    positions: 'positions',
    projectBudgetCosts: 'projectBudgetCosts',
    projectContractPayments: 'projectContractPayments',
    projectTags: 'projectTags',
    projects: 'projects',
    projectTasks: 'projectTasks',
    salaries: 'salaries',
    salaryDetails: 'salaryDetails',
    salaryTypes: 'salaryTypes',
    supplier_types: 'supplier_types',
    taxTypesRates: 'taxTypesRates',
    transactionCategories: 'transactionCategories',
    transactionCategoryItems: 'transactionCategoryItems',
    transactionDetails: 'transactionDetails',
    transactions: 'transactions',
    transactionTags: 'transactionTags',
    assets: 'assets', // 新增資產資料表
    assetDisposals: 'assetDisposals', // 新增資產處分資料表
    journalEntries: 'journalEntries', // 新增會計分錄資料表
    companySettings: 'companySettings', // 新增公司設定資料表
    fireStore_syncQueue: 'fireStore_syncQueue' // 新增同步佇列資料表
};

// 資料表設定
const STORE_CONFIG = {
    accountTypes: { keyPath: 'id', autoIncrement: true },
    accountingItems: { keyPath: 'id', autoIncrement: true },
    accounts: { keyPath: 'id', autoIncrement: true },
    advances: { keyPath: 'id', autoIncrement: true },
    advanceDetails: { keyPath: 'id', autoIncrement: true },
    advance_types: { keyPath: 'id', autoIncrement: true },
    banks: { keyPath: 'id', autoIncrement: true },
    departments: { keyPath: 'id', autoIncrement: true },
    employees: { keyPath: 'id', autoIncrement: true },
    entities: { keyPath: 'id', autoIncrement: true },
    entity_subtypes: { keyPath: 'id', autoIncrement: true },
    entity_types: { keyPath: 'id', autoIncrement: true },
    paymentMethods: { keyPath: 'id', autoIncrement: true },
    positions: { keyPath: 'id', autoIncrement: true },
    projectBudgetCosts: { keyPath: 'id', autoIncrement: true },
    projectContractPayments: { keyPath: 'id', autoIncrement: true },
    projectTags: { keyPath: 'id', autoIncrement: true },
    projects: { keyPath: 'id', autoIncrement: true },
    projectTasks: { keyPath: 'id', autoIncrement: true },
    salaries: { keyPath: 'id', autoIncrement: true },
    salaryDetails: { keyPath: 'id', autoIncrement: true },
    salaryTypes: { keyPath: 'id', autoIncrement: true },
    supplier_types: { keyPath: 'id', autoIncrement: true },
    taxTypesRates: { keyPath: 'id', autoIncrement: true },
    transactionCategories: { keyPath: 'id', autoIncrement: true },
    transactionCategoryItems: { keyPath: 'id', autoIncrement: true },
    transactionDetails: { keyPath: 'id', autoIncrement: true },
    transactions: { keyPath: 'id', autoIncrement: true },
    transactionTags: { keyPath: 'id', autoIncrement: true },
    assets: { keyPath: 'id', autoIncrement: true }, // 新增資產資料表設定
    assetDisposals: { keyPath: 'id', autoIncrement: true }, // 新增資產處分資料表設定
    journalEntries: { keyPath: 'id', autoIncrement: true }, // 新增會計分錄資料表設定
    companySettings: { keyPath: 'id', autoIncrement: false }, // 新增公司設定資料表設定
    fireStore_syncQueue: { keyPath: 'id', autoIncrement: true } // 新增同步佇列資料表設定
};

// 定義資料表標題
const STORE_TITLES = {
    accountTypes: '帳戶類型',
    accountingItems: '會計項目代號表',
    accounts: '帳戶資料',
    advances: '代墊主表',
    advanceDetails: '代墊明細',
    advance_types: '代墊類型',
    banks: '銀行資料',
    departments: '部門資料',
    employees: '員工資料',
    entities: '交易對象資料',
    entity_subtypes: '交易對象細分類型',
    entity_types: '交易對象類型',//客戶、供應商、員工、其他
    paymentMethods: '支付方式',
    positions: '職位資料',
    projectBudgetCosts: '專案預算成本',
    projectContractPayments: '專案合約付款',
    projectTags: '專案標籤關聯',
    projects: '專案資料',
    projectTasks: '專案任務',
    salaries: '薪資發放',
    salaryDetails: '薪資明細',
    salaryTypes: '薪資類型',
    supplier_types: '供應商類型',
    taxTypesRates: '稅別資料',
    transactionCategories: '交易分類項',
    transactionCategoryItems: '分類項目表',
    transactionDetails: '交易明細表',
    transactions: '交易記錄',
    transactionTags: '交易標籤',
    assets: '資產資料', // 新增資產資料表標題
    assetDisposals: '資產處分資料', // 新增資產處分資料表標題
    journalEntries: '會計分錄', // 新增會計分錄資料表標題
    companySettings: '公司設定', // 新增公司設定資料表標題
    fireStore_syncQueue: '同步佇列' // 新增同步佇列資料表標題
};

// 定義資料表欄位，提供給settings.html使用，或其他頁面查詢時使用
// 定義資料表欄位
const STORE_FIELDS = {
    "accountTypes": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "帳戶類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "accountingItems": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "code",label: "會計項目代碼",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "name",label: "會計項目名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "category",label: "會計項目分類",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "type",label: "會計項目類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "accounts": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "帳戶名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "bankCode",label: "銀行代碼",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "branchCode",label: "分行代號",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountNumber",label: "帳號",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountTypeId",label: "帳戶類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "balance",label: "餘額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "advances": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "advanceNo",label: "代墊單號",type: "text",required: true,isIndex: true,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeId",label: "員工ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "typeId",label: "代墊類型ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "status",label: "狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "applyDate",label: "申請日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "approvedAt",label: "核准日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "transactionId",label: "交易ID",type: "text",required: false,isIndex: true,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false}
    ],

    "advanceDetails": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "advanceId",label: "代墊ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "item",label: "項目",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "advance_types": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "代墊類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "banks": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "code",label: "銀行代碼",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "name",label: "銀行名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "departments": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "部門名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "employees": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "empNo",label: "員工編號",type: "text",required: true,isIndex: true,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "姓名",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "email",label: "電子郵件",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "phone",label: "電話",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "address",label: "地址",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 2,defaultDisplay: false},
        { key: "departmentId",label: "部門ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "positionId",label: "職位ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "idNumber",label: "身分證字號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "salary",label: "薪資",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "s",fieldWidth: 1,defaultDisplay: true},
        { key: "supervisorAllowance",label: "主管津貼",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "s",fieldWidth: 1,defaultDisplay: false},
        { key: "mealAllowance",label: "餐費津貼",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "s",fieldWidth: 1,defaultDisplay: false},
        { key: "companyLaborInsurance",label: "公司負擔勞保費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "c",fieldWidth: 1,defaultDisplay: false},
        { key: "companyHealthInsurance",label: "公司負擔健保費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "c",fieldWidth: 1,defaultDisplay: false},
        { key: "companyRetirementInsurance",label: "公司負擔勞退費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "c",fieldWidth: 1,defaultDisplay: false},
        { key: "employeeHealthInsurance",label: "員工負擔健保費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "e",fieldWidth: 1,defaultDisplay: false},
        { key: "employeeLaborInsurance",label: "員工負擔勞保費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "e",fieldWidth: 1,defaultDisplay: false},
        { key: "employeeRetirementInsurance",label: "員工負擔勞退費(自提繳)",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "e",fieldWidth: 1,defaultDisplay: false},
        { key: "startDate",label: "到職日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "resignationDate",label: "離職日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "status",label: "在職狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "createdAt",label: "建立時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "updatedAt",label: "更新時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false}
    ],

    "entities": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "code",label: "編號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "type",label: "對象類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "subtypeId",label: "細分類型",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taxId",label: "統一編號",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "contactPerson",label: "聯絡人",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "phone",label: "電話",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "email",label: "電子郵件",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "address",label: "地址",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "updatedAt",label: "更新時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "entity_subtypes": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "類型名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "parentType",label: "父類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "entity_types": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "類型名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "type",label: "類型代號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "類型描述",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "paymentMethods": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "支付方式",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "positions": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "職位名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projectBudgetCosts": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectId",label: "專案ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "costItem",label: "成本項目",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "budgetAmount",label: "預算金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "actualCost",label: "實際成本",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projectContractPayments": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectId",label: "專案ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "totalContractValue",label: "合約總價",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "numberOfInstallments",label: "分期期數",type: "number",required: true,isIndex: true,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "installmentAmount",label: "每期金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "installmentDueDate",label: "每期應付日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "installmentPaidDate",label: "每期實際付款日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "installmentStatus",label: "每期付款狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "contractDate",label: "簽訂日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "contractNumber",label: "合約編號",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projects": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectNumber",label: "專案編號",type: "text",required: true,isIndex: true,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "name",label: "專案名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "clientId",label: "客戶ID",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "status",label: "狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "startDate",label: "開始日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "expectedEndDate",label: "預計完成日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "專案描述",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "date",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "updatedAt",label: "更新時間",type: "date",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projectTags": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectId",label: "專案ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "tagId",label: "標籤ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "projectTasks": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "projectId",label: "專案ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskName",label: "任務名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskDescription",label: "任務描述",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskStatus",label: "任務狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskStartDate",label: "任務開始日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskEndDate",label: "任務結束日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taskProgress",label: "任務進度",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "salaries": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "periodId", label: "期間ID", type: "text", required: true, isIndex: true, unique: true ,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},// 自定義一個好查詢的ID，例如 "2023-11"
        { key: "salaryPeriodsStart",label: "薪資期間開始",type: "date",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "salaryPeriodsEnd",label: "薪資期間結束",type: "date",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "totalAmount",label: "總金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "batchPaid", label: "已批次支付", type: "boolean", isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}, // V4 新增欄位
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "transactionId",label: "交易ID",type: "text",required: false,isIndex: true,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false}
    ],

    "salaryDetails": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "salariesId",label: "薪資單ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeId",label: "員工ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeName",label: "員工姓名",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeNo",label: "員工編號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "basicSalary",label: "基本薪資",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "overtimePayment",label: "加班費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "leaveDeduction",label: "請假扣項",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "bonus",label: "獎金",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "supervisorAllowance",label: "主管津貼",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "mealAllowance",label: "餐費津貼",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "companyLaborInsurance",label: "公司負擔勞保費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "company",fieldWidth: 1,defaultDisplay: true},
        { key: "companyHealthInsurance",label: "公司負擔健保費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "company",fieldWidth: 1,defaultDisplay: true},
        { key: "companyRetirementInsurance",label: "公司負擔勞退費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "company",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeHealthInsurance",label: "員工負擔健保費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "employee",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeLaborInsurance",label: "員工負擔勞保費",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "employee",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeRetirementInsurance",label: "員工負擔勞退費(自提繳)",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "employee",fieldWidth: 1,defaultDisplay: true},
        { key: "addItems",label: "薪資加項總額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "deductItems",label: "薪資扣項總額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "actualPayment",label: "實發薪資",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "companyBurden",label: "公司負擔總額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "employeeBurden",label: "員工負擔總額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "status",label: "薪資狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "paymentDate",label: "發放日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "transactionId",label: "交易ID",type: "text",required: false,isIndex: true,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false}

    ],

    "salaryTypes": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "薪資類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "supplier_types": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "供應商類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "taxTypesRates": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "稅別名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "rate",label: "稅率",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "說明",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactionCategories": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "分類名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "type",label: "類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactionCategoryItems": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "categoryId",label: "分類ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountingCode",label: "會計項目代碼",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountingName",label: "會計項目名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactionDetails": [
        { key: "id",label: "ID",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "transactionId",label: "交易ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "itemNo",label: "項目編號",type: "text",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "item",label: "項目名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "price",label: "單價",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "quantity",label: "數量",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "unit",label: "單位",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "total",label: "複價",type: "number",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactions": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "invoiceDate",label: "憑證/發票日期",type: "date",required: false,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "paymentDate",label: "收款/付款日期",type: "date",required: false,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "expectedPaymentDate",label: "預計收/付款日期",type: "date",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "transactionType",label: "交易類型",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "paymentDescription",label: "交易描述(會計代號)",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "entityId",label: "交易對象ID",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "entityType",label: "交易對象類型",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taxTypeId",label: "稅別ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "invoiceNumber",label: "發票號碼",type: "text",required: false,isIndex: false,unique: true,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "taxAmount",label: "稅額",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "fee",label: "手續費",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "notes",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "tags",label: "標籤",type: "array",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountId",label: "帳戶ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "paymentStatus",label: "帳款到帳情形",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "transactionStatus",label: "交易狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "date",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "transactionTags": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "標籤名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "date",required: true,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "assets": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "name",label: "資產名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "category",label: "資產類別",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "purchaseDate",label: "購買日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "purchasePrice",label: "購買價格",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "currentValue",label: "現值",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "status",label: "狀態",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}
    ],

    "assetDisposals": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "assetId",label: "資產ID",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "disposalDate",label: "處分日期",type: "date",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "disposalMethod",label: "處分方式",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "amount",label: "處分金額",type: "number",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "note",label: "備註",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true}    
    ],

    "journalEntries": [
        { key: "id",label: "代碼",type: "text",required: true,isIndex: false,unique: true,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false},
        { key: "transactionId",label: "交易ID",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "entryType",label: "分錄類型",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountCode",label: "會計科目代號",type: "text",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "accountName",label: "會計科目名稱",type: "text",required: true,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "debitAmount",label: "借方金額",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "creditAmount",label: "貸方金額",type: "number",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "entryDate",label: "分錄日期",type: "date",required: true,isIndex: true,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "description",label: "摘要",type: "text",required: false,isIndex: false,unique: false,isInputRequired: true,group: "none",fieldWidth: 1,defaultDisplay: true},
        { key: "createdAt",label: "建立時間",type: "text",required: false,isIndex: false,unique: false,isInputRequired: false,group: "none",fieldWidth: 1,defaultDisplay: false}
    ],

    "companySettings": [
        { key: "id", label: "ID", type: "text", required: true, isIndex: false, unique: true, isInputRequired: false, group: "none", fieldWidth: 1, defaultDisplay: false },
        { key: "companyName", label: "公司名稱", type: "text", required: true, isIndex: false, unique: false, isInputRequired: true, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "logo", label: "公司LOGO(Base64)", type: "text", required: false, isIndex: false, unique: false, isInputRequired: false, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "updatedAt", label: "更新時間", type: "text", required: false, isIndex: false, unique: false, isInputRequired: false, group: "none", fieldWidth: 1, defaultDisplay: true }
    ],
    "fireStore_syncQueue": [
        { key: "id", label: "ID", type: "text", required: true, isIndex: false, unique: true, isInputRequired: false, group: "none", fieldWidth: 1, defaultDisplay: false },
        { key: "action", label: "操作", type: "text", required: true, isIndex: false, unique: false, isInputRequired: true, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "collection", label: "集合路徑", type: "text", required: true, isIndex: false, unique: false, isInputRequired: true, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "docId", label: "文件ID", type: "text", required: true, isIndex: false, unique: false, isInputRequired: true, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "companyId", label: "公司ID", type: "text", required: true, isIndex: false, unique: false, isInputRequired: true, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "actionTime", label: "操作時間", type: "text", required: true, isIndex: false, unique: false, isInputRequired: true, group: "none", fieldWidth: 1, defaultDisplay: true },
        { key: "data", label: "資料", type: "array", required: false, isIndex: false, unique: false, isInputRequired: true, group: "none", fieldWidth: 1, defaultDisplay: true }
    ]
};


const STORE_relationTable = [
    {
        id: 1,
        sourceStoreName: 'tableA',
        sourceKeyPath: 'id',
        relatedStoreName: 'tableB',
        relatedForeignKeyPath: 'a_id',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    },
    {
        id: 2,
        sourceStoreName: 'tableB',
        sourceKeyPath: 'id',
        relatedStoreName: 'tableC',
        relatedForeignKeyPath: 'b_id',
        onDelete: 'RESTRICT',
        onUpdate: 'DO_NOTHING'
    },
    // 潛在的循環關聯 (tableC 關聯回 tableA - 不良設計，我們需要檢查出來)
    {
        id: 3,
        sourceStoreName: 'tableC',
        sourceKeyPath: 'id',
        relatedStoreName: 'tableA',
        relatedForeignKeyPath: 'c_related_a_id',
        onDelete: 'DO_NOTHING',
        onUpdate: 'DO_NOTHING'
    }
];


//----------------------------------------------------------------資料表的定義檢查(僅在開發時使用)----------------------------------------------------------------
/**
* -------------------------------------------------------------------------------------
* 測試STORE_FIELDS.STORE_TITLES.STORE_CONFIG.STORE_NAMES各自缺少項目 store
* 使用STORE_NAMES作為標準。
* -------------------------------------------------------------------------------------
**/
/***以下未使用時進行註解。***/
/*
//先測試STORE_FIELDS
let missingStores = [];
let dataFields = Object.keys(STORE_FIELDS);
for (const storeName in STORE_NAMES) {
    if (!dataFields.includes(storeName)) {
        missingStores.push(storeName);
    }

}
console.log(`STORE_FIELDS缺少的資料表：`, missingStores);

//測試STORE_TITLES
missingStores = [];
dataFields = Object.keys(STORE_TITLES);
for (const storeName in STORE_NAMES) {
    if (!dataFields.includes(storeName)) {
        missingStores.push(storeName);
    }

}
console.log(`STORE_TITLES缺少的資料表：`, missingStores);

//測試STORE_CONFIG
missingStores = [];
dataFields = Object.keys(STORE_CONFIG);
for (const storeName in STORE_NAMES) {
    if (!dataFields.includes(storeName)) {
        missingStores.push(storeName);
    }
}
console.log(`STORE_CONFIG缺少的資料表：`, missingStores);


//測試各自項目數量
console.log(`STORE_NAMES的項目數量(標準)：`, Object.keys(STORE_NAMES).length);
console.log(`STORE_FIELDS的項目數量：`, Object.keys(STORE_FIELDS).length);
console.log(`STORE_TITLES的項目數量：`, Object.keys(STORE_TITLES).length);
console.log(`STORE_CONFIG的項目數量：`, Object.keys(STORE_CONFIG).length);

//先測試STORE_NAMES，使用STORE_FIELDS作為標準。
missingStores = [];
dataFields = Object.keys(STORE_NAMES);
for (const storeName in STORE_FIELDS) {
    if (!dataFields.includes(storeName)) {
        missingStores.push(storeName);
    }
}
console.log(`STORE_NAMES缺少的資料表：`, missingStores);

const mergedArray = [...new Set([...Object.keys(STORE_NAMES), ...Object.keys(STORE_FIELDS), ...Object.keys(STORE_TITLES), ...Object.keys(STORE_CONFIG)])];
console.log(`合併後的資料表數量：`, mergedArray.length);
console.log(`合併後的資料表：`, mergedArray);
*/
/***以上未使用時進行註解。***/

//----------------------------------------------------------------資料庫初始化----------------------------------------------------------------
/**
 * @description 用於初始化資料庫，第一次執行時，建立所有資料表和索引，及後續進行操作前的資料庫連線。
 **/

// 初始化資料庫
const initDB = () => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(DB_NAME, DB_VERSION);

        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);

        request.onupgradeneeded = (event) => {
            const db = event.target.result;

            // 建立所有資料表
            Object.entries(STORE_NAMES).forEach(([key, storeName]) => {
                if (!db.objectStoreNames.contains(storeName)) {
                    const store = db.createObjectStore(storeName, STORE_CONFIG[storeName]);

                    // 為不同資料表建立適當的索引
                    console.log(storeName);
                    STORE_FIELDS[storeName].forEach(field => {
                        if (field.isIndex) {
                            store.createIndex(field.key, field.key, { unique: field.unique });
                        }
                    });
                    // console.log(`已建立資料表：${storeName}`);
                }else{
                    console.log(`資料表已存在：${storeName}`);
                    
                }

            });
        };
    });
}


//----------------------------------------------------------------資料表欄位檢核相關----------------------------------------------------------------


/**
 * @description 用於表格資料存入資料庫時，進行欄位檢核。(處理單筆)
 * @param {object} data 要存入的資料
 * @param {string} storeName 資料表名稱
 * @param {boolean} isCheckRequired 是否檢查必填欄位
 * @param {boolean} isCorrectData 是否修正資料格式
 * @returns {object} 回傳檢核結果
 */ 
function checkSingleDataFields(data, storeName, isCheckRequired = true, isCorrectData = false) {
    const storeFields = STORE_FIELDS[storeName];
    let usedNonDefinedFields = [];//Data物件中 提交了 但資料庫未定義 的欄位
    let nonDefinedFields;//data物件中 未提交 但資料庫有定義 的欄位
    let missingFields;//必填欄位 未填寫或為空值
    let typeErrorFields;//欄位類型錯誤
    let errorMessage;//錯誤訊息
    let resultError;//回傳結果
    
    if (isCorrectData) {
        //console.log("處理前", data);
        data = correctData(data, storeName);
        //console.log("處理後", data);
    }
    

    const dataFields = Object.keys(data);
    if (isCheckRequired) {
        nonDefinedFields = storeFields.filter(field => !dataFields.includes(field.key));
        dataFields.forEach(field => {
            if (!storeFields.find(f => f.key === field)) {
                usedNonDefinedFields.push(field);
            }
        });
        missingFields = storeFields.filter(field => field.required && (!data[field.key] && data[field.key] !== 0));
    } else {
        nonDefinedFields = [];
        usedNonDefinedFields = [];
        missingFields = [];
    }


    typeErrorFields = storeFields.filter(field => {
        if (data[field.key] === null ||
            data[field.key] === undefined ||
            data[field.key] === ''
        ) {
            return false;
        }
        if (field.type === 'number') {
            //return isNaN(Number(data[field.key]));
            return typeof data[field.key] !== 'number';
        } else if (field.type === 'text') {
            return typeof data[field.key] !== 'string';
        } else if (field.type === 'date') {
            return isNaN(new Date(data[field.key]).getTime());
        }
    });

    if (missingFields.length > 0) {
        errorMessage = '必填欄位未填寫';
    } else if (typeErrorFields.length > 0) {
        errorMessage = '欄位類型錯誤';
    } else if (missingFields.length > 0 && typeErrorFields.length > 0) {
        errorMessage = '必填欄位未填寫或欄位類型錯誤';
    } else {
        errorMessage = '無錯誤';
    }

    resultError = {
        usedNonDefinedFields: usedNonDefinedFields,
        nonDefinedFields: nonDefinedFields,
        missingFields: missingFields,
        typeErrorFields: typeErrorFields,
        message: errorMessage
    }
    return resultError;
}

/**
 * @description 用於表格資料存入資料庫時，進行欄位檢核。(處理多筆)
 * @param {array} data 要存入的資料
 * @param {string} storeName 資料表名稱
 * @param {boolean} isCheckRequired 是否檢查必填欄位
 * @param {boolean} isCorrectData 是否修正資料格式
 * @returns {boolean} 回傳檢核結果
 */
function checkDataFields(data, storeName, isCheckRequired = true, isCorrectData = false) {
    //判斷是否為陣列，如果是陣列，則表示為多筆資料，需逐一檢查
    if (Array.isArray(data)) {
        let resultError = [];
        data.forEach(item => {
            resultError.push(checkSingleDataFields(item, storeName, isCheckRequired, isCorrectData));
        });
        console.log(resultError);
        //如果其中有任一筆資料有錯誤，則回傳false
        let hasError = false;
        resultError.forEach(item => {
            const checkDataFieldsResult = item;
            if (checkDataFieldsResult.missingFields.length > 0 ||
                checkDataFieldsResult.typeErrorFields.length > 0 ||
                checkDataFieldsResult.usedNonDefinedFields.length > 0 ||
                checkDataFieldsResult.nonDefinedFields.length > 0) {
                hasError = true;
            }
        });

        return hasError;
    } else {
        let resultError=checkSingleDataFields(data, storeName, isCheckRequired, isCorrectData);
        console.log(resultError);
        if (resultError.missingFields.length > 0 ||
            resultError.typeErrorFields.length > 0 ||
            resultError.usedNonDefinedFields.length > 0 ||
            resultError.nonDefinedFields.length > 0) {
            return false;
        }else{
            return true;
        }
    }
}


/**
 * @description 用於表格資料存入資料庫時，進行資料格式校正。
 * @param {object|array} data 要存入的資料
 * @param {string} storeName 資料表名稱
 * @returns {object|array} 回傳校正後的資料
 */
function correctData(data, storeName) {
    const storeFields = STORE_FIELDS[storeName];

    storeFields.forEach(field => {
        if (data[field.key] === null || data[field.key] === undefined ||
            (typeof data[field.key] === "number" && isNaN(data[field.key]))) {
            if (Array.isArray(data)) {
                data.push({
                    [field.key]: ''
                });
            } else if (typeof data === 'object') {
                data[field.key] = '';
            }
        }

        if (field.type === 'number') {
            data[field.key] = Number(data[field.key]);
        } else if (field.type === 'text') {
            data[field.key] = String(data[field.key]);
        } else if (field.type === 'date') {
            if (data[field.key] === '') {
                data[field.key] = '';
            } else {
                data[field.key] = new Date(data[field.key]).toISOString().split('T')[0];
            }
        }
    });
    return data;
}

//----------------------------------------------------------------資料庫操作相關----------------------------------------------------------------
//--------------------------------資料庫開啟事務(transaction)--------------------------------
/**
 * @description 用於資料庫操作前進行，開啟事務(transaction)
 * @param {string} storeName - 事務涉及一個資料表名稱。
 * @param {'readonly' | 'readwrite'} mode - 事務模式。
 * @param {function(IDBObjectStore, function, function): void} action - 執行的動作。此回呼接收 (store, resolve, reject) 作為參數。
 * @returns {Promise<any>} - 一個 Promise，它會解析出 action 中 resolve 的值。
 */
async function executeTransaction(storeName, mode, action) {
    const db = await initDB();
    if (!db.objectStoreNames.contains(storeName)) {
        throw new Error(`Store ${storeName} does not exist`);
    }
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, mode);
        const store = transaction.objectStore(storeName);

        transaction.onerror = () => reject(transaction.error);
        transaction.onabort = () => reject(new Error('Transaction was aborted'));
        // transaction.oncomplete 在這種模式下將由 action 的 Promise 決定 resolve
        // transaction.oncomplete = () => resolve(); // 不再在這裡直接 resolve
        
        //執行動作
        action(store, resolve, reject);
    });
}
//--------------------------------資料庫通用的查詢函數--------------------------------
/**
 * @description 通用的 IndexedDB 查詢函數，支援索引、範圍、排序和客戶端過濾。
 * @param {string} storeName - 要查詢的資料表名稱。
 * @param {object} [options={}] - 查詢選項。
 * @param {string} [options.index] - 要使用的索引名稱。如果未提供，則在主鍵上查詢。
 * @param {IDBKeyRange} [options.range] - 要查詢的範圍 (使用 IDBKeyRange)。如果未提供，則查詢所有資料。
 * @param {'next'|'prev'|'nextunique'|'prevunique'} [options.direction='next'] - 游標移動方向。 
 * 'next' (預設值): 升序。從最小的鍵開始，按順序讀取到最大的鍵。
 * 'prev' (previous): 降序。從最大的鍵開始，按順序讀取到最小的鍵。
 * 'nextunique': 升序且唯一。與 'next' 類似，但如果遇到重複的鍵（在索引上），它只會回傳第一個匹配的記錄，然後跳到下一個唯一的鍵。
 * 'prevunique': 降序且唯一。與 'prev' 類似，但會跳過重複的鍵。
 * @param {function(object):boolean} [options.filter] - 對每筆結果進行過濾的客戶端回呼函數。
 * filter 是一個非常有用的參數，但需要謹慎使用。它是一個您提供的 JavaScript 函數，該函數會對 range 和 index 查詢出的每一筆結果執行一次。
 * 用途：用於處理 IDBKeyRange 無法實現的複雜過濾邏輯。
 * 運作方式：filter 函數會接收一筆記錄（record）作為參數，您必須在函數中回傳 true（保留這筆記錄）或 false（丟棄這筆記錄）。
 * 效能警告：filter 是在 客戶端（JavaScript 環境中）執行的。資料庫會先把符合 range 的資料一筆一筆傳給 JavaScript，然後您的 filter 函數再進行判斷。這遠比使用 index 和 range 進行資料庫層級的過濾要慢。因此，應優先使用 index 和 range 縮小範圍，再用 filter 進行精細過濾。
 * @returns {Promise<Array<object>>} - 回傳一個包含查詢結果陣列的 Promise。
 */
async function query(storeName, options = {}) {
    // 預設選項
    const {
        index,
        range = null,
        direction = 'next',
        filter
    } = options;

    const results = await executeTransaction(storeName, 'readonly', (store, resolve, reject) => {
        const localResults = []; // 在事務內部宣告結果陣列，避免外部作用域汙染

        // 決定是在索引上查詢還是在主儲存空間上查詢
        const source = index ? store.index(index) : store;
        const request = source.openCursor(range, direction);

        request.onerror = (event) => {
            reject(event.target.error);// 不再需要手動 console.error，讓 executeTransaction 統一處理
        };

        request.onsuccess = (event) => {
            const cursor = event.target.result;
            if (cursor) {
                const record = cursor.value;

                // 如果有提供 filter 函數，則執行客戶端過濾
                if (filter) {
                    if (filter(record)) {
                        localResults.push(record);
                    }
                } else {
                    // 如果沒有 filter，直接加入結果
                    localResults.push(record);
                }
                cursor.continue();
            } else {
                // 游標遍歷完成
                resolve(localResults);
            }
        };
    });
    return results;
}
/**
 * 以下為通用查詢之範例
 * 
// 範例一：取得七月份的所有交易紀錄
async function getJulyTransactions() {
    try {
        // 建立一個包含 2025-07-01 到 2025-07-31 的日期範圍
        const range = IDBKeyRange.bound(
            "2025-07-01", 
            "2025-07-31"
        );

        const transactions = await query('transactions', {
            index: 'invoiceDate', // 使用發票日期索引
            range: range
        });

        console.log('七月份的交易紀錄:', transactions);
        return transactions;
    } catch (error) {
        console.error('查詢失敗:', error);
    }
}
// 範例二：取得最新的 5 位員工
// 情境：按員工編號 (empNo) 降序排列，取得最新的員工資料。 (注意：此處未實作 limit，但可以手動 slice 結果)
async function getLatestEmployees() {
    try {
        const employees = await query('employees', {
            index: 'empNo', // 使用員工編號索引
            direction: 'prev' // 降序排列
        });

        console.log('最新的 5 位員工:', employees.slice(0, 5)); // 手動取前 5 筆
        return employees.slice(0, 5);
    } catch (error) {
        console.error('查詢失敗:', error);
    }
}

// 範例三：取得所有重要的供應商，查詢特定類型且備註包含關鍵字的交易對象（使用 range 和 filter）
// 情境：找出所有類型為 "供應商" (entity_types 的某個 id)，且備註 (notes) 欄位中包含 "長期合作" 字樣的交易對象。假設 notes 欄位沒有建立索引。
async function findImportantSuppliers() {
    // 假設 'supplier_type_id' 是從 entity_types 表查到的供應商 ID
    const supplierTypeId = 'supplier_type_id'; 
    try {
        const suppliers = await query('entities', {
            index: 'type', // 假設 'type' 欄位有索引
            range: IDBKeyRange.only(supplierTypeId), // 1. 先用索引和範圍高效過濾出所有供應商
            filter: (record) => { // 2. 再用 filter 函數對結果進行精細過濾
                return record.note && record.note.includes('長期合作');
            }
        });

        console.log('重要的長期合作供應商:', suppliers);
        return suppliers;
    } catch (error) {
        console.error('查詢失敗:', error);
    }
}

// 範例四：取得所有使用過的會計科目代號(使用 direction: 'nextunique'）
// 情境：需要一個不重複的會計科目代號列表，用於報表或下拉選單。
async function getUniqueAccountCodes() {
    try {
        // journalEntries 表中包含了所有分錄，每筆分錄都有 accountCode
        const entries = await query('journalEntries', {
            index: 'accountCode', // 使用會計科目代號索引
            direction: 'nextunique' // 升序且只取唯一值
        });

        // entries 現在是一個每個 accountCode 只出現一次的記錄列表
        const uniqueCodes = entries.map(entry => entry.accountCode);

        console.log('所有使用過的會計科目代號:', uniqueCodes);
        return uniqueCodes;
    } catch (error) {
        console.error('查詢失敗:', error);
    }
}
 *   
 * 以上為通用查詢之範例
 */

//--------------------------------取得資料--------------------------------

// 獲取實體類型標籤
function getEntityTypeLabel(type) {
    const typeLabels = {
        'account': '帳戶',
        'client': '客戶',
        'supplier': '供應商',
        'temporary': '臨時對象',
        'employee': '員工',
    };
    return typeLabels[type] || type;
}

/**
 * @description 取得所有資料
 * @param {string} storeName 資料表名稱
 * @param {string} indexName 排序索引名稱
 * @param {string} sort 排序方式，"next" 為升序，"prev" 為降序
 * @returns {array} 回傳所有資料
 */
async function getAllData(storeName, indexName = null, sort = 'next') {
    let result = await executeTransaction(storeName, 'readonly', (store, resolve, reject) => {
        let request;
        if (indexName) {
            const index = store.index(indexName);
            request = index.openCursor(null, sort);
            const results = [];
            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    results.push(cursor.value);
                    cursor.continue();
                } else {
                    resolve(results);
                }
            };
        } else {
            request = store.getAll();
            request.onsuccess = () => resolve(request.result);
        }

        request.onerror = () => reject(request.error);
    });
    return result;
}


/**
 * @description 根據 ID 獲取單筆資料
 * @param {string} storeName 資料表名稱
 * @param {string} id 資料ID
 * @returns {object} 回傳資料
 */
async function getDataById(storeName, id) {
    let result = await executeTransaction(storeName, 'readonly', (store, resolve, reject) => {
        const getRequest = store.get(String(id));
        getRequest.onsuccess = () => resolve(getRequest.result);
        getRequest.onerror = () => reject(getRequest.error);
    });
    return result;
}

/**
 * @description 根據 Index索引欄位、其索引值 獲取單一或多筆資料
 * @param {string} storeName 資料表名稱
 * @param {string} field 索引欄位名稱
 * @param {any} value 索引值
 * @returns {array} 回傳資料
 */
async function getDataByIndex(storeName, field, value) {
    const result = await query(storeName, {
        index: field,
        range: IDBKeyRange.only(value)
    });
    return result;
}

// 根據 索引值範圍 獲取單一或多筆資料，*暫時保留* 需修改成通用的查詢函數
// (目前使用的有財務總覽financialOverview.js、報表reports.js、現金流量表cashFlowStatement.js、損益表profitLossStatement.js、薪資發放salary_payment.HTML)
async function getDataByDateRange(storeName, field, startDate, endDate) {
    const result = await query(storeName, {
        index: field,
        range: IDBKeyRange.bound(startDate, endDate)
    });
    return result;
}

//--------------------------------更新資料--------------------------------

/**
 * @description 更新整筆資料
 * @param {string} storeName 資料表名稱
 * @param {object} updatedData 要更新的資料(須包含id欄位)
 * @returns {object} 回傳更新後的資料
 */
async function updateData(storeName, updatedData) {
    
    // 將更新資料的ID取出，以便獲取舊資料進行合併更新。
    const { id, ...updatedDataWithoutId } = updatedData;
    const result = await executeTransaction(storeName, 'readwrite', (store, resolve, reject) => {
        // 先獲取現有資料
        const getRequest = store.get(String(id));
        getRequest.onerror = () => {
            ErrorHandler(getRequest.error,' (db.js)updateData獲取失敗', true);
            reject(getRequest.error);
        };
        getRequest.onsuccess = () => {
            const existingData = getRequest.result;
            if (!existingData) {
                reject(new NotFoundError('找不到指定的資料'));
                return;
            }

            // 合併現有資料和更新資料
            const updatedData = {
                ...existingData,
                ...updatedDataWithoutId
            };

            //檢查資料欄位是否符合資料庫欄位，不進行修正。(開發時使用)
            const hasError = checkDataFields(updatedData, storeName, true, false);
            //不檢查資料欄位是否符合資料庫欄位，進行修正。(發布後使用)
            //const hasError = checkDataFields(updatedData, storeName,false,true);
            if (!hasError) {
                console.log('資料欄位有誤，無法更新資料');
                return null;
            }

            // 更新資料
            const updateRequest = store.put(updatedData);

            updateRequest.onerror = () => {
                ErrorHandler(updateRequest.error,' (db.js)updateData更新失敗', true);
                reject(updateRequest.error);
            };

            updateRequest.onsuccess = () => {
                resolve(updateRequest.result);
            };
        };
    });
    return result;
}

// 
/**
 * @description 
 * 更新關聯表的資料，
 * 操作上無法直接覆蓋更新原資料，而是須先刪除原有資料，再新增新資料
 * @param {string} storeName 為操作之表格
 * @param {string} relationField 關聯的欄位
 * @param {array<object>} updatedDatas 更新後的資料 
 * @returns 
 */
async function updateRelationData(storeName,relationField ,updatedDatas) {
    const db = await initDB();
    return new Promise(async (resolve, reject) => {
        try {
            //刪除原有資料
            await deleteDataByIndex(storeName, relationField, updatedDatas[0][relationField]);
            //新增新資料
            await addMultipleData(storeName, updatedDatas);
            resolve();
        } catch (error) {
            reject(error);
        }
    });
        
}

//--------------------------------新增資料--------------------------------

//整合新增資料，可新增單筆或多筆資料
async function addEntries(storeName, datas, options = { isAutoId: true, isDevCheck: false }) {
    const db = await initDB();
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);

    const { isAutoId, isDevCheck } = options;

    // 將單筆資料轉換為多筆陣列，方便處理
    const entries = Array.isArray(datas) ? datas : [datas];

    // 處理每個資料，移除 id 欄位，讓 IndexedDB 自動產生
    const savingEntries = entries.map(data => {
        const { id, ...dataWithoutId } = data;

        let entry = dataWithoutId;
        if (isAutoId) {
            entry = {
                id: self.crypto.randomUUID(),
                ...dataWithoutId
            };
        } else if (data.id) {
            entry = {
                id: data.id,
                ...dataWithoutId
            };
        }else{
            //如果不指定AutoId，且未提供id，則視為使用IndexedDB自動產生ID
            //不傳入id，讓IndexedDB自動產生
            entry = dataWithoutId;
        }

        return entry;
    });

    //檢查資料欄位是否符合資料庫欄位，不進行修正。(開發時使用)
    //const hasError = checkDataFields(savingDatas, storeName, true, false);
    //不檢查資料欄位是否符合資料庫欄位，進行修正。(發布後使用)
    //const hasError = checkDataFields(savingDatas, storeName,false,true);
    //透過傳入參數，來決定是否進行檢查與修正
    const hasError = checkDataFields( savingEntries,storeName,isDevCheck,!isDevCheck);

    if (hasError) {
        console.log('資料欄位有誤，無法新增資料');
        return null;
    }

    const addPromises = savingEntries.map(data => {
        return new Promise((resolve, reject) => {
            const request = store.add(data);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    });

    return Promise.all(addPromises)
        .then(results => (Array.isArray(datas) ? results : results[0]))
        .catch(error => {
            console.error('新增資料失敗:', error);
            throw error;
        });
}

// 新增多筆資料，**暫時保留，用於向下相容**
async function addMultipleData(storeName, datas) {
    return addEntries(storeName, datas, { isAutoId: true, isDevCheck: false });
}

// 新增單一資料，**暫時保留，用於向下相容**
async function addData(storeName, data,isAutoId = true) {
    return addEntries(storeName, data, { isAutoId: isAutoId, isDevCheck: false });
}

//--------------------------------刪除資料--------------------------------

// 刪除資料
async function deleteData(storeName, id) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.delete(id);

        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
    });
}

//使用索引刪除資料，目前主要為關聯表使用(projectTags、projectTasks、projectBudgetsCosts、projectPayments)
async function deleteDataByIndex(storeName, indexName, value) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const index = store.index(indexName);
        const request = index.openCursor(IDBKeyRange.only(value));
        let deleteCount = 0;
        request.onsuccess = (event) => {
            const cursor = event.target.result;
            deleteCount = deleteCount || 0;
            if (cursor) {
                cursor.delete();
                deleteCount++;
                cursor.continue();
            } else {
                resolve(deleteCount);
            }
        };
        request.onerror = () => reject(request.error);
    });
}



//--------------------------------專案標籤(新增、更新、刪除、查詢)--------------------------------


// 更新專案標籤
async function updateProjectTags(projectID, newTags) {
    const db = await initDB();
    const transaction = db.transaction(['projectTags'], 'readwrite');
    const store = transaction.objectStore('projectTags');
    // 1. 刪除現有標籤
    // 假設你已經為 projectID 創建了一個索引，名稱為 projectId
    const index = store.index('projectId');
    const deleteRequests = [];
    return new Promise((resolve, reject) => {
        index.openCursor(IDBKeyRange.only(projectID)).onsuccess = (cursorEvent) => {
            const cursor = cursorEvent.target.result;
            if (cursor) {
                deleteRequests.push(cursor.delete()); // 刪除當前記錄
                cursor.continue();
            } else {
                // 游標遍歷完成，等待所有刪除請求完成
                Promise.all(deleteRequests)
                    .then(() => {
                        // 2. 新增新的標籤
                        const addRequests = [];
                        newTags.forEach(tag => {
                            addRequests.push(
                                store.add({
                                    projectId: projectID,
                                    tagId: tag
                                })
                            );
                        });
                        return Promise.all(addRequests);
                    })
                    .then(() => {
                        // 事務完成
                        transaction.oncomplete = () => {
                            console.log(`專案 ${projectID} 的標籤已更新。`);
                            resolve();
                        };
                        transaction.onerror = (txError) => {
                            console.error('事務錯誤:', txError.target.errorCode);
                            reject(txError.target.errorCode);
                        };
                        transaction.onabort = () => {
                            console.warn('事務已中止');
                            reject('Transaction aborted');
                        };
                    })
                    .catch(addError => {
                        console.error('新增標籤失敗:', addError);
                        transaction.abort(); // 出錯時中止事務
                        reject(addError);
                    });
            }
        };
        index.openCursor(IDBKeyRange.only(projectID)).onerror = (error) => {
            console.error('開啟游標錯誤:', error);
            reject(error);
        };
    });
}

//--------------------------------資產管理(新增、更新、刪除、查詢)--------------------------------

// 資產管理相關函數
async function saveAsset(assetData) {
    try {
        const db = await initDB();
        const tx = db.transaction(STORE_NAMES.assets, 'readwrite');
        const store = tx.objectStore(STORE_NAMES.assets);
        
        // 確保有ID
        if (!assetData.id) {
            assetData.id = self.crypto.randomUUID();
        }
        
        await store.put(assetData);
        await tx.complete;
        return true;
    } catch (error) {
        console.error('儲存資產失敗:', error);
        throw error;
    }
}

async function updateAsset(assetData) {
    try {
        const db = await initDB();
        const tx = db.transaction(STORE_NAMES.assets, 'readwrite');
        const store = tx.objectStore(STORE_NAMES.assets);
        
        if (!assetData.id) {
            throw new Error('更新資產時需要ID');
        }
        
        assetData.updatedAt = new Date().toISOString();
        await store.put(assetData);
        await tx.complete;
        return true;
    } catch (error) {
        console.error('更新資產失敗:', error);
        throw error;
    }
}

async function deleteAsset(id) {
    try {
        const db = await initDB();
        const tx = db.transaction(STORE_NAMES.assets, 'readwrite');
        const store = tx.objectStore(STORE_NAMES.assets);
        
        await store.delete(id);
        await tx.complete;
        return true;
    } catch (error) {
        console.error('刪除資產失敗:', error);
        throw error;
    }
}

async function getAssetById(id) {
    try {
        const db =await initDB();
        const tx = db.transaction(STORE_NAMES.assets, 'readonly');
        const store = tx.objectStore(STORE_NAMES.assets);
        
        const asset = await store.get(id);
        return asset;
    } catch (error) {
        console.error('獲取資產失敗:', error);
        throw error;
    }
}

async function saveAssetDisposal(disposalData) {
    try {
        const db = await initDB();
        const tx = db.transaction(STORE_NAMES.assetDisposals, 'readwrite');
        const store = tx.objectStore(STORE_NAMES.assetDisposals);
        
        if (!disposalData.id) {
            disposalData.id = self.crypto.randomUUID();
        }
        
        await store.put(disposalData);
        await tx.complete;
        return true;
    } catch (error) {
        console.error('儲存資產處分記錄失敗:', error);
        throw error;
    }
}

// 更新資產折舊
async function updateAssetDepreciation(assetId, depreciationAmount) {
    try {
        const asset = await getAssetById(assetId);
        if (!asset) {
            throw new Error('找不到資產');
        }

        asset.accumulatedDepreciation = (asset.accumulatedDepreciation || 0) + depreciationAmount;
        asset.updatedAt = new Date().toISOString();

        await updateAsset(asset);
        return true;
    } catch (error) {
        console.error('更新資產折舊失敗:', error);
        throw error;
    }
}

async function saveAssetDepreciation(assetId, depreciationData) {
    const collection = await getCollection('assetDepreciation');
    return await collection.updateOne(
        { assetId },
        { 
            $set: {
                method: depreciationData.method,
                schedule: depreciationData.schedule,
                totalProductionUnits: depreciationData.totalProductionUnits,
                lastUpdated: new Date()
            }
        },
        { upsert: true }
    );
}

async function getAssetDepreciation(assetId) {
    const collection = await getCollection('assetDepreciation');
    return await collection.findOne({ assetId });
}


//--------------------------------會計分錄(新增、更新、刪除、查詢)--------------------------------

// 儲存會計分錄
async function saveJournalEntries(transactionId, journalData) {
    try {
        const db = await initDB();
        const transaction = db.transaction(STORE_NAMES.journalEntries, 'readwrite');
        const store = transaction.objectStore(STORE_NAMES.journalEntries);

        const entries = [];
        const currentTime = new Date().toISOString();

        // 處理借方分錄
        if (journalData.debit && journalData.debit.length > 0) {
            journalData.debit.forEach(debitEntry => {
                entries.push({
                    id: self.crypto.randomUUID(),
                    transactionId: transactionId,
                    entryType: 'debit',
                    accountCode: Number(debitEntry.code),
                    accountName: debitEntry.name,
                    debitAmount: debitEntry.amount,
                    creditAmount: 0,
                    entryDate: debitEntry.date,
                    description: `${journalData.debitEntity || ''} - ${debitEntry.name}`,
                    createdAt: currentTime
                });
            });
        }

        // 處理貸方分錄
        if (journalData.credit && journalData.credit.length > 0) {
            journalData.credit.forEach(creditEntry => {
                entries.push({
                    id: self.crypto.randomUUID(),
                    transactionId: transactionId,
                    entryType: 'credit',
                    accountCode: creditEntry.code,
                    accountName: creditEntry.name,
                    debitAmount: 0,
                    creditAmount: creditEntry.amount,
                    entryDate: creditEntry.date,
                    description: `${journalData.creditEntity || ''} - ${creditEntry.name}`,
                    createdAt: currentTime
                });
            });
        }

        // 批量儲存分錄
        const savePromises = entries.map(entry => {
            return new Promise((resolve, reject) => {
                const request = store.add(entry);
                request.onsuccess = async () => {
                    resolve(request.result);
                    //console.log('已儲存會計分錄');

                    // 加入同步佇列
                    //await addToQueue(await packageQueueItems('add', request.result,'companyId_01', STORE_NAMES.journalEntries, entry));
                    
                }
                request.onerror = () => reject(request.error);
            });
        });

        await Promise.all(savePromises);
        console.log('已儲存會計分錄');
        return entries.length;
    } catch (error) {
        console.error('儲存會計分錄失敗:', error);
        throw error;
    }
}

// 根據交易ID獲取會計分錄
async function getJournalEntriesByTransactionId(transactionId) {
    try {
        const db = await initDB();
        const transaction = db.transaction(STORE_NAMES.journalEntries, 'readonly');
        const store = transaction.objectStore(STORE_NAMES.journalEntries);
        const index = store.index('transactionId');

        return new Promise((resolve, reject) => {
            const request = index.getAll(IDBKeyRange.only(transactionId));
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    } catch (error) {
        console.error('獲取會計分錄失敗:', error);
        throw error;
    }
}




/***************處理同步佇列*********************/

async function addToQueue(item) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAMES.fireStore_syncQueue], 'readwrite');
        const store = transaction.objectStore(STORE_NAMES.fireStore_syncQueue);
        const request = store.add(item);

        request.onsuccess = () => {
            if ('serviceWorker' in navigator && navigator.serviceWorker.ready) {
                navigator.serviceWorker.ready.then(registration => {
                    registration.active.postMessage({ type: 'SYNC_QUEUE' });
                    console.log('Sync message sent to active SW.');
                });
            }
            resolve(request.result);
        };
        request.onerror = (event) => reject('Error adding to queue: ' + event.target.errorCode);
    });
}

async function getQueue() {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAMES.fireStore_syncQueue], 'readonly');
        const store = transaction.objectStore(STORE_NAMES.fireStore_syncQueue);
        const request = store.getAll();

        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => reject('Error getting queue: ' + event.target.errorCode);
    });
}

async function deleteFromQueue(id) {
    const db = await initDB();
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([STORE_NAMES.fireStore_syncQueue], 'readwrite');
        const store = transaction.objectStore(STORE_NAMES.fireStore_syncQueue);
        const request = store.delete(id);

        request.onsuccess = () => resolve();
        request.onerror = (event) => reject('Error deleting from queue: ' + event.target.errorCode);
    });
}

//打包佇列資料
async function packageQueueItems(action,docId,companyId, storeName, data) {
    const collection = `companies/${companyId}/${storeName}`;
    const queue = {
        action: action,
        collection: collection,
        docId: docId,
        companyId: companyId,
        actionTime: new Date().toISOString(),
        data: data
    };
    return queue;
}

// 新增資料
async function sync_addData(storeName, data,isAutoId = true) {
    const db = await initDB();
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const IS_UUID = true;//開發時使用，來指定ID欄位是否使用UUID，或由IndexedDB資料庫自動產生。

    let savingData;

    // 移除 id 欄位，讓 IndexedDB 自動產生
    const { id, ...dataWithoutId } = data;

    if (IS_UUID) {
        // 為ID欄位生成UUID
        savingData = {
            id: self.crypto.randomUUID(),
            ...dataWithoutId
        }
    } else {
        savingData = dataWithoutId;
    }

    //檢查資料欄位是否符合資料庫欄位，不進行修正。(開發時使用)
    const hasError = checkDataFields(savingData, storeName, true, false);
    //不檢查資料欄位是否符合資料庫欄位，進行修正。(發布後使用)
    //const hasError = checkDataFields(savingData, storeName,false,true);

    if (!hasError) {
        console.log('資料欄位有誤，無法新增資料');
        return null;
    }

    if(!isAutoId){
        savingData.id = data.id;
    }

    //console.log(savingData);
    return new Promise((resolve, reject) => {
        const request = store.add(savingData);

        request.onsuccess = () => {
            resolve(request.result); // 解析 Promise 並回傳 ID
        };
        request.onerror = () => reject(request.error);
    });
}




//--------------------------------資料庫備份功能--------------------------------

// 備份資料庫
async function backupDatabase() {
    const db = await initDB();
    const data = {};

    // 取得所有資料表的資料
    for (const storeName of Object.values(STORE_NAMES)) {
        const store = db.transaction(storeName, 'readonly').objectStore(storeName);
        const request = store.getAll();

        data[storeName] = await new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // 建立下載檔案
    const date = new Date().toISOString().split('T')[0];
    const filename = `finance_data_${date}.json`;
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });

    return { blob, filename };
}

// 匯出資料
async function exportData() {
    const db = await initDB();
    const data = {};

    // 只匯出基礎資料相關的資料表
    const basicStores = [
        'banks',
        'accountTypes',
        'departments',
        'positions',
        'paymentMethods',
        'taxTypesRates',
    ];

    for (const storeName of basicStores) {
        const store = db.transaction(storeName, 'readonly').objectStore(storeName);
        const request = store.getAll();

        await new Promise((resolve, reject) => {
            request.onsuccess = () => {
                data[storeName] = request.result;
                resolve();
            };
            request.onerror = () => reject(request.error);
        });
    }

    return JSON.stringify(data, null, 2);
}

// 匯入資料
async function importData(data) {
    const db = await initDB();

    // 為每個資料表建立獨立的交易
    for (const [storeName, items] of Object.entries(data)) {
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);

        // 清空現有資料
        await store.clear();

        // 新增匯入的資料
        for (const item of items) {
            await store.add(item);
        }

        // 等待交易完成
        await new Promise((resolve, reject) => {
            transaction.oncomplete = () => resolve();
            transaction.onerror = () => reject(transaction.error);
        });
    }
}






//--------------------------------資料表讀取及過濾功能--------------------------------

//----尚未實作----尚未實作----尚未實作----尚未實作----尚未實作
async function getStoreFields(storeName) {
    return STORE_FIELDS[storeName];
}

async function getStoreData(storeName, getFields = null, filter = null) {

}
//----尚未實作----尚未實作----尚未實作----尚未實作----尚未實作----尚未實作


//-------------------------------------------------------------------------------------


// 確保函數可以在瀏覽器和Node.js環境中使用
if (typeof window !== 'undefined') {
    window.initDB = initDB;
}
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initDB };
}