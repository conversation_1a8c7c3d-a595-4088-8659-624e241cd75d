/**
 * @file transactionCreate_LogicManager.js
 * @description 此檔案負責提供模組所需的後端業務邏輯。
 * - 它處理從 IndexedDB 中獲取原始資料（如員工、對象）。
 * - 它提供計算統計數據的函式，供控制器 (Controller) 和其他模組使用。
 * - 作為一個邏輯層，它將資料獲取和處理與 UI 渲染分離開來。
 */


/**
 * @description 透過 pageTransfer 及 url 判斷，取得目前模式
 * @returns {string} 目前模式，Create(新增) / Edit(編輯) / Advance(處理代墊款轉單) / Salary(處理薪資資料)
 */
async function getCurrentMode() {

    //先檢查是否有由pageTransfer 傳入的資料
    const transferData = await pageTransfer.getPageTransferData('transaction-create');
    if (transferData) {
        //如果有，則可能為「薪資」或「代墊款」轉單，需取得資料後並設定目前模式
        if (transferData.type === 'advance') {
            currentMode = 'Advance';//由代墊款核銷轉單
        }else if (transferData.type === 'salary') {
            currentMode = 'Salary';//由薪資轉單
        }
        return {
            currentMode,
            transferData,
            editId: null
        };
    } else {
        // 如果沒有 transferData，檢查 URL 參數
        const urlParams = new URLSearchParams(window.location.search);
        const editId = urlParams.get('id');
        // 如果有id，則為編輯模式，否則為一般新增模式
        if (editId) {
            currentMode = 'Edit';
        }else{
            currentMode = 'Create';
        }
        return {
            currentMode,
            transferData: {},
            editId
        };
    }
}

// 處理一般新增交易單-預設填入
async function handleDefaultCreate() {
    const {taxTypeIdSelect,accountIdSelect,invoiceNumberContainer}=getTransactionFormCard_DOMElements();
    // --- 初始化日期 ---
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('paymentDate').value = today;
    document.getElementById('invoiceDate').value = today;

    //預設稅別為營業稅
    taxTypeIdSelect.selectedIndex = 1;

    //開啟發票號碼輸入欄位
    invoiceNumberContainer.classList.remove('hidden');

    //預設帳款到帳情況為同日收付款
    setPaymentStatusSetting('same_day');

    //判斷選單中是否有選項，預設由localStorage取得主要帳戶ID,如果無則使用 第一個帳戶為主要帳戶
    if(accountIdSelect.options.length > 0){
        const primaryAccountId = localStorage.getItem('YIXIN_financeDB_primaryAccountId');
        if(primaryAccountId){
            accountIdSelect.value = primaryAccountId;
        }else{
            accountIdSelect.selectedIndex = 1;
        }
    }
}


//  處理編輯交易單-預設填入
async function handleEditTransaction(transactionId) {
    try {
        // 載入交易資料
        
        const transaction = await getTransactionById(transactionId);
        if (!transaction) return;

        // 重置明細區域
        resetDetailSection();

        // 載入明細資料
        const details = await getTransactionDetailByTransactionId(transactionId);
        
        // 填充表單資料
        const form = document.querySelector('form');
        const expectedPaymentDateContainer = document.getElementById('expectedPaymentDateContainer');

        // 設置帳款到帳情形
        setPaymentStatusSetting(transaction.paymentStatus);

        // 設置發票日期、收付款日期、預計收付款日期
        form.elements['invoiceDate'].value = transaction.invoiceDate;
        form.elements['paymentDate'].value = transaction.paymentDate;
        form.elements['expectedPaymentDate'].value = transaction.expectedPaymentDate || '';


        document.querySelector(`input[name="transactionType"][value="${transaction.transactionType}"]`).checked = true;
        // 執行交易類型變化的處理
        handleTransactionTypeChange(transaction.transactionType === 'transfer');

        // 在處理完交易類型變化後重新渲染[交易項目選單]
        await renderPaymentDescriptionMegaMenu(transaction.transactionType);
        form.elements['paymentDescription'].value = transaction.paymentDescription;


        //如果有手續費，則顯示手續費輸入欄位
        if(transaction.fee == 0){
            document.getElementById('feeToggle').checked = false;
            form.elements['fee'].value = '';
        }else{
            document.getElementById('feeToggle').checked = true;
            document.getElementById('feeInputContainer').classList.remove('hidden');
            form.elements['fee'].value = transaction.fee;
        }
        
        //如果有發票號碼，則顯示發票號碼輸入欄位
        if(transaction.invoiceNumber){
            document.getElementById('invoiceNumberContainer').classList.remove('hidden');
            form.elements['invoiceNumber'].value = transaction.invoiceNumber || '';
        }else{
            document.getElementById('invoiceNumberContainer').classList.add('hidden');
        }

        form.elements['selectedEntityId'].value = transaction.entityId;
        form.elements['selectedEntityType'].value = transaction.entityType;
        form.elements['amount'].value = transaction.amount;
        form.elements['taxTypeSelect'].value = transaction.taxTypeId;
        form.elements['taxAmount'].value = transaction.taxAmount || '';
        form.elements['notes'].value = transaction.notes || '';
        form.elements['accountSelect'].value = transaction.accountId;
        
        form.elements['transactionStatusSelect'].value = transaction.transactionStatus;

        // 設置標籤
        tagManager.setSelectedTags(transaction.tags || []);

        // 設置選中的交易對象
        const selectedEntity ={
            id: transaction.entityId,
            type: transaction.entityType
        }
        selectEntity(selectedEntity);

        // 更新交易描述的下拉選單顯示
        await updatePaymentDescriptionMenuDisplay(transaction.transactionType, transaction.paymentDescription);

        currentEditId = transactionId;

        // 如果有明細資料，載入明細
        if (details && details.length > 0) {
            const detailSwitch = document.getElementById('detailSwitch');
            const detailSection = document.getElementById('detailSection');
            
            // 開啟明細開關
            detailSwitch.checked = true;
            detailSection.classList.remove('hidden');
            
            // 載入明細資料
            details.forEach(detail => {
                keyDetailRowWithData(detail);
            });
        }
    } catch (error) {
        console.error('載入交易資料失敗：', error);
        alert('載入交易資料失敗');
    }
}

// 處理代墊款核銷轉單-自動填入
async function handleAdvanceTransfer(advanceData) {
    try {
        // 設置交易類型為支出
        document.getElementById('transactionType_expense').checked = true;
        
        // 設置交易描述
        document.getElementById('paymentDescription').value = `代墊款核銷-${advanceData.advanceNo}`;
        // 設置交易備註
        document.getElementById('notes').value =  `代墊款核銷-${advanceData.advanceNo}-${advanceData.description}`;
        // 設置金額
        document.getElementById('amount').value = advanceData.amount;
        

        // 設置付款日期為今天
        document.getElementById('paymentDate').value = new Date().toISOString().split('T')[0];

        // 如果有發票日期
        if (advanceData.invoiceDate) {
            document.getElementById('invoiceDate').value = advanceData.invoiceDate;
        }
        
        // 設置交易對象（代墊人）
        if (advanceData.employeeId) {
                    // 設置選中的交易對象
            const selectedEntity ={
                id:advanceData.employeeId, 
                type:'employee'
            }

            selectEntity(selectedEntity);

        }
        
        // 如果有明細，啟用明細輸入
        if (advanceData.details && advanceData.details.length > 0) {
            document.getElementById('detailSwitch').checked = true;
            toggleDetailSection(false); // 顯示明細區域，且不自動新增列
            
            // 填入明細資料
            advanceData.details.forEach(detail => {
                keyDetailRowWithData(detail);
            });
        }
        
        // 加入代墊款核銷標籤
        tagManager.addTag('代墊款核銷');
        
        // 儲存代墊款 ID，用於取消時回復狀態
        window.originalAdvanceId = advanceData.id;
        
        // 帳款到帳情形預設為「非日收款」
        setPaymentStatusSetting('different_day');

        // 稅別預設為「免稅」
        const taxTypeSelect = document.getElementById('taxTypeSelect');
        if (taxTypeSelect) {
            // 依照 option 文字內容尋找 "免稅" 對應的 value
            let found = false;
            for (let i = 0; i < taxTypeSelect.options.length; i++) {
                if (taxTypeSelect.options[i].text === '免稅') {
                    taxTypeSelect.selectedIndex = i;
                    found = true;
                    break;
                }
            }
            // 若找不到，則不動作
        }
        
    } catch (error) {
        console.error('處理代墊款項轉單失敗：', error);
        alert('處理代墊款項轉單失敗');
        
        // 如果處理失敗，返回代墊款項列表
        //window.location.href = 'advance.html';
    }
}

// 處理代墊款核銷轉單-取消或失敗處理
async function handleAdvanceTransferCancel() {
    //恢復代墊款狀態 (已核可- approved --> 待核可-pending)
    
    if(currentMode === 'Advance'){
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const advanceId = urlParams.get('advanceId');
            if(advanceId){
                const advance = await getAdvancePaymentById(advanceId);

                if (advance && advance.status === 'approved') {
                    advance.status = 'pending';
                    await updateAdvancePaymentToDB(advance);
                }
            }
        } catch (error) {
            console.error('恢復代墊款項狀態失敗：', error);
        }
    }
}

// 處理薪資轉單
async function handleSalaryTransfer(salaryData) {
    try {
        const form = document.querySelector('form');
        const expectedPaymentDateContainer = document.getElementById('expectedPaymentDateContainer');
        
        // 設置交易類型為支出
        form.elements['transactionType'].value = 'expense';
        
        // 設置交易描述 (會計科目代號)
        form.elements['paymentDescription'].value = 6010;
        
        // 設置交易備註
        form.elements['notes'].value = `${salaryData.description}`;
        // 設置金額
        form.elements['amount'].value = salaryData.totalAmount;
        
        form.elements['selectedEntityId'].value = salaryData.salaries[0].employeeId;
        form.elements['selectedEntityType'].value = 'employee';
        form.elements['taxTypeSelect'].value = '2';
        form.elements['taxAmount'].value = '0';
        form.elements['fee'].value = '0';
        
        //form.elements['accountSelect'].value = '1111';

        // 設置帳款到帳情形為非同日收款
        setPaymentStatusSetting('different_day');

        // 設置交易日期
        form.elements['invoiceDate'].value = salaryData.paymentDate.split('T')[0];;
        form.elements['paymentDate'].value = salaryData.paymentDate.split('T')[0];
        form.elements['expectedPaymentDate'].value = salaryData.paymentDate.split('T')[0] || '';


        // 設置交易狀態為完成
        form.elements['transactionStatusSelect'].value = 'completed';

        // 設置標籤
        tagManager.setSelectedTags(['薪資',salaryData.description]);

        // 設置交易對象
        const selectedEntity ={
            id: salaryData.salaries[0].employeeId,
            type: 'employee'
        }
        selectEntity(selectedEntity);


        // 更新交易描述的下拉選單顯示
        await updatePaymentDescriptionMenuDisplay('expense', '6010');

        
    } catch (error) {
        console.error('處理薪資轉單失敗：', error);
        alert('處理薪資轉單失敗');
    }
}

// 處理表單提交
async function handleSubmit(event) {
    event.preventDefault();

    const formData = getTransactionFormData();
    formData.id= currentEditId;

    if(formData.paymentDescription === 0){
        alert('請選擇交易項目');
        return;
    }
    const form = event.target;
    // 儲存交易/
    const savedTransactionId = await saveTransaction(form,formData);

    // 儲存失敗時
    if(!savedTransactionId){
        if(currentMode=== 'Create'){
            alert('本筆新增交易失敗，檢查資料是否正確');
        }else if(currentMode=== 'Edit'){
            alert('本筆編輯交易失敗，檢查資料是否正確');
        }else if(currentMode=== 'Advance'){
            // 判斷是否需要將代墊款［核可］狀態修改
            //恢復代墊款狀態
            handleAdvanceTransferCancel();
            // 返回代墊款項列表
            //window.location.href = 'advance.html';
        }else if(currentMode=== 'Salary'){
            // 返回薪資列表
            //window.location.href = 'salary.html';
        }
    }else{
        // 儲存成功後的處理
        alert('交易儲存成功！');
        if(currentMode=== 'Create'){
            // 返回上一頁
            //window.history.back();
        }else if(currentMode=== 'Edit'){
            //window.location.href = 'transactions.html';
        }else if(currentMode=== 'Advance'){
            
            // 存入本次交易紀錄ID，至代墊款資料表中。
            const advanceData = await getAdvancePaymentById(window.originalAdvanceId);
            advanceData.transactionId = savedTransactionId;
            advanceData.status = 'approved';
            advanceData.approvedAt = new Date().toISOString();
            await updateAdvancePaymentToDB(advanceData)
            // 返回代墊款項列表
            //window.location.href = 'advance.html';
        }else if(currentMode=== 'Salary'){
            // 返回薪資列表
            // 存入本次交易紀錄ID，至薪資資料表中。
            //window.location.href = 'salary.html';
        }
    }


}

// 處理取消按鈕
async function handleCancel() {
    // 返回上一頁
    window.history.back();
}

// 離開頁面時自動
window.addEventListener('beforeunload', async function (e) {
    // 避免非正常操作離開頁面

});
/***********選單資料相關***********/

// 載入帳戶和稅別選項
async function loadOptions() {
    try {
        // 載入帳戶選項
        const accounts = await getAccountsAll();
        var accountSelect = document.getElementById('accountSelect');
        accounts.forEach(function(account) {
            var option = document.createElement('option');
            option.value = account.id;
            option.textContent = account.name;
            accountSelect.appendChild(option);
        });
        
        // 載入稅別選項
        const taxTypes = await getTaxTypesRatesAll();
        var taxTypeSelect = document.getElementById('taxTypeSelect');
        taxTypes.forEach(function(taxType) {
                var option = document.createElement('option');
                option.dataset.rate = taxType.rate;
                option.value = taxType.id;
                option.textContent = taxType.name;
                taxTypeSelect.appendChild(option);
        });

        //載入初始化 帳款到帳情形 選項
        loadPaymentStatusOptions();

        //載入初始化 交易對象搜尋 欄位
        loadEntitySearchOptions();

    } catch (error) {
        console.error('載入選項時發生錯誤:', error);
        alert('載入資料時發生錯誤，請重新整理頁面或聯絡系統管理員');
    }
}



/***********計算相關***********/

/**
 * @description 處理稅額計算
 * @param {number} amount 含稅金額
 * @param {number} taxRate 稅率
 * @returns {number} 稅額
 */
function calculateTaxAmount(amount, taxRate) {
    //因為輸入金額屬於含稅，假設未稅A則A*(1+稅率)=B 則稅額=B/A-1=B-A  
    const amountNoTax = Math.round(amount / (1 + Number(taxRate)));//未稅金額
    const taxAmount = amount - amountNoTax;//稅額
    return taxAmount;
}

/***********儲存相關***********/

// 儲存交易
async function saveTransaction(form,formData) {
    try {
        let savedTransactionId;
        
        if (currentEditId) {
            // 編輯模式下更新交易
            formData.id = currentEditId;
            savedTransactionId = await saveTransactionToDB(formData);
        } else {
            // 新增模式下新增交易
            formData.createdAt = new Date().toISOString();
            //清除id，讓資料庫自動產生
            delete formData.id;
            savedTransactionId = await saveTransactionToDB(formData);
        }

        // 儲存成功後在處理明細資料
        if(savedTransactionId){
            // 處理明細資料
            if (document.getElementById('detailSwitch').checked) {
                
                //傳入交易ID，取得打包後的明細資料
                const details = getTransactionDetailFormData(savedTransactionId);
                console.log('明細資料',details);

                if (currentEditId) {
                    await updateTransactionDetailToDB(details);
                } else {
                    await saveTransactionDetailToDB(details);
                }
            } else if (currentEditId) {
                // 如果是編輯模式且明細開關關閉，刪除所有相關明細
                await deleteTransactionDetailToDB(currentEditId);
            }

            // 儲存會計分錄
            if(currentEditId){
                // 如果是編輯模式，先刪除舊的會計分錄
                await deleteOldJournal(currentEditId);
                await saveNewJournal(savedTransactionId, formData);
            }else{
                await saveNewJournal(savedTransactionId, formData);
            }

            return savedTransactionId;
        }else{
            
            return null;
        }
        
        
    } catch (error) {
        console.error('儲存交易失敗：', error);
        alert('儲存交易失敗');
        return null;
    }
}
