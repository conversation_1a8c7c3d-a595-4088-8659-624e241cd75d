/**
 * @file batchTransaction_DataManager.js
 * @description 批次交易資料管理模組
 * 負責管理批次交易資料的儲存、更新和處理
 */

/**
 * 批次資料管理器類別
 */
class BatchDataManager {
    constructor() {
        this.data = new Map(); // 使用 Map 儲存行資料
        this.batchSettings = {}; // 批次設定
    }

    /**
     * 設定行資料
     * @param {number} rowIndex - 行索引
     * @param {Object} rowData - 行資料
     */
    setRowData(rowIndex, rowData) {
        this.data.set(rowIndex, rowData);
    }

    /**
     * 獲取行資料
     * @param {number} rowIndex - 行索引
     * @returns {Object} 行資料
     */
    getRowData(rowIndex) {
        return this.data.get(rowIndex) || this.createEmptyRowData();
    }

    /**
     * 移除行資料
     * @param {number} rowIndex - 行索引
     */
    removeRow(rowIndex) {
        this.data.delete(rowIndex);
    }

    /**
     * 清空所有資料
     */
    clearAll() {
        this.data.clear();
        this.batchSettings = {};
    }

    /**
     * 獲取所有行資料
     * @returns {Array} 所有行資料陣列
     */
    getAllRowData() {
        return Array.from(this.data.values());
    }

    /**
     * 更新批次設定
     */
    updateBatchSettings() {
        this.batchSettings = {
            transactionType: document.querySelector('input[name="batchTransactionType"]:checked')?.value || 'expense',
            accountId: document.getElementById('batchAccountSelect').value,
            paymentStatus: document.getElementById('batchPaymentStatusSelect').value,
            taxTypeId: document.getElementById('batchTaxTypeSelect').value,
            transactionStatus: document.getElementById('batchStatusSelect').value
        };
    }

    /**
     * 獲取批次設定
     * @returns {Object} 批次設定
     */
    getBatchSettings() {
        this.updateBatchSettings();
        return this.batchSettings;
    }

    /**
     * 創建空的行資料
     * @returns {Object} 空的行資料物件
     */
    createEmptyRowData() {
        return {
            entityId: '',
            entityType: '',
            entityName: '',
            paymentDescription: '',
            paymentDescriptionName: '',
            amount: 0,
            taxAmount: 0,
            invoiceNumber: '',
            notes: '',
            // 日期欄位將根據到帳情形動態設定
            paymentDate: '',
            invoiceDate: '',
            expectedPaymentDate: ''
        };
    }

    /**
     * 從表單收集行資料
     * @param {number} rowIndex - 行索引
     * @returns {Object} 收集到的行資料
     */
    collectRowDataFromForm(rowIndex) {
        const rowData = this.createEmptyRowData();
        
        // 交易對象
        const entityInput = document.getElementById(`entity_${rowIndex}`);
        const entityIdInput = document.getElementById(`entity_${rowIndex}_id`);
        const entityTypeInput = document.getElementById(`entity_${rowIndex}_type`);
        
        if (entityInput && entityIdInput && entityTypeInput) {
            rowData.entityName = entityInput.value;
            rowData.entityId = entityIdInput.value;
            rowData.entityType = entityTypeInput.value;
        }
        
        // 交易項目
        const paymentDescInput = document.getElementById(`paymentDescription_${rowIndex}`);
        const paymentDescNameInput = document.getElementById(`paymentDescription_${rowIndex}_name`);
        
        if (paymentDescInput) {
            rowData.paymentDescription = paymentDescInput.value;
        }
        if (paymentDescNameInput) {
            rowData.paymentDescriptionName = paymentDescNameInput.value;
        }
        
        // 金額
        const amountInput = document.getElementById(`amount_${rowIndex}`);
        if (amountInput) {
            rowData.amount = parseFloat(amountInput.value) || 0;
        }
        
        // 稅額
        const taxAmountInput = document.getElementById(`taxAmount_${rowIndex}`);
        if (taxAmountInput) {
            rowData.taxAmount = parseFloat(taxAmountInput.value) || 0;
        }
        
        // 發票號碼
        const invoiceNumberInput = document.getElementById(`invoiceNumber_${rowIndex}`);
        if (invoiceNumberInput) {
            rowData.invoiceNumber = invoiceNumberInput.value;
        }
        
        // 備註
        const notesInput = document.getElementById(`notes_${rowIndex}`);
        if (notesInput) {
            rowData.notes = notesInput.value;
        }
        
        // 儲存到資料管理器
        this.setRowData(rowIndex, rowData);
        
        return rowData;
    }

    /**
     * 合併行資料與批次設定
     * @param {Object} rowData - 行資料
     * @returns {Object} 完整的交易資料
     */
    mergeWithBatchSettings(rowData) {
        const batchSettings = this.getBatchSettings();
        
        // 設定日期欄位
        const today = new Date().toISOString().split('T')[0];
        let paymentDate = today;
        let invoiceDate = today;
        let expectedPaymentDate = '';
        
        // 根據到帳情形設定日期
        switch (batchSettings.paymentStatus) {
            case 'same_day':
                // 同日收付款 - 付款日期和發票日期相同
                paymentDate = today;
                invoiceDate = today;
                break;
            case 'receivable':
                // 應收付款 - 沒有實際付款日期
                paymentDate = '';
                invoiceDate = today;
                expectedPaymentDate = today;
                break;
            case 'prepayment':
                // 暫收付款 - 沒有發票日期
                paymentDate = today;
                invoiceDate = '';
                break;
            case 'different_day':
                // 非同日收款 - 所有日期都有
                paymentDate = today;
                invoiceDate = today;
                expectedPaymentDate = today;
                break;
        }
        
        return {
            // 批次設定
            transactionType: batchSettings.transactionType,
            accountId: batchSettings.accountId,
            paymentStatus: batchSettings.paymentStatus,
            taxTypeId: batchSettings.taxTypeId,
            transactionStatus: batchSettings.transactionStatus,
            
            // 日期欄位
            paymentDate: paymentDate,
            invoiceDate: invoiceDate,
            expectedPaymentDate: expectedPaymentDate,
            
            // 行資料
            entityId: rowData.entityId,
            entityType: rowData.entityType,
            paymentDescription: Number(rowData.paymentDescription) || 0,
            amount: rowData.amount,
            taxAmount: rowData.taxAmount,
            invoiceNumber: rowData.invoiceNumber,
            notes: rowData.notes,
            
            // 其他必要欄位
            fee: 0, // 暫時設為 0，未來可擴展
            tags: [] // 暫時為空陣列，未來可擴展
        };
    }

    /**
     * 批次儲存所有有效交易
     * @param {Array} validTransactions - 有效的交易資料陣列
     * @returns {Array} 儲存結果陣列
     */
    async saveAll(validTransactions) {
        const results = [];
        
        for (let i = 0; i < validTransactions.length; i++) {
            const validation = validTransactions[i];
            
            try {
                // 收集該行的完整資料
                const rowData = this.collectRowDataFromForm(validation.rowIndex);
                const completeData = this.mergeWithBatchSettings(rowData);
                
                // 儲存交易
                const savedTransactionId = await this.saveSingleTransaction(completeData);
                
                results.push({
                    rowIndex: validation.rowIndex,
                    displayIndex: validation.displayIndex,
                    success: true,
                    transactionId: savedTransactionId,
                    message: '儲存成功'
                });
                
            } catch (error) {
                console.error(`儲存第 ${validation.displayIndex} 筆交易失敗:`, error);
                results.push({
                    rowIndex: validation.rowIndex,
                    displayIndex: validation.displayIndex,
                    success: false,
                    error: error.message || '儲存失敗',
                    message: '儲存失敗: ' + (error.message || '未知錯誤')
                });
            }
        }
        
        return results;
    }

    /**
     * 儲存單筆交易
     * @param {Object} transactionData - 交易資料
     * @returns {string} 儲存的交易 ID
     */
    async saveSingleTransaction(transactionData) {
        try {
            // 儲存交易記錄
            const savedTransactionId = await saveTransactionToDB(transactionData);
            
            if (!savedTransactionId) {
                throw new Error('交易記錄儲存失敗');
            }
            
            // 儲存會計分錄
            await this.saveTransactionJournal(savedTransactionId, transactionData);
            
            return savedTransactionId;
            
        } catch (error) {
            console.error('儲存單筆交易失敗:', error);
            throw error;
        }
    }

    /**
     * 儲存交易的會計分錄
     * @param {string} transactionId - 交易 ID
     * @param {Object} transactionData - 交易資料
     */
    async saveTransactionJournal(transactionId, transactionData) {
        try {
            // 轉換為會計分錄所需的格式
            const journalFormData = await this.transformToJournalData(transactionData);
            
            // 生成會計分錄
            const mjournal = new Journal(journalFormData);
            const journalList = mjournal.getJournal();
            
            // 儲存會計分錄到資料庫
            const saveResult = await saveJournalEntriesToDB(transactionId, journalList);
            
            if (!saveResult) {
                throw new Error('會計分錄儲存失敗');
            }
            
            return true;
            
        } catch (error) {
            console.error('儲存會計分錄失敗:', error);
            throw error;
        }
    }

    /**
     * 轉換為會計分錄格式
     * @param {Object} transactionData - 交易資料
     * @returns {Object} 會計分錄格式的資料
     */
    async transformToJournalData(transactionData) {
        // 獲取帳戶資訊
        const account = await getAccountById(transactionData.accountId);
        const mainAccountName = account ? account.name : '未知帳戶';
        const mainAccountType = account ? account.type : 'unknown';
        
        // 獲取交易對象資訊
        let counterpartyName = '未指定';
        if (transactionData.entityId && transactionData.entityType) {
            try {
                if (transactionData.entityType === 'employee') {
                    const employee = await getEmployeeById(transactionData.entityId);
                    counterpartyName = employee ? employee.name : '未知員工';
                } else {
                    const entity = await getEntityById(transactionData.entityId);
                    counterpartyName = entity ? entity.name : '未知交易對象';
                }
            } catch (error) {
                console.warn('獲取交易對象資訊失敗:', error);
            }
        }
        
        // 獲取交易項目描述
        let description = '未選擇';
        if (transactionData.paymentDescription) {
            try {
                const items = await getTransactionCategoryItemsAll();
                const item = items.find(item => item.accountingCode == transactionData.paymentDescription);
                description = item ? item.accountingName : '未選擇';
            } catch (error) {
                console.warn('獲取交易項目描述失敗:', error);
            }
        }
        
        return {
            type: transactionData.transactionType,
            status: transactionData.paymentStatus,
            mainAccountName: mainAccountName,
            mainAccountType: mainAccountType,
            counterpartyName: counterpartyName,
            mainAmount: transactionData.amount,
            taxAmount: transactionData.taxAmount,
            feeAmount: transactionData.fee || 0,
            totalAmount: transactionData.amount - transactionData.taxAmount,
            paymentDate: transactionData.paymentDate || transactionData.invoiceDate,
            invoiceDate: transactionData.invoiceDate || transactionData.paymentDate,
            description: description,
            descriptionCode: transactionData.paymentDescription
        };
    }
}
