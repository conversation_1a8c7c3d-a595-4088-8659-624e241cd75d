document.addEventListener('DOMContentLoaded', function() {
    const transactionTypeInputs = document.querySelectorAll('input[name="transactionType"]');
    const paymentStatusContainer = document.querySelector('#paymentStatusSelect').parentElement;
    const invoiceDateContainer = document.querySelector('#invoiceDate').parentElement;
    const taxTypeContainer = document.querySelector('#taxTypeSelect').parentElement;
    const taxAmountContainer = document.querySelector('#taxAmount').parentElement.parentElement;
    const expectedPaymentDateContainer = document.getElementById('expectedPaymentDateContainer');
    const paymentDescriptionMegaMenuContainer = document.getElementById('paymentDescriptionMegaMenuContainer');


    // 處理交易類型變化的函數
    window.handleTransactionTypeChange = function(isTransfer) {
        if (isTransfer) {
            // 隱藏不需要的欄位並移除必填限制
            paymentStatusContainer.classList.add('hidden');
            document.querySelector('#paymentStatusSelect').removeAttribute('required');
            
            invoiceDateContainer.classList.add('hidden');
            document.querySelector('#invoiceDate').removeAttribute('required');
            
            taxTypeContainer.classList.add('hidden');
            document.querySelector('#taxTypeSelect').removeAttribute('required');
            
            taxAmountContainer.classList.add('hidden');
            document.querySelector('#taxAmount').removeAttribute('required');
            
            expectedPaymentDateContainer.classList.add('hidden');
            document.querySelector('#expectedPaymentDate').removeAttribute('required');
            
            paymentDescriptionMegaMenuContainer.classList.add('hidden');
            document.querySelector('#paymentDescription').removeAttribute('required');
            document.querySelector('#paymentDescription').value = '1110';//當帳戶間轉移時，設定交易項目會計為銀行存款"1110"
        
        } else {
            // 顯示所有欄位並恢復必填限制
            paymentStatusContainer.classList.remove('hidden');
            document.querySelector('#paymentStatusSelect').setAttribute('required', '');
            
            invoiceDateContainer.classList.remove('hidden');
            document.querySelector('#invoiceDate').setAttribute('required', '');
            
            taxTypeContainer.classList.remove('hidden');
            document.querySelector('#taxTypeSelect').setAttribute('required', '');
            
            taxAmountContainer.classList.remove('hidden');
            document.querySelector('#taxAmount').setAttribute('required', '');
            
            // expectedPaymentDateContainer的顯示由paymentStatus.js控制
            paymentDescriptionMegaMenuContainer.classList.remove('hidden');
            document.querySelector('#paymentDescription').setAttribute('required', '');
            document.querySelector('#paymentDescription').value = '';//當交易類型為現金交易時，設定交易項目會計為空
        }
    }

    // 檢查初始狀態
    const selectedTransactionType = document.querySelector('input[name="transactionType"]:checked');
    if (selectedTransactionType) {
        handleTransactionTypeChange(selectedTransactionType.value === 'transfer');
    }
});