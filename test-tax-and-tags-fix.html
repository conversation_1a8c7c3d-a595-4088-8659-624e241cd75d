<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>稅額計算與標籤儲存修正驗證</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🔧 稅額計算與標籤儲存修正驗證</h1>
        
        <!-- 問題說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-red-600">❌ 發現的問題</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h3 class="font-medium text-red-800 mb-2">問題 1: 稅額計算範圍錯誤</h3>
                    <ul class="text-red-700 text-sm space-y-1">
                        <li>• 共同屬性稅別切換只影響第1筆</li>
                        <li>• 第2筆之後的稅額不會自動計算</li>
                        <li>• 原因：使用錯誤的行選擇器</li>
                        <li>• 兩列式佈局中每個邏輯行包含兩個 tr 元素</li>
                    </ul>
                </div>
                <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h3 class="font-medium text-red-800 mb-2">問題 2: 標籤資料未儲存</h3>
                    <ul class="text-red-700 text-sm space-y-1">
                        <li>• 標籤輸入不會存入資料庫</li>
                        <li>• 資料收集時使用錯誤的欄位名稱</li>
                        <li>• 共同屬性和個別屬性處理不一致</li>
                        <li>• 行選擇器邏輯錯誤</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 修正方案 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-green-600">✅ 修正方案</h2>
            <div class="space-y-4">
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-medium text-green-800 mb-2">修正 1: 稅額計算邏輯</h3>
                    <pre class="text-sm text-green-700 bg-white p-3 rounded border overflow-x-auto"><code>// 修正前：錯誤的行選擇器
const rows = document.querySelectorAll('#batchTableBody tr');
rows.forEach((row, index) => {
    const rowNum = index + 1; // ❌ 錯誤：包含了第二行的 tr
    updateRowTaxCalculation(rowNum, taxRate);
});

// 修正後：正確的邏輯行選擇器
const firstRows = document.querySelectorAll('#batchTableBody tr.batch-row-first');
firstRows.forEach((firstRow) => {
    const rowNum = parseInt(firstRow.dataset.rowNum); // ✅ 正確：只處理邏輯行
    if (rowNum) {
        updateRowTaxCalculation(rowNum, taxRate);
    }
});</code></pre>
                </div>
                
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 class="font-medium text-blue-800 mb-2">修正 2: 標籤資料收集</h3>
                    <pre class="text-sm text-blue-700 bg-white p-3 rounded border overflow-x-auto"><code>// 修正前：錯誤的欄位名稱和行選擇器
const rows = document.querySelectorAll('#batchTableBody tr');
// 沒有正確處理標籤欄位

// 修正後：正確的標籤資料處理
// 共同屬性標籤
if (field === 'tags') {
    commonProps[field] = document.querySelector(`input[name="batch_tagsData"]`)?.value || '';
}

// 個別屬性標籤
else if (field === 'tags') {
    const tagsData = document.querySelector(`input[name="batch_tagsData_${rowNum}"]`)?.value || '';
    transaction[field] = tagsData;
}</code></pre>
                </div>
            </div>
        </div>
        
        <!-- 測試指引 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibent mb-4">🧪 測試指引</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 1: 共同屬性稅額計算</h3>
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <ol class="text-sm text-blue-700 space-y-1">
                            <li>1. 將稅別設為共同屬性</li>
                            <li>2. 選擇一個稅別（如5%營業稅）</li>
                            <li>3. 添加多行，在每行輸入不同金額</li>
                            <li>4. 切換稅別，檢查所有行的稅額是否都重新計算</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 2: 個別屬性稅額計算</h3>
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <ol class="text-sm text-green-700 space-y-1">
                            <li>1. 將稅別設為個別屬性</li>
                            <li>2. 在每行選擇不同的稅別</li>
                            <li>3. 輸入金額，檢查稅額是否正確計算</li>
                            <li>4. 修改稅別，檢查稅額是否重新計算</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 3: 標籤功能測試</h3>
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <ol class="text-sm text-yellow-700 space-y-1">
                            <li>1. 在標籤欄位輸入標籤（按Enter添加）</li>
                            <li>2. 添加多個標籤到不同行</li>
                            <li>3. 點擊"批次儲存"按鈕</li>
                            <li>4. 檢查控制台確認標籤資料被正確收集</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 4: 資料收集驗證</h3>
                    <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <ol class="text-sm text-purple-700 space-y-1">
                            <li>1. 填寫完整的交易資料</li>
                            <li>2. 在控制台執行資料收集命令</li>
                            <li>3. 檢查收集到的資料結構</li>
                            <li>4. 確認標籤資料格式正確</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 驗證工具 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibent mb-4">🛠️ 驗證工具</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    開啟批次頁面
                </button>
                <button onclick="showTestCommands()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    <i class="fas fa-code mr-2"></i>
                    測試命令
                </button>
                <button onclick="showExpectedResults()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    <i class="fas fa-check-circle mr-2"></i>
                    預期結果
                </button>
                <button onclick="showTroubleshooting()" 
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    <i class="fas fa-bug mr-2"></i>
                    問題排除
                </button>
            </div>
            
            <!-- 測試命令 -->
            <div id="testCommands" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg hidden">
                <h3 class="font-medium text-green-800 mb-3">💻 在批次頁面控制台中執行</h3>
                <div class="space-y-2">
                    <div class="p-2 bg-white border rounded">
                        <code class="text-sm text-gray-700">
                            // 測試稅額計算函式<br>
                            updateAllRowsTaxCalculationWithRate(0.05); // 5%稅率<br>
                            console.log('稅額計算測試完成');
                        </code>
                    </div>
                    <div class="p-2 bg-white border rounded">
                        <code class="text-sm text-gray-700">
                            // 測試資料收集<br>
                            const transactions = collectBatchTransactions();<br>
                            console.log('收集到的交易資料:', transactions);<br>
                            console.log('標籤資料:', transactions.map(t => ({ rowNum: t.rowNum, tags: t.tags })));
                        </code>
                    </div>
                    <div class="p-2 bg-white border rounded">
                        <code class="text-sm text-gray-700">
                            // 檢查邏輯行數量<br>
                            const firstRows = document.querySelectorAll('#batchTableBody tr.batch-row-first');<br>
                            console.log('邏輯行數量:', firstRows.length);<br>
                            console.log('總 tr 數量:', document.querySelectorAll('#batchTableBody tr').length);
                        </code>
                    </div>
                </div>
            </div>
            
            <!-- 預期結果 -->
            <div id="expectedResults" class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg hidden">
                <h3 class="font-medium text-yellow-800 mb-3">🎯 預期結果</h3>
                <ul class="text-sm text-yellow-700 space-y-1">
                    <li>• <strong>稅額計算:</strong> 所有行的稅額都會同時更新</li>
                    <li>• <strong>標籤收集:</strong> 標籤資料正確包含在交易物件中</li>
                    <li>• <strong>行數計算:</strong> 邏輯行數 = 總 tr 數 ÷ 2</li>
                    <li>• <strong>資料完整性:</strong> 所有欄位資料都被正確收集</li>
                </ul>
            </div>
            
            <!-- 問題排除 -->
            <div id="troubleshooting" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg hidden">
                <h3 class="font-medium text-red-800 mb-3">🔧 問題排除</h3>
                <ul class="text-sm text-red-700 space-y-2">
                    <li>• <strong>稅額不計算:</strong> 檢查金額欄位是否有值，稅率是否正確</li>
                    <li>• <strong>標籤未儲存:</strong> 檢查 batch_tagsData_X 欄位是否有值</li>
                    <li>• <strong>行數錯誤:</strong> 確認使用 .batch-row-first 選擇器</li>
                    <li>• <strong>資料缺失:</strong> 檢查欄位名稱是否正確</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function openBatchPage() {
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function showTestCommands() {
            const element = document.getElementById('testCommands');
            element.classList.toggle('hidden');
        }
        
        function showExpectedResults() {
            const element = document.getElementById('expectedResults');
            element.classList.toggle('hidden');
        }
        
        function showTroubleshooting() {
            const element = document.getElementById('troubleshooting');
            element.classList.toggle('hidden');
        }
        
        // 頁面載入時顯示修正完成訊息
        document.addEventListener('DOMContentLoaded', function() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>稅額計算與標籤儲存問題已修正！</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 5000);
        });
    </script>
</body>
</html>
