<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次交易輸入 - 益芯能源工程-管理系統</title>
    <link rel="icon" href="../../common/img/logo.png" type="image/x-icon">

    <!-- 模組導入映射設定 -->
    <script type="importmap">
    {
        "imports": {
        "@common/": "/common/"
        }
    }
    </script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 自定義樣式 -->
    <style>
        /* 批次表格樣式 */
        .batch-table {
            border-collapse: collapse;
            width: 100%;
        }
        
        .batch-table th,
        .batch-table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        .batch-table th {
            background-color: #f9fafb;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .batch-table tbody tr:hover {
            background-color: #f3f4f6;
        }
        
        /* 輸入欄位樣式 */
        .batch-input {
            width: 100%;
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .batch-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 1px #3b82f6;
        }
        
        /* 錯誤狀態 */
        .error-row {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
        }
        
        .error-input {
            border-color: #ef4444;
            background-color: #fef2f2;
        }
        
        /* 成功狀態 */
        .success-row {
            background-color: #f0fdf4;
            border-left: 4px solid #22c55e;
        }
        
        /* 下拉選單樣式 */
        .dropdown-menu {
            position: absolute;
            z-index: 50;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
        }
        
        .dropdown-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .dropdown-item:hover {
            background-color: #f3f4f6;
        }
        
        .dropdown-item:last-child {
            border-bottom: none;
        }
        
        /* 共同欄位設定區樣式 */
        .common-field-section {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 1px solid #93c5fd;
        }
        
        /* 按鈕樣式 */
        .btn-primary {
            background-color: #3b82f6;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
        }
        
        .btn-secondary {
            background-color: #6b7280;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        
        .btn-danger {
            background-color: #ef4444;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }
        
        .btn-danger:hover {
            background-color: #dc2626;
        }
    </style>

    <!-- 自定義工具庫載入 -->
    <script src="../../common/firebaseAPI/auth.js"></script>
    <script src="../../common/db/db.js"></script>
    <script src="../../common/db/preload.js"></script>
    <script src="../../common/utils/CommonUtils.js"></script>
    <script src="../../common/utils/pageTransfer.js"></script>
    <script src="../../common/utils/ModalUtils.js"></script>
    <script src="../../common/utils/DatabaseErrors.js"></script>
</head>

<body class="bg-gray-100">
    <!-- 導航欄 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">批次交易輸入</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button type="button" onclick="handleCancel()" 
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        <i class="fas fa-arrow-left mr-2"></i>返回
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <main class="container mx-auto px-4 py-8">
        <!-- 頁面標題區 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">批次交易輸入</h1>
            <p class="text-gray-600">一次性輸入多筆交易資料，提高資料輸入效率</p>
        </div>

        <!-- 批次設定區 -->
        <div class="common-field-section rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">
                <i class="fas fa-cog mr-2 text-blue-600"></i>批次設定
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- 交易類型 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">交易類型</label>
                    <div class="flex space-x-2">
                        <label class="flex items-center">
                            <input type="radio" name="batchTransactionType" value="expense" checked 
                                class="mr-1" onchange="handleBatchTransactionTypeChange()">
                            <span class="text-sm text-red-600">支出</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="batchTransactionType" value="income" 
                                class="mr-1" onchange="handleBatchTransactionTypeChange()">
                            <span class="text-sm text-blue-600">收入</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="batchTransactionType" value="transfer" 
                                class="mr-1" onchange="handleBatchTransactionTypeChange()">
                            <span class="text-sm text-green-600">轉移</span>
                        </label>
                    </div>
                </div>

                <!-- 主要帳戶 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">主要帳戶</label>
                    <select id="batchAccountSelect" class="batch-input" onchange="handleBatchFieldChange()">
                        <option value="">請選擇帳戶</option>
                    </select>
                </div>

                <!-- 到帳情形 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">到帳情形</label>
                    <select id="batchPaymentStatusSelect" class="batch-input" onchange="handleBatchPaymentStatusChange()">
                        <option value="">請選擇狀態</option>
                        <option value="same_day">同日收付款</option>
                        <option value="receivable">應收付款</option>
                        <option value="prepayment">暫收付款</option>
                        <option value="different_day">非同日收款</option>
                    </select>
                </div>

                <!-- 稅別 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">稅別</label>
                    <select id="batchTaxTypeSelect" class="batch-input" onchange="handleBatchFieldChange()">
                        <option value="">請選擇稅別</option>
                    </select>
                </div>

                <!-- 狀態 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">狀態</label>
                    <select id="batchStatusSelect" class="batch-input" onchange="handleBatchFieldChange()">
                        <option value="">請選擇狀態</option>
                        <option value="completed">已完成</option>
                        <option value="pending">未撥款</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 資料輸入區 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-table mr-2 text-green-600"></i>批次交易資料
                </h2>
                <div class="flex space-x-2">
                    <button type="button" onclick="addBatchRow()" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>新增行
                    </button>
                    <button type="button" onclick="clearAllRows()" class="btn-secondary">
                        <i class="fas fa-trash mr-2"></i>清空全部
                    </button>
                </div>
            </div>

            <!-- 批次資料表格 -->
            <div class="overflow-x-auto">
                <table class="batch-table">
                    <thead>
                        <tr>
                            <th style="width: 50px;">#</th>
                            <th style="width: 200px;">交易對象 <span class="text-red-500">*</span></th>
                            <th style="width: 200px;">交易項目 <span class="text-red-500">*</span></th>
                            <th style="width: 120px;">金額 <span class="text-red-500">*</span></th>
                            <th style="width: 100px;">稅額</th>
                            <th style="width: 150px;">發票號碼</th>
                            <th style="width: 200px;">備註</th>
                            <th style="width: 80px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="batchTableBody">
                        <!-- 批次交易資料行將在此動態生成 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 操作控制區 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    <span>總計：</span>
                    <span id="totalRowsCount" class="font-semibold text-lg">0</span>
                    <span>筆交易</span>
                </div>
                <div class="flex space-x-3">
                    <button type="button" onclick="validateAllTransactions()" class="btn-primary">
                        <i class="fas fa-check-circle mr-2"></i>驗證資料
                    </button>
                    <button type="button" onclick="submitBatchTransactions()" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>批次儲存
                    </button>
                </div>
            </div>
        </div>

        <!-- 結果顯示區 -->
        <div id="resultSection" class="bg-white rounded-lg shadow-md p-6 hidden">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">
                <i class="fas fa-chart-bar mr-2 text-indigo-600"></i>處理結果
            </h2>
            
            <!-- 結果統計 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 text-2xl mr-3"></i>
                        <div>
                            <div class="text-sm text-green-600">成功</div>
                            <div id="successCount" class="text-2xl font-bold text-green-700">0</div>
                        </div>
                    </div>
                </div>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-times-circle text-red-500 text-2xl mr-3"></i>
                        <div>
                            <div class="text-sm text-red-600">失敗</div>
                            <div id="failureCount" class="text-2xl font-bold text-red-700">0</div>
                        </div>
                    </div>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-list text-blue-500 text-2xl mr-3"></i>
                        <div>
                            <div class="text-sm text-blue-600">總計</div>
                            <div id="totalCount" class="text-2xl font-bold text-blue-700">0</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 詳細結果列表 -->
            <div id="resultDetails" class="space-y-2">
                <!-- 詳細結果將在此動態顯示 -->
            </div>

            <!-- 結果操作按鈕 -->
            <div class="mt-6 flex justify-end space-x-3">
                <button type="button" onclick="resetBatchForm()" class="btn-primary">
                    <i class="fas fa-redo mr-2"></i>重新開始
                </button>
            </div>
        </div>
    </main>

    <!-- 載入現有模組 -->
    <script src="transactionCreate_EntitySearch_Module.js"></script>
    <script src="transactionCreate_PaymentStatus_Module.js"></script>
    <script src="transactionCreate_TransactionType_Module.js"></script>
    <script src="transactionCreate_PaymentDescriptionMegaMenu_Module.js"></script>
    <script src="tagManager.js"></script>
    <script src="transactionCreate_JournalManagement_Module.js"></script>
    <script src="transactionCreate_Service.js"></script>
    <script src="transactionCreate_UI.js"></script>

    <!-- 批次交易專用模組 -->
    <script src="batchTransaction_Controller.js"></script>
    <script src="batchTransaction_UI.js"></script>
    <script src="batchTransaction_DataManager.js"></script>
    <script src="batchTransaction_Validator.js"></script>
    <script src="batchTransaction_Service.js"></script>

</body>
</html>
