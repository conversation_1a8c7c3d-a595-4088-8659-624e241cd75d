/**
 * @file transactionCreate_Controller.js
 * @description 這個檔案是模組的主控制器。
 * - 它負責在頁面載入完成後，初始化必要的資料。
 * - 作為整個模組的進入點，協調其他模組的啟動和資料更新。
 */
//全域-目前執行模式： Create(新增) / Edit(編輯) / Advance(處理代墊款轉單) / Salary(處理薪資資料)
let currentMode = 'Create'; //預設模式為新增模式
let currentEditId = null;// 編輯模式時的 ID


/**
 * DOM 內容載入完成後執行的初始化函式
 * 這是頁面載入後第一個執行的主要邏輯。
 */
document.addEventListener('DOMContentLoaded', async () => {


    // 載入 帳戶、稅別 及 帳款到帳情形 選單選項
    await loadOptions();
    //預設交易類型(收入/支出)為支出，初始渲染交易項目選單，後續會隨著交易類型變化進行重新渲染
    await renderPaymentDescriptionMegaMenu('expense');

    //載入預設值完成後，開始載入監聽事件
    loadEventListeners();

    //初始化會計分錄管理模組，監聽交易表單變化進行會計分錄預覽
    initializeJournalManagementModule();

    //先獲取網址參數，用於判斷目前執行模式
    const {currentMode,transferData,editId} = await getCurrentMode();
    //確保帳戶及稅別選項載入完成後，載入頁面時初始化處理欄位預設顯示狀態
    await initializePage(currentMode,transferData,editId);
    //欄位預設處理後，處理第一次預覽 會計分錄
    updatePreview();



});


/**
 * @description 載入DOM監聽事件
 */
function loadEventListeners() {
    //載入帳款到帳情形、日期相關之事件監聽
    load_PaymentStatus_EventListener();

    //載入交易對象搜尋相關之事件監聽
    load_EntitySearch_EventListener();

    //處理稅額自動計算
    function handleUpdateTaxAmountChange() {
        const {taxTypeIdSelect}=getTransactionFormCard_DOMElements();
        const taxRate = taxTypeIdSelect.options[taxTypeIdSelect.selectedIndex].dataset.rate;
        const amount = amountInput.value; //完稅金額
        taxAmountInput.value = calculateTaxAmount(amount, taxRate);
    }

    //獲取相關DOM元素
    const {
        taxTypeIdSelect,
        taxAmountInput,
        amountInput,
        invoiceNumberContainer,
        feeToggle,
        feeInputContainer
    }=getTransactionFormCard_DOMElements();

    //處理稅別變化時，自動計算稅額並且確認是否開啟發票號碼輸入欄位
    taxTypeIdSelect.addEventListener('change', function() {
        handleUpdateTaxAmountChange();
        //當稅別為營業稅時，則開啟發票號碼輸入欄位
        if(this.value === '1'){
            invoiceNumberContainer.classList.remove('hidden');
        }else{
            invoiceNumberContainer.classList.add('hidden');
        }
    });

    // 監聽Toggle開關的變化
    feeToggle.addEventListener('change', function() {
        if (this.checked) {
            feeInputContainer.classList.remove('hidden');
        } else {
            feeInputContainer.classList.add('hidden');
        }
    });

    //處理金額變化時，自動計算稅額
    amountInput.addEventListener('input', function() {        
        handleUpdateTaxAmountChange();
    });

    // 設置交易類型變更監聽器
    const transactionTypeRadios = document.querySelectorAll('input[name="transactionType"]');
    transactionTypeRadios.forEach(radio => {
        radio.addEventListener('change', async function() {
            // 重設選單顯示文字
            document.getElementById('paymentDescriptionSelected').textContent = '請選擇交易項目';
            document.getElementById('paymentDescription').value = '';
            handleTransactionTypeChange(this.value === 'transfer');
            // 重新渲染選單
            await renderPaymentDescriptionMegaMenu(this.value);
        });
    });

    // 設置表單提交事件
    document.querySelector('form').addEventListener('submit', handleSubmit);
}

/**
 * @description 開啟頁面時初始化處理欄位預設顯示狀態
 */
async function initializePage(currentMode,transferData,editId) {
    const {paymentDateSelect}=getTransactionFormCard_DOMElements();
    // 先預設=>設置收/付款日期預設值為當天日期，後續可能依照不同模式進行調整
    const today = CommonUtils.formatDate(new Date(), 'YYYY-MM-DD');
    paymentDateSelect.value = today;

    if(currentMode === 'Create'){
        //一般新增交易單
        await handleDefaultCreate();
    }else if(currentMode === 'Edit'){
        //編輯模式
        await handleEditTransaction(editId);
    }else if(currentMode === 'Advance'){
        //代墊款核銷轉單
       await handleAdvanceTransfer(transferData.data);
    }else if(currentMode === 'Salary'){
        //薪資轉單
        await handleSalaryTransfer(transferData.data);
    }
}
