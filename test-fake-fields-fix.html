<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>假欄位問題修正驗證</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🔧 假欄位問題修正驗證</h1>
        
        <!-- 問題說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-red-600">❌ 原有問題</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">假欄位問題</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 個別屬性區載入的都是假欄位，無實際功能</li>
                        <li>• 只有在共同區開啟/關閉欄位時才出現真欄位</li>
                        <li>• 真欄位被堆置在假欄位後方</li>
                        <li>• 假欄位是為了介面而額外增加的UI-DOM元素</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">根本原因</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 初始化時的 <code>addBatchRow()</code> 創建假欄位</li>
                        <li>• <code>updateExistingBatchRows()</code> 基於錯誤的行索引</li>
                        <li>• 兩套欄位生成邏輯並存</li>
                        <li>• 表格重建邏輯不完整</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 修正方案 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-green-600">✅ 修正方案</h2>
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">1. 統一欄位生成邏輯</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 移除舊的行重建邏輯，統一使用 createBatchRowElements
function addBatchRow() {
    rowCounter++;
    
    // 先確保表格標題已正確設置
    if (!window.tableFieldsLayout) {
        updateBatchTableColumns();
    }
    
    // 創建行容器
    createBatchRowElements(rowCounter);
}</code></pre>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">2. 修正行更新邏輯</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 基於邏輯行而非 DOM tr 元素
function updateExistingBatchRows() {
    const firstRows = document.querySelectorAll('#batchTableBody tr.batch-row-first');
    
    firstRows.forEach((firstRow) => {
        const rowNum = parseInt(firstRow.dataset.rowNum);
        // 保存值 -> 移除舊行 -> 重新創建 -> 恢復值
    });
}</code></pre>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">3. 修正初始化順序</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 確保表格結構在添加行之前已初始化
document.addEventListener('DOMContentLoaded', async () => {
    await initializePage();
    initializeBatchPage();
    setupEventListeners();
    
    // 初始化表格結構
    updateCommonPropertiesForm();
    updateBatchTableColumns();
    
    // 添加第一行
    addBatchRow();
});</code></pre>
                </div>
            </div>
        </div>
        
        <!-- 技術細節 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🔧 關鍵修正點</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">移除的問題代碼</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 舊的 <code>updateExistingBatchRows</code> 邏輯</li>
                        <li>• 重複的欄位生成代碼</li>
                        <li>• 錯誤的行索引計算</li>
                        <li>• 無用的事件監聽器函式</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">新增的正確邏輯</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• <code>createBatchRowElements</code> 統一行創建</li>
                        <li>• <code>restoreRowValues</code> 值恢復機制</li>
                        <li>• 基於 <code>data-row-num</code> 的行識別</li>
                        <li>• 正確的初始化順序</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 測試指引 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🧪 測試指引</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試步驟 1: 初始載入</h3>
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <ol class="text-sm text-blue-700 space-y-1">
                            <li>1. 開啟批次交易頁面</li>
                            <li>2. 檢查初始行的欄位是否都是真欄位</li>
                            <li>3. 測試每個欄位的輸入功能</li>
                            <li>4. 確認沒有假欄位存在</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試步驟 2: 共同屬性變更</h3>
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <ol class="text-sm text-green-700 space-y-1">
                            <li>1. 切換共同屬性的開啟/關閉</li>
                            <li>2. 檢查表格是否正確重建</li>
                            <li>3. 確認沒有重複的欄位</li>
                            <li>4. 測試欄位功能是否正常</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試步驟 3: 添加新行</h3>
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <ol class="text-sm text-yellow-700 space-y-1">
                            <li>1. 點擊添加新行按鈕</li>
                            <li>2. 檢查新行的欄位是否正確</li>
                            <li>3. 測試新行的功能</li>
                            <li>4. 確認與現有行一致</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試步驟 4: 值保持</h3>
                    <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <ol class="text-sm text-purple-700 space-y-1">
                            <li>1. 在欄位中輸入一些值</li>
                            <li>2. 切換共同屬性設置</li>
                            <li>3. 檢查值是否正確保持</li>
                            <li>4. 確認沒有資料遺失</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 預期結果 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🎯 預期結果</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-check-circle text-2xl text-green-600"></i>
                    </div>
                    <h3 class="font-medium text-gray-800 mb-2">無假欄位</h3>
                    <p class="text-sm text-gray-600">所有欄位都是真實可操作的，沒有假的UI元素</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-sync-alt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="font-medium text-gray-800 mb-2">正確重建</h3>
                    <p class="text-sm text-gray-600">共同屬性變更時表格正確重建，無重複欄位</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-save text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="font-medium text-gray-800 mb-2">值保持</h3>
                    <p class="text-sm text-gray-600">表格重建時用戶輸入的值正確保持</p>
                </div>
            </div>
        </div>
        
        <!-- 測試按鈕 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">🚀 開始測試</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    開啟批次頁面
                </button>
                <button onclick="showTestChecklist()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    <i class="fas fa-list-check mr-2"></i>
                    測試清單
                </button>
                <button onclick="showTechnicalDetails()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    <i class="fas fa-code mr-2"></i>
                    技術細節
                </button>
            </div>
            
            <!-- 測試清單 -->
            <div id="testChecklist" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg hidden">
                <h3 class="font-medium text-green-800 mb-3">📋 測試清單</h3>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-green-700">初始載入時所有欄位都是真欄位</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-green-700">共同屬性變更時表格正確重建</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-green-700">沒有重複或假的欄位</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-green-700">用戶輸入的值正確保持</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-green-700">新增行功能正常</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-green-700">所有欄位功能正常運作</span>
                    </label>
                </div>
            </div>
            
            <!-- 技術細節 -->
            <div id="technicalDetails" class="mt-6 p-4 bg-purple-50 border border-purple-200 rounded-lg hidden">
                <h3 class="font-medium text-purple-800 mb-3">💻 關鍵修正</h3>
                <ul class="text-sm text-purple-700 space-y-2">
                    <li>• <strong>統一邏輯：</strong>移除重複的欄位生成代碼</li>
                    <li>• <strong>正確識別：</strong>使用 data-row-num 而非索引</li>
                    <li>• <strong>完整重建：</strong>移除舊行後重新創建</li>
                    <li>• <strong>值保持：</strong>實現完整的值保存和恢復機制</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function openBatchPage() {
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function showTestChecklist() {
            const element = document.getElementById('testChecklist');
            element.classList.toggle('hidden');
        }
        
        function showTechnicalDetails() {
            const element = document.getElementById('technicalDetails');
            element.classList.toggle('hidden');
        }
        
        // 頁面載入時顯示成功訊息
        document.addEventListener('DOMContentLoaded', function() {
            // 顯示成功通知
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>假欄位問題已修正完成！</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            // 5秒後自動隱藏
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 5000);
        });
    </script>
</body>
</html>
