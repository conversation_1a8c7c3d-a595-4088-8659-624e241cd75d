<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能恢復測試</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🔧 功能恢復測試</h1>
        
        <!-- 緊急修復說明 -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-red-600">🚨 緊急修復</h2>
            <div class="space-y-3">
                <p class="text-red-700">
                    <strong>問題：</strong>修改後所有欄位變成假欄位，功能全部消失
                </p>
                <p class="text-red-700">
                    <strong>影響：</strong>標籤輸入、交易描述搜尋、交易對象選擇等功能都無法使用
                </p>
                <p class="text-red-700">
                    <strong>原因：</strong>移除了太多代碼，包括重要的事件監聽器設置
                </p>
            </div>
        </div>
        
        <!-- 修復方案 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-green-600">✅ 修復方案</h2>
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">1. 添加事件監聽器設置</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>function setupRowEventListeners(rowNum) {
    // 設置交易項目選擇器
    const paymentDescBtn = document.querySelector(`[data-row="${rowNum}"].payment-desc-btn`);
    if (paymentDescBtn) {
        setupPaymentDescriptionSelector(rowNum);
    }
    
    // 設置交易對象搜尋
    const entitySearchInput = document.querySelector(`[data-row="${rowNum}"].entity-search-input`);
    if (entitySearchInput) {
        setupEntitySearch(rowNum);
    }
    
    // 設置標籤輸入
    const tagsInput = document.querySelector(`[data-row="${rowNum}"].tags-input`);
    if (tagsInput) {
        setupTagsInput(rowNum);
    }
}</code></pre>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">2. 修正HTML生成</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 確保標籤輸入的name屬性正確
case 'tags':
    return `
        &lt;input type="text" class="tags-input" name="batch_tags_${rowNum}" 
               data-row="${rowNum}"&gt;
        &lt;input type="hidden" name="batch_tagsData_${rowNum}"&gt;
    `;</code></pre>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">3. 在行創建後立即設置功能</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 添加到表格後立即設置事件監聽器
tbody.appendChild(firstRow);
tbody.appendChild(secondRow);

setTimeout(() => {
    setupRowEventListeners(rowNum);
}, 0);</code></pre>
                </div>
            </div>
        </div>
        
        <!-- 檢查清單 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">📋 功能檢查清單</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">需要恢復的功能</h3>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-sm text-gray-700">標籤輸入功能（Enter鍵添加）</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-sm text-gray-700">交易描述搜尋選擇</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-sm text-gray-700">交易對象選擇功能</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-sm text-gray-700">金額輸入和稅額計算</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-sm text-gray-700">到帳情形變更響應</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-sm text-gray-700">稅別選擇和計算</span>
                        </label>
                    </div>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">檢查要點</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 檢查HTML結構是否正確生成</li>
                        <li>• 確認事件監聽器是否正確設置</li>
                        <li>• 驗證選擇器是否能找到正確元素</li>
                        <li>• 測試功能是否正常運作</li>
                        <li>• 確認沒有JavaScript錯誤</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 調試工具 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🔍 調試工具</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="checkTagsInput()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    檢查標籤輸入
                </button>
                <button onclick="checkPaymentDesc()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    檢查交易描述
                </button>
                <button onclick="checkEntitySearch()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    檢查交易對象
                </button>
                <button onclick="checkEventListeners()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    檢查事件監聽器
                </button>
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    開啟批次頁面
                </button>
                <button onclick="openConsole()" 
                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    開啟控制台
                </button>
            </div>
        </div>
        
        <!-- 調試結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">📊 調試結果</h2>
            <div id="debugResults" class="space-y-2 max-h-96 overflow-y-auto">
                <p class="text-gray-600">點擊上方按鈕開始調試...</p>
            </div>
        </div>
    </div>

    <script>
        function addDebugResult(message, type = 'info') {
            const resultsDiv = document.getElementById('debugResults');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor}`;
            resultItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span class="text-sm">${message}</span>
                    <span class="ml-auto text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            
            resultsDiv.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        function checkTagsInput() {
            addDebugResult('檢查標籤輸入功能...', 'info');
            addDebugResult('請在批次頁面中測試：', 'info');
            addDebugResult('1. 找到標籤輸入欄位', 'info');
            addDebugResult('2. 輸入文字後按Enter', 'info');
            addDebugResult('3. 檢查是否出現標籤', 'info');
        }
        
        function checkPaymentDesc() {
            addDebugResult('檢查交易描述功能...', 'info');
            addDebugResult('請在批次頁面中測試：', 'info');
            addDebugResult('1. 點擊"選擇交易項目"按鈕', 'info');
            addDebugResult('2. 檢查是否出現下拉選單', 'info');
            addDebugResult('3. 測試選擇功能', 'info');
        }
        
        function checkEntitySearch() {
            addDebugResult('檢查交易對象功能...', 'info');
            addDebugResult('請在批次頁面中測試：', 'info');
            addDebugResult('1. 在交易對象欄位輸入文字', 'info');
            addDebugResult('2. 檢查是否出現搜尋結果', 'info');
            addDebugResult('3. 測試選擇功能', 'info');
        }
        
        function checkEventListeners() {
            addDebugResult('檢查事件監聽器...', 'info');
            addDebugResult('請開啟瀏覽器控制台檢查：', 'info');
            addDebugResult('1. 是否有JavaScript錯誤', 'error');
            addDebugResult('2. 事件監聽器是否正確設置', 'warning');
            addDebugResult('3. 選擇器是否能找到元素', 'warning');
        }
        
        function openBatchPage() {
            addDebugResult('開啟批次交易頁面進行測試...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function openConsole() {
            addDebugResult('請按F12開啟瀏覽器開發者工具', 'info');
            addDebugResult('在Console標籤中檢查錯誤訊息', 'warning');
        }
        
        // 頁面載入時顯示警告訊息
        document.addEventListener('DOMContentLoaded', function() {
            // 顯示警告通知
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <span>功能修復中，請進行測試驗證！</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            // 10秒後自動隱藏
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 10000);
        });
    </script>
</body>
</html>
