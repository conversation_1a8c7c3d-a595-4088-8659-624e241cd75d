/**
 * @file batchTransaction_UI.js
 * @description 批次交易 UI 管理模組
 * 負責處理批次交易介面的動態生成和互動
 */

/**
 * 創建批次資料行
 * @param {number} rowIndex - 行索引
 * @returns {HTMLElement} 創建的行元素
 */
function createBatchRow(rowIndex) {
    const row = document.createElement('tr');
    row.id = `batchRow_${rowIndex}`;
    row.dataset.rowIndex = rowIndex;
    
    // 行號欄
    const indexCell = document.createElement('td');
    indexCell.className = 'text-center font-medium';
    indexCell.textContent = rowIndex + 1;
    row.appendChild(indexCell);
    
    // 交易對象欄
    const entityCell = document.createElement('td');
    entityCell.appendChild(createEntitySearchElement(rowIndex));
    row.appendChild(entityCell);
    
    // 交易項目欄
    const paymentDescCell = document.createElement('td');
    paymentDescCell.appendChild(createPaymentDescriptionElement(rowIndex));
    row.appendChild(paymentDescCell);
    
    // 金額欄
    const amountCell = document.createElement('td');
    amountCell.appendChild(createAmountElement(rowIndex));
    row.appendChild(amountCell);
    
    // 稅額欄
    const taxAmountCell = document.createElement('td');
    taxAmountCell.appendChild(createTaxAmountElement(rowIndex));
    row.appendChild(taxAmountCell);
    
    // 發票號碼欄
    const invoiceNumberCell = document.createElement('td');
    invoiceNumberCell.appendChild(createInvoiceNumberElement(rowIndex));
    row.appendChild(invoiceNumberCell);
    
    // 備註欄
    const notesCell = document.createElement('td');
    notesCell.appendChild(createNotesElement(rowIndex));
    row.appendChild(notesCell);
    
    // 操作欄
    const actionCell = document.createElement('td');
    actionCell.className = 'text-center';
    actionCell.innerHTML = `
        <button type="button" onclick="removeBatchRow(${rowIndex})" 
            class="btn-danger" title="刪除此行">
            <i class="fas fa-trash"></i>
        </button>
    `;
    row.appendChild(actionCell);
    
    return row;
}

/**
 * 創建交易對象搜尋元素
 * @param {number} rowIndex - 行索引
 * @returns {HTMLElement} 交易對象搜尋元素
 */
function createEntitySearchElement(rowIndex) {
    const container = document.createElement('div');
    container.className = 'relative';
    
    const input = document.createElement('input');
    input.type = 'text';
    input.id = `entity_${rowIndex}`;
    input.className = 'batch-input';
    input.placeholder = '搜尋交易對象';
    input.required = true;
    
    const resultsDiv = document.createElement('div');
    resultsDiv.id = `entity_${rowIndex}_results`;
    resultsDiv.className = 'dropdown-menu hidden';
    
    const hiddenIdInput = document.createElement('input');
    hiddenIdInput.type = 'hidden';
    hiddenIdInput.id = `entity_${rowIndex}_id`;
    
    const hiddenTypeInput = document.createElement('input');
    hiddenTypeInput.type = 'hidden';
    hiddenTypeInput.id = `entity_${rowIndex}_type`;
    
    container.appendChild(input);
    container.appendChild(resultsDiv);
    container.appendChild(hiddenIdInput);
    container.appendChild(hiddenTypeInput);
    
    // 添加搜尋事件監聽
    input.addEventListener('input', function() {
        handleEntitySearch(this.value, rowIndex);
    });
    
    input.addEventListener('focus', function() {
        handleEntitySearch('', rowIndex);
    });
    
    return container;
}

/**
 * 創建交易項目選擇元素
 * @param {number} rowIndex - 行索引
 * @returns {HTMLElement} 交易項目選擇元素
 */
function createPaymentDescriptionElement(rowIndex) {
    const container = document.createElement('div');
    container.className = 'relative';
    
    const button = document.createElement('button');
    button.type = 'button';
    button.id = `paymentDescription_${rowIndex}_btn`;
    button.className = 'batch-input dropdown-trigger text-left';
    button.innerHTML = '<span>選擇交易項目</span><i class="fas fa-chevron-down float-right mt-0.5"></i>';
    
    const dropdown = document.createElement('div');
    dropdown.id = `paymentDescription_${rowIndex}_dropdown`;
    dropdown.className = 'dropdown-menu hidden';
    dropdown.style.width = '300px';
    dropdown.style.maxHeight = '250px';
    
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.id = `paymentDescription_${rowIndex}`;
    hiddenInput.required = true;
    
    const hiddenNameInput = document.createElement('input');
    hiddenNameInput.type = 'hidden';
    hiddenNameInput.id = `paymentDescription_${rowIndex}_name`;
    
    container.appendChild(button);
    container.appendChild(dropdown);
    container.appendChild(hiddenInput);
    container.appendChild(hiddenNameInput);
    
    // 添加點擊事件監聽
    button.addEventListener('click', function(e) {
        e.stopPropagation();
        togglePaymentDescriptionDropdown(rowIndex);
    });
    
    return container;
}

/**
 * 創建金額輸入元素
 * @param {number} rowIndex - 行索引
 * @returns {HTMLElement} 金額輸入元素
 */
function createAmountElement(rowIndex) {
    const container = document.createElement('div');
    container.className = 'relative';
    
    const dollarSign = document.createElement('span');
    dollarSign.className = 'absolute left-2 top-2 text-gray-500 text-sm';
    dollarSign.textContent = '$';
    
    const input = document.createElement('input');
    input.type = 'number';
    input.id = `amount_${rowIndex}`;
    input.className = 'batch-input pl-6';
    input.min = '0';
    input.step = '0.01';
    input.placeholder = '含稅金額';
    input.required = true;
    
    // 添加輸入事件監聽，自動計算稅額
    input.addEventListener('input', function() {
        calculateTaxAmount(rowIndex);
    });
    
    container.appendChild(dollarSign);
    container.appendChild(input);
    
    return container;
}

/**
 * 創建稅額顯示元素
 * @param {number} rowIndex - 行索引
 * @returns {HTMLElement} 稅額顯示元素
 */
function createTaxAmountElement(rowIndex) {
    const input = document.createElement('input');
    input.type = 'number';
    input.id = `taxAmount_${rowIndex}`;
    input.className = 'batch-input bg-gray-50';
    input.min = '0';
    input.step = '0.01';
    input.placeholder = '自動計算';
    input.readOnly = true;
    
    return input;
}

/**
 * 創建發票號碼輸入元素
 * @param {number} rowIndex - 行索引
 * @returns {HTMLElement} 發票號碼輸入元素
 */
function createInvoiceNumberElement(rowIndex) {
    const input = document.createElement('input');
    input.type = 'text';
    input.id = `invoiceNumber_${rowIndex}`;
    input.className = 'batch-input';
    input.placeholder = '發票號碼';
    
    return input;
}

/**
 * 創建備註輸入元素
 * @param {number} rowIndex - 行索引
 * @returns {HTMLElement} 備註輸入元素
 */
function createNotesElement(rowIndex) {
    const textarea = document.createElement('textarea');
    textarea.id = `notes_${rowIndex}`;
    textarea.className = 'batch-input';
    textarea.rows = 2;
    textarea.placeholder = '備註';
    
    return textarea;
}

/**
 * 初始化行功能
 * @param {number} rowIndex - 行索引
 */
function initializeRowFeatures(rowIndex) {
    // 初始化交易項目選單
    loadPaymentDescriptionOptions(rowIndex);
    
    // 設定預設稅額計算
    calculateTaxAmount(rowIndex);
}

/**
 * 計算稅額
 * @param {number} rowIndex - 行索引
 */
function calculateTaxAmount(rowIndex) {
    const amountInput = document.getElementById(`amount_${rowIndex}`);
    const taxAmountInput = document.getElementById(`taxAmount_${rowIndex}`);
    const taxTypeSelect = document.getElementById('batchTaxTypeSelect');
    
    if (!amountInput || !taxAmountInput || !taxTypeSelect) return;
    
    const amount = parseFloat(amountInput.value) || 0;
    const selectedOption = taxTypeSelect.options[taxTypeSelect.selectedIndex];
    const taxRate = parseFloat(selectedOption.dataset.rate) || 0;
    
    if (amount > 0 && taxRate > 0) {
        // 使用與原系統相同的稅額計算邏輯（含稅金額反推稅額）
        const amountNoTax = Math.round(amount / (1 + Number(taxRate)));
        const taxAmount = amount - amountNoTax;
        taxAmountInput.value = taxAmount.toFixed(2);
    } else {
        taxAmountInput.value = '';
    }
}

/**
 * 處理交易對象搜尋
 * @param {string} searchTerm - 搜尋關鍵字
 * @param {number} rowIndex - 行索引
 */
async function handleEntitySearch(searchTerm, rowIndex) {
    const resultsDiv = document.getElementById(`entity_${rowIndex}_results`);
    
    if (!searchTerm || searchTerm.length < 1) {
        // 顯示最近使用的選項
        try {
            const employees = await getEmployeesAll();
            const entities = await getEntitiesAll();
            
            const limitedEmployees = employees.slice(0, 3);
            const limitedEntities = entities.slice(0, 3);
            
            showEntitySearchResults(rowIndex, limitedEmployees, limitedEntities);
        } catch (error) {
            console.error('載入交易對象失敗:', error);
            hideEntitySearchResults(rowIndex);
        }
        return;
    }
    
    if (searchTerm.length < 2) {
        hideEntitySearchResults(rowIndex);
        return;
    }
    
    try {
        // 搜尋員工
        const employees = await getEmployeesAll();
        const matchedEmployees = employees.filter(emp => 
            emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            emp.code.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 5);
        
        // 搜尋交易對象
        const entities = await getEntitiesAll();
        const matchedEntities = entities.filter(entity => 
            entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entity.code.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 5);
        
        // 顯示搜尋結果
        showEntitySearchResults(rowIndex, matchedEmployees, matchedEntities);
        
    } catch (error) {
        console.error('搜尋交易對象失敗:', error);
        hideEntitySearchResults(rowIndex);
    }
}

/**
 * 顯示交易對象搜尋結果
 * @param {number} rowIndex - 行索引
 * @param {Array} employees - 員工陣列
 * @param {Array} entities - 交易對象陣列
 */
function showEntitySearchResults(rowIndex, employees, entities) {
    const resultsDiv = document.getElementById(`entity_${rowIndex}_results`);
    if (!resultsDiv) return;
    
    resultsDiv.innerHTML = '';
    
    // 添加員工結果
    if (employees.length > 0) {
        const employeeHeader = document.createElement('div');
        employeeHeader.className = 'px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700';
        employeeHeader.textContent = '員工';
        resultsDiv.appendChild(employeeHeader);
        
        employees.forEach(employee => {
            const item = document.createElement('div');
            item.className = 'dropdown-item';
            item.innerHTML = `
                <div class="text-sm font-medium">${employee.name}</div>
                <div class="text-xs text-gray-500">${employee.code}</div>
            `;
            item.onclick = () => selectEntity(rowIndex, employee.id, 'employee', employee.name);
            resultsDiv.appendChild(item);
        });
    }
    
    // 添加交易對象結果
    if (entities.length > 0) {
        const entityHeader = document.createElement('div');
        entityHeader.className = 'px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700';
        entityHeader.textContent = '交易對象';
        resultsDiv.appendChild(entityHeader);
        
        entities.forEach(entity => {
            const item = document.createElement('div');
            item.className = 'dropdown-item';
            item.innerHTML = `
                <div class="text-sm font-medium">${entity.name}</div>
                <div class="text-xs text-gray-500">${entity.code}</div>
            `;
            item.onclick = () => selectEntity(rowIndex, entity.id, 'entity', entity.name);
            resultsDiv.appendChild(item);
        });
    }
    
    if (employees.length === 0 && entities.length === 0) {
        const noResult = document.createElement('div');
        noResult.className = 'dropdown-item text-gray-500';
        noResult.textContent = '找不到相符的結果';
        resultsDiv.appendChild(noResult);
    }
    
    resultsDiv.classList.remove('hidden');
}

/**
 * 隱藏交易對象搜尋結果
 * @param {number} rowIndex - 行索引
 */
function hideEntitySearchResults(rowIndex) {
    const resultsDiv = document.getElementById(`entity_${rowIndex}_results`);
    if (resultsDiv) {
        resultsDiv.classList.add('hidden');
    }
}

/**
 * 選擇交易對象
 * @param {number} rowIndex - 行索引
 * @param {string} entityId - 交易對象 ID
 * @param {string} entityType - 交易對象類型
 * @param {string} entityName - 交易對象名稱
 */
function selectEntity(rowIndex, entityId, entityType, entityName) {
    const input = document.getElementById(`entity_${rowIndex}`);
    const idInput = document.getElementById(`entity_${rowIndex}_id`);
    const typeInput = document.getElementById(`entity_${rowIndex}_type`);
    
    if (input && idInput && typeInput) {
        input.value = entityName;
        idInput.value = entityId;
        typeInput.value = entityType;
        
        // 移除錯誤樣式
        input.classList.remove('error-input');
    }
    
    hideEntitySearchResults(rowIndex);
}
