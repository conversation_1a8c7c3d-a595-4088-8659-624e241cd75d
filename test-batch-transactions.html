<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次交易功能測試</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">批次交易功能測試</h1>
        
        <!-- 測試按鈕 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">功能測試</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <button onclick="testBasicFunctions()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    測試基本函式
                </button>
                <button onclick="testDataLoading()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    測試資料載入
                </button>
                <button onclick="testValidation()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    測試資料驗證
                </button>
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    開啟批次頁面
                </button>
                <button onclick="clearResults()" 
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    清除結果
                </button>
            </div>
        </div>
        
        <!-- 測試結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">測試結果</h2>
            <div id="testResults" class="space-y-2">
                <p class="text-gray-600">點擊上方按鈕開始測試...</p>
            </div>
        </div>
    </div>

    <!-- 載入必要的腳本 -->
    <script src="../../common/firebaseAPI/auth.js"></script>
    <script src="../../common/db/db.js"></script>
    <script src="../../common/db/preload.js"></script>
    <script src="../../common/utils/CommonUtils.js"></script>
    <script src="../../common/utils/pageTransfer.js"></script>
    <script src="../../common/utils/ModalUtils.js"></script>
    <script src="../../common/utils/DatabaseErrors.js"></script>
    
    <!-- 現有模組 -->
    <script src="transactionCreate_Service.js"></script>
    <script src="transactionCreate_JournalManagement_Module.js"></script>
    
    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor}`;
            resultItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            
            resultsDiv.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-600">測試結果已清除</p>';
        }
        
        async function testBasicFunctions() {
            addTestResult('開始測試基本函式...', 'info');
            
            try {
                // 測試是否能訪問基本函式
                if (typeof getAccountsAll === 'function') {
                    addTestResult('✓ getAccountsAll 函式可用', 'success');
                } else {
                    addTestResult('✗ getAccountsAll 函式不可用', 'error');
                }
                
                if (typeof getTaxTypesRatesAll === 'function') {
                    addTestResult('✓ getTaxTypesRatesAll 函式可用', 'success');
                } else {
                    addTestResult('✗ getTaxTypesRatesAll 函式不可用', 'error');
                }
                
                if (typeof saveTransactionToDB === 'function') {
                    addTestResult('✓ saveTransactionToDB 函式可用', 'success');
                } else {
                    addTestResult('✗ saveTransactionToDB 函式不可用', 'error');
                }
                
                if (typeof Journal !== 'undefined') {
                    addTestResult('✓ Journal 類別可用', 'success');
                } else {
                    addTestResult('✗ Journal 類別不可用', 'error');
                }
                
                addTestResult('基本函式測試完成', 'info');
                
            } catch (error) {
                addTestResult(`基本函式測試失敗: ${error.message}`, 'error');
            }
        }
        
        async function testDataLoading() {
            addTestResult('開始測試資料載入...', 'info');
            
            try {
                // 測試帳戶資料載入
                const accounts = await getAccountsAll();
                addTestResult(`✓ 成功載入 ${accounts.length} 個帳戶`, 'success');
                
                // 測試稅別資料載入
                const taxTypes = await getTaxTypesRatesAll();
                addTestResult(`✓ 成功載入 ${taxTypes.length} 個稅別`, 'success');
                
                // 測試交易分類載入
                const categories = await getTransactionCategoriesAll();
                addTestResult(`✓ 成功載入 ${categories.length} 個交易分類`, 'success');
                
                addTestResult('資料載入測試完成', 'info');
                
            } catch (error) {
                addTestResult(`資料載入測試失敗: ${error.message}`, 'error');
            }
        }
        
        async function testValidation() {
            addTestResult('開始測試資料驗證...', 'info');
            
            try {
                // 測試空資料驗證
                const emptyData = {
                    transactionType: '',
                    accountId: '',
                    amount: 0
                };
                
                addTestResult('測試空資料驗證邏輯...', 'info');
                
                // 模擬驗證邏輯
                const errors = [];
                if (!emptyData.transactionType) errors.push('交易類型為必填');
                if (!emptyData.accountId) errors.push('帳戶為必填');
                if (!emptyData.amount || emptyData.amount <= 0) errors.push('金額必須大於0');
                
                if (errors.length > 0) {
                    addTestResult(`✓ 驗證邏輯正常，發現 ${errors.length} 個錯誤`, 'success');
                    errors.forEach(error => addTestResult(`  - ${error}`, 'warning'));
                } else {
                    addTestResult('✗ 驗證邏輯異常', 'error');
                }
                
                addTestResult('資料驗證測試完成', 'info');
                
            } catch (error) {
                addTestResult(`資料驗證測試失敗: ${error.message}`, 'error');
            }
        }
        
        function openBatchPage() {
            addTestResult('開啟批次交易頁面...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        // 頁面載入時自動執行基本測試
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('測試頁面載入完成', 'success');
            addTestResult('可以開始進行功能測試', 'info');
        });
    </script>
</body>
</html>
