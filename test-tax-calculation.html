<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>稅額自動計算功能測試</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">稅額自動計算功能測試</h1>
        
        <!-- 測試控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">測試控制面板</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="testBasicCalculation()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    基本計算測試
                </button>
                <button onclick="testBatchCalculation()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    批次計算測試
                </button>
                <button onclick="testTaxRateChange()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    稅率變更測試
                </button>
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    開啟批次頁面
                </button>
                <button onclick="clearResults()" 
                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    清除結果
                </button>
                <button onclick="runAllTests()" 
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    執行所有測試
                </button>
            </div>
        </div>
        
        <!-- 稅額計算示範 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">稅額計算示範</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">含稅金額</label>
                    <input type="number" id="demoAmount" class="w-full p-2 border rounded" 
                           placeholder="輸入含稅金額" step="0.01" min="0">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">稅率</label>
                    <select id="demoTaxRate" class="w-full p-2 border rounded">
                        <option value="0">免稅 (0%)</option>
                        <option value="0.05" selected>營業稅 (5%)</option>
                        <option value="0.1">其他稅 (10%)</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">計算結果</label>
                    <div class="p-2 bg-gray-50 border rounded">
                        <div id="demoResult" class="text-sm">
                            <div>未稅金額: <span id="demoAmountNoTax">0</span></div>
                            <div>稅額: <span id="demoTaxAmount">0</span></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <button onclick="calculateDemo()" 
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                手動計算
            </button>
        </div>
        
        <!-- 測試結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">測試結果</h2>
            <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto">
                <p class="text-gray-600">點擊上方按鈕開始測試...</p>
            </div>
        </div>
    </div>

    <script>
        // 稅額計算函式（與系統相同的邏輯）
        function calculateTaxAmount(amount, taxRate) {
            const amountNoTax = Math.round(amount / (1 + Number(taxRate)));
            const taxAmount = amount - amountNoTax;
            return { amountNoTax, taxAmount };
        }
        
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor}`;
            resultItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span class="text-sm">${message}</span>
                    <span class="ml-auto text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            
            resultsDiv.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-600">測試結果已清除</p>';
        }
        
        function calculateDemo() {
            const amount = parseFloat(document.getElementById('demoAmount').value) || 0;
            const taxRate = parseFloat(document.getElementById('demoTaxRate').value) || 0;
            
            if (amount <= 0) {
                addTestResult('請輸入有效的金額', 'warning');
                return;
            }
            
            const result = calculateTaxAmount(amount, taxRate);
            
            document.getElementById('demoAmountNoTax').textContent = result.amountNoTax.toFixed(2);
            document.getElementById('demoTaxAmount').textContent = result.taxAmount.toFixed(2);
            
            addTestResult(`計算完成：含稅 ${amount} → 未稅 ${result.amountNoTax.toFixed(2)} + 稅額 ${result.taxAmount.toFixed(2)}`, 'success');
        }
        
        function testBasicCalculation() {
            addTestResult('開始基本計算測試...', 'info');
            
            const testCases = [
                { amount: 1050, taxRate: 0.05, expectedNoTax: 1000, expectedTax: 50 },
                { amount: 1100, taxRate: 0.1, expectedNoTax: 1000, expectedTax: 100 },
                { amount: 525, taxRate: 0.05, expectedNoTax: 500, expectedTax: 25 },
                { amount: 1000, taxRate: 0, expectedNoTax: 1000, expectedTax: 0 }
            ];
            
            let passCount = 0;
            
            testCases.forEach((testCase, index) => {
                const result = calculateTaxAmount(testCase.amount, testCase.taxRate);
                
                if (Math.abs(result.amountNoTax - testCase.expectedNoTax) <= 1 && 
                    Math.abs(result.taxAmount - testCase.expectedTax) <= 1) {
                    addTestResult(`✓ 測試案例 ${index + 1} 通過`, 'success');
                    passCount++;
                } else {
                    addTestResult(`✗ 測試案例 ${index + 1} 失敗：預期未稅 ${testCase.expectedNoTax}，實際 ${result.amountNoTax}`, 'error');
                }
            });
            
            addTestResult(`基本計算測試完成 (${passCount}/${testCases.length})`, passCount === testCases.length ? 'success' : 'warning');
        }
        
        function testBatchCalculation() {
            addTestResult('開始批次計算測試...', 'info');
            
            // 模擬批次計算
            const amounts = [1050, 2100, 525, 1000, 3150];
            const taxRate = 0.05;
            
            let totalOriginal = 0;
            let totalNoTax = 0;
            let totalTax = 0;
            
            amounts.forEach((amount, index) => {
                const result = calculateTaxAmount(amount, taxRate);
                totalOriginal += amount;
                totalNoTax += result.amountNoTax;
                totalTax += result.taxAmount;
                
                addTestResult(`批次項目 ${index + 1}：${amount} → 未稅 ${result.amountNoTax} + 稅額 ${result.taxAmount}`, 'info');
            });
            
            addTestResult(`批次計算完成：總含稅 ${totalOriginal}，總未稅 ${totalNoTax}，總稅額 ${totalTax}`, 'success');
        }
        
        function testTaxRateChange() {
            addTestResult('開始稅率變更測試...', 'info');
            
            const amount = 1050;
            const taxRates = [0, 0.05, 0.1, 0.15];
            
            taxRates.forEach(rate => {
                const result = calculateTaxAmount(amount, rate);
                const percentage = (rate * 100).toFixed(0);
                addTestResult(`稅率 ${percentage}%：${amount} → 未稅 ${result.amountNoTax} + 稅額 ${result.taxAmount}`, 'info');
            });
            
            addTestResult('稅率變更測試完成', 'success');
        }
        
        async function runAllTests() {
            addTestResult('開始執行所有測試...', 'info');
            
            testBasicCalculation();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testBatchCalculation();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testTaxRateChange();
            
            addTestResult('所有測試執行完成！', 'success');
        }
        
        function openBatchPage() {
            addTestResult('開啟批次交易頁面...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        // 自動計算示範
        document.getElementById('demoAmount').addEventListener('input', calculateDemo);
        document.getElementById('demoTaxRate').addEventListener('change', calculateDemo);
        
        // 頁面載入時自動執行基本測試
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('稅額計算測試頁面載入完成', 'success');
            addTestResult('可以開始進行功能測試', 'info');
            
            // 設定示範預設值
            document.getElementById('demoAmount').value = 1050;
            calculateDemo();
        });
    </script>
</body>
</html>
