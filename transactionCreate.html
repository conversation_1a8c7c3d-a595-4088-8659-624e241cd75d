<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增交易 - 益芯能源工程-管理系統</title>
    <link rel="icon" href="../../common/img/logo.png" type="image/x-icon">

    <!-- 模組導入映射設定 -->
    <script type="importmap">
    {
        "imports": {
        "@common/": "/common/"
        }
    }
    </script>

    <!-- 樣式庫載入 -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script> <!-- Tailwind CSS 框架 -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <!-- Font Awesome 圖示庫 -->

    <!-- Firebase 相關庫載入 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-app-compat.js"></script> <!-- Firebase 核心 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-analytics-compat.js"></script> <!-- Firebase 分析 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-firestore-compat.js"></script> <!-- Firebase 資料庫 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-auth-compat.js"></script> <!-- Firebase 認證 -->

    <!-- 自定義工具庫載入 -->
    <script src="../../common/firebaseAPI/auth.js"></script> <!-- 認證相關功能 -->
    <script src="../../common/db/db.js"></script> <!-- 資料庫操作功能 -->
    <script src="../../common/db/preload.js"></script> <!-- 資料預載功能 -->
    <script src="../../common/utils/CommonUtils.js"></script> <!-- 通用工具函數 -->
    <script src="../../common/utils/pageTransfer.js"></script> <!-- 頁面轉換工具 -->
    <script src="../../common/utils/ModalUtils.js"></script> <!-- 模態框工具 -->
    <script src="../../common/utils/DatabaseErrors.js"></script> <!-- 資料庫錯誤處理 -->

    <!-- 自定義樣式 -->
    <style>
        /* 導航選單懸停效果 */
        .nav-item:hover>.submenu {
            display: block; /* 懸停時顯示子選單 */
        }

        /* 子選單基本樣式 */
        .submenu {
            display: none; /* 預設隱藏 */
            position: absolute; /* 絕對定位 */
            background-color: white; /* 白色背景 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 陰影效果 */
            z-index: 1000; /* 層級設定 */
            top: 100%; /* 位於父元素下方 */
            left: 0; /* 左對齊 */
        }

        /* 多層子選單定位 */
        .submenu .submenu {
            top: 0; /* 與父選單同高 */
            left: 100%; /* 位於父選單右側 */
        }

        /* 導航項目相對定位 */
        .nav-item {
            position: relative; /* 相對定位以支援子選單 */
        }
    </style>

    <!-- 導航功能載入 -->
    <script src="../../common/navigation/navigation.js"></script>

</head>

<body class="bg-gray-100">

    <main class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">新增交易</h1>
            <p class="text-gray-600">填寫交易資訊</p>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-3 p-2">
            <!-- 交易表單 -->
            <div class="bg-white rounded-lg shadow-md p-6 col-span-2 m-4">
                <form id="transactionForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">


                        <!-- 交易類型 -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">交易類型</label>
                            <div class="btn-check-group">
                                <div class="flex flex-row">
                                    <div class="p-2">
                                        <input type="radio" id="transactionType_expense" name="transactionType" value="expense"
                                            class="hidden peer" checked>
                                        <label for="transactionType_expense"
                                            class="inline-flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-5 py-1 text-red-500 peer-checked:bg-red-400 peer-checked:border-2 peer-checked:text-white  hover:border-red-200">
                                            <div class="block">
                                                <div class="w-full text-lg font-semibold">支出</div>
                                            </div>
                                        </label>
                                    </div>
                                    <div class="p-2">
                                        <input type="radio" id="transactionType_income" name="transactionType" value="income"
                                            class="hidden peer">
                                        <label for="transactionType_income"
                                            class="inline-flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-5 py-1 text-blue-500 peer-checked:bg-blue-400 peer-checked:border-2 peer-checked:text-white  hover:border-blue-200">
                                            <div class="block">
                                                <div class="w-full text-lg font-semibold">收入</div>
                                            </div>
                                        </label>
                                    </div>
                                    <div class="p-2">
                                        <input type="radio" id="transactionType_transfer" name="transactionType" value="transfer"
                                            class="hidden peer">
                                        <label for="transactionType_transfer"
                                            class="inline-flex cursor-pointer items-center justify-between rounded-lg border border-gray-200 bg-white px-5 py-1 text-green-500 peer-checked:bg-green-400 peer-checked:border-2 peer-checked:text-white  hover:border-green-200">
                                            <div class="block">
                                                <div class="w-full text-lg font-semibold">轉移</div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- 資金帳戶 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700"><span class="text-red-500">*</span>我方主要帳戶</label>
                            <select id="accountSelect" required
                                class="mt-1 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">請選擇帳戶</option>
                                <!-- 帳戶選項將從基礎資料動態載入 -->
                            </select>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 帳款到帳情形 -->
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700"><span class="text-red-500">*</span>帳款到帳情形</label>
                                <select id="paymentStatusSelect" required
                                    class="mt-1 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">請選擇狀態</option>
                                    <option value="same_day">同日收付款（現金基礎）</option>
                                    <option value="receivable">應收付款（權責基礎）（已開立發票未收款）</option>
                                    <option value="prepayment">暫收付款（權責基礎）（未開立發票已收款）</option>
                                    <option value="different_day">非同日收款（現金基礎）(補未紀錄之已收款項)</option>
                                </select>
                            </div>

                            <!-- 收款/付款日期 -->
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">收款/付款日期</label>
                                <input type="date" id="paymentDate" required
                                    class="mt-1 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>

                            <!-- 憑證/發票日期 -->
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">憑證/發票日期</label>
                                <input type="date" id="invoiceDate"
                                    class="mt-1 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>

                            <!-- 預計收/付款日期 (預設隱藏) -->
                            <div class="md:col-span-2 hidden" id="expectedPaymentDateContainer">
                                <label class="block text-sm font-medium text-gray-700">預計收/付款日期</label>
                                <input type="date" id="expectedPaymentDate"
                                    class="mt-1 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>

                        </div>
                        <!-- 交易項目（Mega Menu） -->
                        <div class="md:col-span-2" id="paymentDescriptionMegaMenuContainer">
                            <label class="block text-sm font-medium text-gray-700"><span class="text-red-500">*</span>交易項目</label>
                            <div id="paymentDescriptionMegaMenu" class="relative inline-block w-full">
                                <button id="paymentDescriptionMenuBtn" type="button" class="w-full text-left px-4 py-2 border rounded bg-white shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <span id="paymentDescriptionSelected">請選擇交易項目</span>
                                    <i class="fas fa-chevron-down float-right mt-1"></i>
                                </button>
                                <div id="paymentDescriptionMenuDropdown" class="absolute z-20 left-0 mt-1 w-full bg-white border rounded shadow-lg hidden"></div>
                            </div>
                            <input type="hidden" id="paymentDescription" name="paymentDescription" value="">
                        </div>

                        <!-- 交易對象/帳戶 -->
                        <div>
                            <label for="entitySearch" class="block text-sm font-medium text-gray-700">交易對象/帳戶</label>
                            <div class="relative">
                                <input type="text" id="entitySearch" placeholder="輸入名稱或代碼搜尋"
                                    class="p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <div id="searchResults" class="absolute z-10 w-full bg-white shadow-lg rounded-md mt-1 hidden">
                                    <!-- 搜尋結果將在此動態顯示 -->
                                </div>
                                <div id="selectedEntityContainer" class="">
                                    <!-- 選中的交易對象將在此顯示為badge -->
                                </div>
                            </div>
                            <input type="hidden" required id="selectedEntityId">
                            <input type="hidden" required id="selectedEntityType">
                        </div>





                        <!-- 金額 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700"><span class="text-red-500">*</span>金額</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">$</span>
                                </div>
                                <input id="amount" type="number" required min="0" placeholder="0"
                                    class="pl-7 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- 明細開關 -->
                        <div class="flex flex-row md:col-span-2">
                            <label class="block flex items-center text-sm font-medium text-gray-700">明細輸入</label>
                            <div class="flex items-center justify-between p-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="detailSwitch" class="sr-only peer" onchange="toggleDetailSection(true)">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    <span class="ml-3 text-xs font-medium text-gray-700">※可選擇是否啟用明細輸入</span>
                                </label>
                            </div>
                        </div>

                        <!-- 明細表格區域 -->
                        <div id="detailSection" class="hidden md:col-span-2 space-y-4">
                            <div class="overflow-x-auto border rounded-lg">
                                <div class="inline-block min-w-full align-middle">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#編號</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 200px;">項目</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">單價</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">數量</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">單位</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 150px;">複價</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody id="detailTableBody" class="bg-white divide-y divide-gray-200">
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="7" class="px-4 py-2">
                                                    <button type="button" onclick="addDetailRow()" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                                                        新增明細項目
                                                    </button>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 稅別 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700"><span class="text-red-500">*</span>稅別</label>
                            <select id="taxTypeSelect" required
                                class="mt-1 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">請選擇稅別</option>
                                <!-- 稅別選項將從基礎資料動態載入 -->
                            </select>
                        </div>

                        <!-- 稅額 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700"><span class="text-red-500">*</span>稅額</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">$</span>
                                </div>
                                <input id="taxAmount" type="number" min="0" placeholder="0" disabled
                                    class="pl-7 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- 發票號碼 -->
                        <div id="invoiceNumberContainer" class="md:col-span-2 hidden">
                            <label class="block text-sm font-medium text-gray-700">發票號碼</label>
                            <input id="invoiceNumber" type="text" placeholder="請輸入發票號碼"
                                class="p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>

                        <!-- 手續費 Toggle -->
                        <div class="flex flex-row md:col-span-2">
                            <label class="block flex items-center text-sm font-medium text-gray-700">手續費</label>
                            <div class="flex items-center justify-between p-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="feeToggle" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                            <!-- 手續費輸入欄位 (預設關閉)-->
                            <div id="feeInputContainer" class="mt-1 hidden">

                                <div class="relative rounded-md shadow-sm mt-1">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input type="number" id="fee" min="0" step="0.01" placeholder="0.00"
                                        class="pl-7 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>


                        <!-- 備註 -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">備註</label>
                            <textarea rows="3" id="notes" placeholder="請輸入備註（選填）"
                                class="mt-1 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                        </div>
                        <!-- 標籤 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">標籤</label>
                            <div class="relative">
                                <input type="text" id="tagInput" placeholder="輸入標籤後按Enter"
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div id="tagContainer" class="flex flex-wrap gap-2">
                                <!-- 標籤將在此動態顯示 -->
                            </div>
                        </div>
                        <!-- 狀態 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700"><span class="text-red-500">*</span>狀態</label>
                            <select id="transactionStatusSelect" required
                                class="mt-1 p-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">請選擇狀態</option>
                                <option value="completed">已完成</option>
                                <option value="pending">未撥款</option>
                            </select>
                        </div>


                    </div>

                    <!-- 按鈕 -->
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="handleCancel()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            儲存
                        </button>
                    </div>
                </form>
            </div>

            <!-- 預覽區域 -->
            <div class="col-span-1">

                <!-- 顯示借貸對象區域 -->
                <div class="bg-white rounded-lg shadow-md p-2 col-span-1 m-4 relative">
                    <!-- 標題 -->
                    <div class="absolute top-0 right-0 w-full flex flex-row justify-between">
                        <div class="flex flex-row justify-between items-center">
                            <h2 class="text-xl font-bold text-green-900 m-2">借方(進帳)</h2>
                            <i class="fas fa-arrow-left text-green-900 m-2"></i>
                        </div>
                        <div class="flex flex-row justify-between items-center">
                            <i class="fas fa-arrow-left text-red-900 m-2"></i>
                            <h2 class="text-xl font-bold text-red-900 m-2">貸方(支付)</h2>
                        </div>
                    </div>
                    <!-- 內容 -->
                    <div class="flex justify-between h-4/5 pt-4">
                        <!-- 借方 -->
                        <div class="flex flex-col w-full items-center justify-center m-4">
                            <p id="debitEntity" class="px-4 text-lg font-bold bg-green-50 border-b-4 border-green-100 text-green-500 m-2"> 員工/國稅局 </p>
                        </div>
                        <!-- 背景分割線 -->
                        <div class="bg-gray-100 border-1 border-gray-50 rounded-lg w-2 min-h-full"></div>
                        <!-- 貸方 -->
                        <div class="flex flex-col w-full items-center justify-center m-4">
                            <p id="creditEntity" class="px-4 text-lg font-bold bg-red-50 border-b-4 border-red-100 text-red-500 m-2"> 本公司 </p>
                        </div>
                    </div>
                </div>
                <!-- 會計科目區域 -->
                <div class="bg-white rounded-lg shadow-md p-2 col-span-1 m-4 relative">

                    <!-- 內容 -->
                    <div class="flex justify-between h-4/5">
                        <!-- 借方 -->
                        <div id="debitAccountingContainer" class="flex flex-col p-4 w-full">
                            <!-- 借方會計科目 -->
                            <div class="mb-2 border-b-4 border-r-4 border-green-100 p-2 text-sm mb-4">
                                <div class="flex flex-col justify-between border-b-2 border-gray-200 pb-2">
                                    <span class="debitAccountingDate font-bold text-blue-600">1980/01/01</span>
                                </div>
                                <div class="flex flex-col justify-center items-center border-b-2 border-gray-200 p-2">
                                    <span class="debitAccountingType text-xs text-gray-600">1980</span>
                                    <span class="debitAccountingName font-bold text-gray-600">工程款</span>
                                </div>

                                <div>
                                    <p class="debitAccountingBalance font-bold text-gray-900 text-right mt-2">$1,000</p>
                                </div>
                            </div>

                            <!-- 借方會計科目2 -->
                            <div class="mb-2 border-b-4 border-r-4 border-green-100 p-2 text-sm mb-4">
                                <div class="flex flex-col justify-between border-b-2 border-gray-200 pb-2">
                                    <span class="debitAccountingDate font-bold text-blue-600">1980/01/01</span>
                                </div>
                                <div class="flex flex-col justify-center items-center border-b-2 border-gray-200 p-2">
                                    <span class="debitAccountingCode text-xs text-gray-600">1025</span>
                                    <span class="debitAccountingName font-bold text-gray-600">扣繳稅額</span>
                                </div>
                                <div>
                                    <p class="debitAccountingBalance font-bold text-gray-900 text-right mt-2">$10</p>
                                </div>
                            </div>

                            <!-- 借方會計科目3 -->
                            <div class="mb-2 border-b-4 border-r-4 border-green-100 p-2 text-sm mb-4">
                                <div class="flex flex-col justify-between border-b-2 border-gray-200 pb-2">
                                    <span class="debitAccountingDate font-bold text-blue-600">1980/01/01</span>
                                </div>
                                <div class="flex flex-col justify-center items-center border-b-2 border-gray-200 p-2">
                                    <span class="debitAccountingCode text-xs text-gray-600">1100</span>
                                    <span class="debitAccountingName font-bold text-gray-600">什雜項費用<br>(手續費)</span>
                                </div>
                                <div>
                                    <p class="debitAccountingBalance font-bold text-gray-900 text-right mt-2">$10</p>
                                </div>
                            </div>
                        </div>
                        <!-- 背景分割線 -->
                        <div class="bg-gray-100 border-1 border-gray-50 rounded-lg w-2 min-h-full"></div>
                        <!-- 貸方 -->
                        <div id="creditAccountingContainer" class="flex flex-col p-4 w-full">
                            <!-- 貸方會計科目 -->
                            <div class="mb-2 border-b-4 border-r-4 border-red-100 p-2 text-sm mb-4">
                                <div class="flex flex-col justify-between border-b-2 border-gray-200 pb-2">
                                    <span class="debitAccountingDate font-bold text-blue-600">1980/01/01</span>
                                </div>
                                <div class="flex flex-col justify-center items-center border-b-2 border-gray-200 p-2">
                                    <span class="creditAccountingCode text-xs text-gray-600">2120</span>
                                    <span class="creditAccountingName font-bold text-gray-600">銀行存款</span>
                                </div>
                                <div>
                                    <p class="creditAccountingBalance font-bold text-gray-900 text-right mt-2">$2,000</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
    </main>

    <script src="transactionCreate_EntitySearch_Module.js"></script>
    <script src="transactionCreate_PaymentStatus_Module.js"></script>
    <script src="transactionCreate_TransactionType_Module.js"></script>
    <script src="transactionCreate_PaymentDescriptionMegaMenu_Module.js"></script>
    <script src="tagManager.js"></script>

    <script src="detailManager.js"></script>
    <script src="transactionCreate_JournalManagement_Module.js"></script>
    <script src="transactionCreate_Controller.js"></script>
    <script src="transactionCreate_LogicManager.js"></script>
"    <script src="transactionCreate_Service.js"></script>
    <script src="transactionCreate_UI.js"></script>

    <footer class="bg-white shadow-lg mt-8">
        <div class="container mx-auto px-4 py-6">
            <p class="text-center text-gray-600">© 2024 益芯能源工程-管理系統. All rights reserved.</p>
        </div>
    </footer>

    <script>
        
    </script>
</body>

</html>