/**
 * @file transactionsBatch_Events.js
 * @description 批次交易事件處理模組
 * 負責處理各種使用者互動事件和資料變更邏輯
 */

/**
 * 初始化行事件監聽器
 */
function initializeRowEventListeners(rowIndex) {
    // 這個函式在 UI 模組中被調用，用於設定新行的事件監聽
    console.log(`初始化第 ${rowIndex} 行的事件監聽器`);
}

/**
 * 處理行交易類型變更
 */
function handleRowTransactionTypeChange(rowIndex) {
    // 重新載入該行的交易項目選單
    loadRowPaymentDescriptionOptions(rowIndex);
}

/**
 * 處理行帳款到帳情形變更
 */
function handleRowPaymentStatusChange(rowIndex) {
    const paymentStatusSelect = document.getElementById(`batch_paymentStatus_${rowIndex}`);
    const paymentStatus = paymentStatusSelect.value;
    
    // 根據到帳情形調整該行的日期欄位
    handleRowPaymentDateFields(rowIndex, paymentStatus);
}

/**
 * 處理行日期變更
 */
function handleRowDateChange(rowIndex, fieldKey) {
    const paymentStatusSelect = document.getElementById(`batch_paymentStatus_${rowIndex}`);
    const paymentStatus = paymentStatusSelect ? paymentStatusSelect.value : '';
    
    const paymentDateInput = document.getElementById(`batch_paymentDate_${rowIndex}`);
    const invoiceDateInput = document.getElementById(`batch_invoiceDate_${rowIndex}`);
    const expectedPaymentDateInput = document.getElementById(`batch_expectedPaymentDate_${rowIndex}`);
    
    // 根據到帳情形同步日期
    if (paymentStatus === 'same_day' && fieldKey === 'paymentDate' && paymentDateInput && invoiceDateInput) {
        invoiceDateInput.value = paymentDateInput.value;
    } else if (paymentStatus === 'different_day' && fieldKey === 'paymentDate' && paymentDateInput && expectedPaymentDateInput) {
        expectedPaymentDateInput.value = paymentDateInput.value;
    }
}

/**
 * 處理行帳款到帳情形的日期欄位控制
 */
function handleRowPaymentDateFields(rowIndex, paymentStatus) {
    const paymentDateInput = document.getElementById(`batch_paymentDate_${rowIndex}`);
    const invoiceDateInput = document.getElementById(`batch_invoiceDate_${rowIndex}`);
    const expectedPaymentDateInput = document.getElementById(`batch_expectedPaymentDate_${rowIndex}`);
    
    if (!paymentDateInput || !invoiceDateInput) return;
    
    // 重置所有欄位狀態
    paymentDateInput.disabled = false;
    paymentDateInput.classList.remove('opacity-50', 'cursor-not-allowed');
    invoiceDateInput.disabled = false;
    invoiceDateInput.classList.remove('opacity-50', 'cursor-not-allowed');
    
    if (expectedPaymentDateInput) {
        expectedPaymentDateInput.disabled = false;
        expectedPaymentDateInput.classList.remove('opacity-50', 'cursor-not-allowed');
    }
    
    // 根據到帳情形調整欄位狀態
    switch(paymentStatus) {
        case 'same_day':
            // 同日收款 - 發票日期自動同步付款日期
            invoiceDateInput.disabled = true;
            invoiceDateInput.classList.add('opacity-50', 'cursor-not-allowed');
            if (paymentDateInput.value) {
                invoiceDateInput.value = paymentDateInput.value;
            }
            break;
            
        case 'receivable':
            // 應收款 - 沒有實際付款日期
            paymentDateInput.disabled = true;
            paymentDateInput.classList.add('opacity-50', 'cursor-not-allowed');
            paymentDateInput.value = '';
            break;
            
        case 'prepayment':
            // 暫收款 - 沒有發票日期
            invoiceDateInput.disabled = true;
            invoiceDateInput.classList.add('opacity-50', 'cursor-not-allowed');
            invoiceDateInput.value = '';
            break;
            
        case 'different_day':
            // 非同日收款 - 預計付款日期自動同步付款日期
            if (expectedPaymentDateInput) {
                expectedPaymentDateInput.disabled = true;
                expectedPaymentDateInput.classList.add('opacity-50', 'cursor-not-allowed');
                if (paymentDateInput.value) {
                    expectedPaymentDateInput.value = paymentDateInput.value;
                }
            }
            break;
    }
}

/**
 * 計算行稅額
 */
function calculateRowTaxAmount(rowIndex) {
    const amountInput = document.getElementById(`batch_amount_${rowIndex}`);
    const taxTypeSelect = document.getElementById(`batch_taxType_${rowIndex}`);
    const taxAmountInput = document.getElementById(`batch_taxAmount_${rowIndex}`);
    
    if (!amountInput || !taxTypeSelect || !taxAmountInput) return;
    
    const amount = parseFloat(amountInput.value) || 0;
    const selectedOption = taxTypeSelect.options[taxTypeSelect.selectedIndex];
    const taxRate = parseFloat(selectedOption.dataset.rate) || 0;
    
    if (amount > 0 && taxRate > 0) {
        // 使用與原系統相同的稅額計算邏輯（含稅金額反推稅額）
        const amountNoTax = Math.round(amount / (1 + Number(taxRate)));
        const taxAmount = amount - amountNoTax;
        taxAmountInput.value = taxAmount.toFixed(2);
    } else {
        taxAmountInput.value = '';
    }
}

/**
 * 處理行交易對象搜尋
 */
async function handleRowEntitySearch(searchTerm, rowIndex) {
    const resultsDiv = document.getElementById(`batch_entity_${rowIndex}_results`);
    
    if (!searchTerm || searchTerm.length < 1) {
        // 顯示所有選項（限制數量）
        try {
            const employees = await getEmployeesAll();
            const entities = await getEntitiesAll();
            
            const limitedEmployees = employees.slice(0, 5);
            const limitedEntities = entities.slice(0, 5);
            
            showRowEntitySearchResults(rowIndex, limitedEmployees, limitedEntities);
        } catch (error) {
            console.error('載入交易對象失敗:', error);
            hideRowEntitySearchResults(rowIndex);
        }
        return;
    }
    
    if (searchTerm.length < 2) {
        hideRowEntitySearchResults(rowIndex);
        return;
    }
    
    try {
        // 搜尋員工
        const employees = await getEmployeesAll();
        const matchedEmployees = employees.filter(emp => 
            emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            emp.code.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 5);
        
        // 搜尋交易對象
        const entities = await getEntitiesAll();
        const matchedEntities = entities.filter(entity => 
            entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entity.code.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 5);
        
        // 顯示搜尋結果
        showRowEntitySearchResults(rowIndex, matchedEmployees, matchedEntities);
        
    } catch (error) {
        console.error('搜尋交易對象失敗:', error);
        hideRowEntitySearchResults(rowIndex);
    }
}

/**
 * 顯示行交易對象搜尋結果
 */
function showRowEntitySearchResults(rowIndex, employees, entities) {
    const resultsDiv = document.getElementById(`batch_entity_${rowIndex}_results`);
    if (!resultsDiv) return;
    
    resultsDiv.innerHTML = '';
    
    // 添加員工結果
    if (employees.length > 0) {
        const employeeHeader = document.createElement('div');
        employeeHeader.className = 'px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700';
        employeeHeader.textContent = '員工';
        resultsDiv.appendChild(employeeHeader);
        
        employees.forEach(employee => {
            const item = document.createElement('div');
            item.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer text-xs';
            item.textContent = `${employee.name} (${employee.code})`;
            item.onclick = () => selectRowEntity(rowIndex, employee.id, 'employee', employee.name);
            resultsDiv.appendChild(item);
        });
    }
    
    // 添加交易對象結果
    if (entities.length > 0) {
        const entityHeader = document.createElement('div');
        entityHeader.className = 'px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700';
        entityHeader.textContent = '交易對象';
        resultsDiv.appendChild(entityHeader);
        
        entities.forEach(entity => {
            const item = document.createElement('div');
            item.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer text-xs';
            item.textContent = `${entity.name} (${entity.code})`;
            item.onclick = () => selectRowEntity(rowIndex, entity.id, 'entity', entity.name);
            resultsDiv.appendChild(item);
        });
    }
    
    if (employees.length === 0 && entities.length === 0) {
        const noResult = document.createElement('div');
        noResult.className = 'px-3 py-2 text-xs text-gray-500';
        noResult.textContent = '找不到相符的結果';
        resultsDiv.appendChild(noResult);
    }
    
    resultsDiv.classList.remove('hidden');
}

/**
 * 隱藏行交易對象搜尋結果
 */
function hideRowEntitySearchResults(rowIndex) {
    const resultsDiv = document.getElementById(`batch_entity_${rowIndex}_results`);
    if (resultsDiv) {
        resultsDiv.classList.add('hidden');
    }
}

/**
 * 選擇行交易對象
 */
function selectRowEntity(rowIndex, entityId, entityType, entityName) {
    const input = document.getElementById(`batch_entity_${rowIndex}`);
    const idInput = document.getElementById(`batch_entity_${rowIndex}_id`);
    const typeInput = document.getElementById(`batch_entity_${rowIndex}_type`);
    
    if (input && idInput && typeInput) {
        input.value = entityName;
        idInput.value = entityId;
        typeInput.value = entityType;
    }
    
    hideRowEntitySearchResults(rowIndex);
}

// 匯出函式供其他模組使用
window.handleRowTransactionTypeChange = handleRowTransactionTypeChange;
window.handleRowPaymentStatusChange = handleRowPaymentStatusChange;
window.handleRowDateChange = handleRowDateChange;
window.calculateRowTaxAmount = calculateRowTaxAmount;
window.handleRowEntitySearch = handleRowEntitySearch;
window.selectRowEntity = selectRowEntity;
