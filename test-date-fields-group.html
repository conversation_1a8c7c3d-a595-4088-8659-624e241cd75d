<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期欄位群組功能測試</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">📅 日期欄位群組功能測試</h1>
        
        <!-- 功能說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-blue-600">🎯 功能說明</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">設計目標</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 日期相關欄位作為一個群組統一管理</li>
                        <li>• 要麼全部在共同屬性區，要麼全部在個別屬性區</li>
                        <li>• 避免日期欄位分散在兩個區域造成混亂</li>
                        <li>• 提供直觀的視覺提示和同步操作</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">包含欄位</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>📅 收款/付款日期</li>
                        <li>📄 憑證/發票日期</li>
                        <li>⏰ 預計收/付款日期</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 測試控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🧪 測試控制面板</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="testGroupSync()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    測試群組同步
                </button>
                <button onclick="testIndividualToggle()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    測試個別切換
                </button>
                <button onclick="testGroupEnforcement()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    測試群組強制
                </button>
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    開啟批次頁面
                </button>
                <button onclick="clearResults()" 
                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    清除結果
                </button>
                <button onclick="runAllTests()" 
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    執行所有測試
                </button>
            </div>
        </div>
        
        <!-- 模擬日期欄位群組 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">📋 模擬日期欄位群組</h2>
            <div class="border border-blue-200 bg-blue-50 rounded-lg p-4">
                <div class="text-sm text-blue-600 font-medium mb-3">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    日期欄位群組 (統一設置)
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label class="flex items-center p-3 bg-white rounded border hover:bg-gray-50">
                        <input type="checkbox" class="mr-3 date-field-checkbox" data-field="paymentDate" id="paymentDate">
                        <div>
                            <div class="font-medium text-gray-800">收款/付款日期</div>
                            <div class="text-xs text-gray-500">實際資金流動日期</div>
                        </div>
                    </label>
                    <label class="flex items-center p-3 bg-white rounded border hover:bg-gray-50">
                        <input type="checkbox" class="mr-3 date-field-checkbox" data-field="invoiceDate" id="invoiceDate">
                        <div>
                            <div class="font-medium text-gray-800">憑證/發票日期</div>
                            <div class="text-xs text-gray-500">發票或憑證開立日期</div>
                        </div>
                    </label>
                    <label class="flex items-center p-3 bg-white rounded border hover:bg-gray-50">
                        <input type="checkbox" class="mr-3 date-field-checkbox" data-field="expectedPaymentDate" id="expectedPaymentDate">
                        <div>
                            <div class="font-medium text-gray-800">預計收/付款日期</div>
                            <div class="text-xs text-gray-500">預期的資金流動日期</div>
                        </div>
                    </label>
                </div>
                <div class="mt-4 p-3 bg-white rounded border">
                    <div class="text-sm font-medium text-gray-700 mb-2">群組狀態:</div>
                    <div id="groupStatus" class="text-sm text-gray-600">請點擊上方複選框測試群組同步功能</div>
                </div>
            </div>
        </div>
        
        <!-- 測試場景 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🎭 測試場景</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">場景 1: 群組同步</h3>
                    <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-600 mb-3">當用戶點擊任何一個日期欄位時，其他日期欄位應該自動同步到相同狀態。</p>
                        <button onclick="simulateScenario1()" class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600">
                            模擬測試
                        </button>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">場景 2: 群組強制</h3>
                    <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-600 mb-3">當系統檢測到日期欄位狀態不一致時，應該自動統一設置。</p>
                        <button onclick="simulateScenario2()" class="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600">
                            模擬測試
                        </button>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">場景 3: 視覺提示</h3>
                    <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-600 mb-3">群組狀態變更時應該顯示清楚的視覺提示。</p>
                        <button onclick="simulateScenario3()" class="px-3 py-1 bg-yellow-500 text-white text-sm rounded hover:bg-yellow-600">
                            模擬測試
                        </button>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">場景 4: 表格重建</h3>
                    <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-600 mb-3">群組狀態變更後，表格應該正確重建，日期欄位要麼全部在共同區，要麼全部在個別區。</p>
                        <button onclick="simulateScenario4()" class="px-3 py-1 bg-purple-500 text-white text-sm rounded hover:bg-purple-600">
                            模擬測試
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 測試結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">📊 測試結果</h2>
            <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto">
                <p class="text-gray-600">點擊上方按鈕開始測試...</p>
            </div>
        </div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor}`;
            resultItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span class="text-sm">${message}</span>
                    <span class="ml-auto text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            
            resultsDiv.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-600">測試結果已清除</p>';
        }
        
        function updateGroupStatus() {
            const checkboxes = document.querySelectorAll('.date-field-checkbox');
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            const totalCount = checkboxes.length;
            
            let statusText = '';
            let statusClass = '';
            
            if (checkedCount === 0) {
                statusText = '全部設為個別屬性';
                statusClass = 'text-red-600';
            } else if (checkedCount === totalCount) {
                statusText = '全部設為共同屬性';
                statusClass = 'text-green-600';
            } else {
                statusText = `部分選中 (${checkedCount}/${totalCount}) - 需要統一`;
                statusClass = 'text-yellow-600';
            }
            
            const statusDiv = document.getElementById('groupStatus');
            statusDiv.textContent = statusText;
            statusDiv.className = `text-sm font-medium ${statusClass}`;
        }
        
        function syncDateFields(changedCheckbox) {
            const checkboxes = document.querySelectorAll('.date-field-checkbox');
            const newState = changedCheckbox.checked;
            
            checkboxes.forEach(cb => {
                if (cb !== changedCheckbox) {
                    cb.checked = newState;
                }
            });
            
            updateGroupStatus();
            
            const statusText = newState ? '共同屬性' : '個別屬性';
            addTestResult(`日期欄位群組已同步設置為: ${statusText}`, 'success');
            showNotification(`日期欄位群組已設為: ${statusText}`);
        }
        
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity duration-300';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    <span class="text-sm">${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 2000);
        }
        
        function testGroupSync() {
            addTestResult('開始測試群組同步功能...', 'info');
            
            // 重置所有複選框
            document.querySelectorAll('.date-field-checkbox').forEach(cb => cb.checked = false);
            updateGroupStatus();
            
            // 模擬點擊第一個複選框
            const firstCheckbox = document.getElementById('paymentDate');
            firstCheckbox.checked = true;
            syncDateFields(firstCheckbox);
            
            addTestResult('✓ 群組同步測試完成', 'success');
        }
        
        function testIndividualToggle() {
            addTestResult('開始測試個別切換功能...', 'info');
            
            const checkboxes = document.querySelectorAll('.date-field-checkbox');
            
            // 測試每個複選框的切換
            checkboxes.forEach((checkbox, index) => {
                setTimeout(() => {
                    checkbox.checked = !checkbox.checked;
                    syncDateFields(checkbox);
                    addTestResult(`✓ 測試 ${checkbox.dataset.field} 切換`, 'info');
                }, index * 1000);
            });
            
            setTimeout(() => {
                addTestResult('✓ 個別切換測試完成', 'success');
            }, checkboxes.length * 1000);
        }
        
        function testGroupEnforcement() {
            addTestResult('開始測試群組強制功能...', 'info');
            
            // 創建不一致狀態
            document.getElementById('paymentDate').checked = true;
            document.getElementById('invoiceDate').checked = false;
            document.getElementById('expectedPaymentDate').checked = true;
            
            updateGroupStatus();
            addTestResult('✓ 創建不一致狀態', 'warning');
            
            // 模擬強制統一
            setTimeout(() => {
                const checkedCount = Array.from(document.querySelectorAll('.date-field-checkbox')).filter(cb => cb.checked).length;
                const shouldAllBeChecked = checkedCount >= 2;
                
                document.querySelectorAll('.date-field-checkbox').forEach(cb => {
                    cb.checked = shouldAllBeChecked;
                });
                
                updateGroupStatus();
                addTestResult(`✓ 群組已強制統一為: ${shouldAllBeChecked ? '共同屬性' : '個別屬性'}`, 'success');
            }, 1000);
        }
        
        function simulateScenario1() {
            addTestResult('模擬場景 1: 群組同步', 'info');
            testGroupSync();
        }
        
        function simulateScenario2() {
            addTestResult('模擬場景 2: 群組強制', 'info');
            testGroupEnforcement();
        }
        
        function simulateScenario3() {
            addTestResult('模擬場景 3: 視覺提示', 'info');
            showNotification('這是群組狀態變更的視覺提示');
            addTestResult('✓ 視覺提示已顯示', 'success');
        }
        
        function simulateScenario4() {
            addTestResult('模擬場景 4: 表格重建', 'info');
            addTestResult('✓ 模擬表格重建 - 日期欄位統一在共同屬性區', 'success');
            addTestResult('✓ 模擬表格重建 - 日期欄位統一在個別屬性區', 'success');
        }
        
        async function runAllTests() {
            addTestResult('開始執行所有測試...', 'info');
            
            testGroupSync();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            testGroupEnforcement();
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            simulateScenario3();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            simulateScenario4();
            
            addTestResult('所有測試執行完成！', 'success');
        }
        
        function openBatchPage() {
            addTestResult('開啟批次交易頁面...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('日期欄位群組測試頁面載入完成', 'success');
            
            // 為複選框添加事件監聽器
            document.querySelectorAll('.date-field-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    syncDateFields(this);
                });
            });
            
            updateGroupStatus();
        });
    </script>
</body>
</html>
