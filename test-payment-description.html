<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易項目選擇功能測試</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">交易項目選擇功能測試</h1>
        
        <!-- 測試控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">測試控制面板</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="testDropdownFunctionality()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    下拉選單測試
                </button>
                <button onclick="testSearchFunctionality()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    搜尋功能測試
                </button>
                <button onclick="testCategoryDisplay()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    分類顯示測試
                </button>
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    開啟批次頁面
                </button>
                <button onclick="clearResults()" 
                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    清除結果
                </button>
                <button onclick="runAllTests()" 
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    執行所有測試
                </button>
            </div>
        </div>
        
        <!-- 交易項目選擇示範 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">交易項目選擇示範</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 共同交易項目選擇 -->
                <div>
                    <h3 class="text-lg font-medium mb-3">共同交易項目選擇</h3>
                    <div class="relative payment-desc-container">
                        <button type="button" id="demoCommonBtn" class="w-full p-3 border rounded-lg text-left bg-white hover:bg-gray-50">
                            選擇交易項目
                        </button>
                        <div class="payment-desc-dropdown absolute z-20 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-64 overflow-y-auto" style="min-width: 300px;">
                            <!-- 交易項目選單將在此顯示 -->
                        </div>
                        <input type="hidden" id="demoCommonCode">
                        <input type="hidden" id="demoCommonName">
                    </div>
                    <div class="mt-2 text-sm text-gray-600">
                        <div>選擇的項目代碼: <span id="selectedCommonCode" class="font-mono">未選擇</span></div>
                        <div>項目名稱: <span id="selectedCommonName" class="font-mono">未選擇</span></div>
                    </div>
                </div>
                
                <!-- 個別交易項目選擇 -->
                <div>
                    <h3 class="text-lg font-medium mb-3">個別交易項目選擇</h3>
                    <div class="relative payment-desc-container">
                        <button type="button" id="demoIndividualBtn" class="w-full p-3 border rounded-lg text-left bg-white hover:bg-gray-50" data-row="demo">
                            選擇交易項目
                        </button>
                        <div class="payment-desc-dropdown absolute z-20 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-64 overflow-y-auto" style="min-width: 300px;">
                            <!-- 交易項目選單將在此顯示 -->
                        </div>
                        <input type="hidden" id="demoIndividualCode">
                        <input type="hidden" id="demoIndividualName">
                    </div>
                    <div class="mt-2 text-sm text-gray-600">
                        <div>選擇的項目代碼: <span id="selectedIndividualCode" class="font-mono">未選擇</span></div>
                        <div>項目名稱: <span id="selectedIndividualName" class="font-mono">未選擇</span></div>
                    </div>
                </div>
            </div>
            
            <!-- 交易類型選擇 -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="font-medium mb-3">交易類型選擇</h4>
                <div class="flex space-x-4">
                    <label class="flex items-center">
                        <input type="radio" name="demoTransactionType" value="expense" checked class="mr-2">
                        <span>支出</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="demoTransactionType" value="income" class="mr-2">
                        <span>收入</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="demoTransactionType" value="transfer" class="mr-2">
                        <span>轉移</span>
                    </label>
                </div>
            </div>
            
            <!-- 功能說明 -->
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-medium text-blue-800 mb-2">功能說明</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• 點擊按鈕開啟下拉選單</li>
                    <li>• 支援搜尋功能，即時篩選項目</li>
                    <li>• 按分類組織顯示交易項目</li>
                    <li>• 滑鼠懸停高亮效果</li>
                    <li>• 點擊外部自動關閉選單</li>
                    <li>• 根據交易類型篩選可用項目</li>
                </ul>
            </div>
        </div>
        
        <!-- 測試結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">測試結果</h2>
            <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto">
                <p class="text-gray-600">點擊上方按鈕開始測試...</p>
            </div>
        </div>
    </div>

    <script>
        // 模擬資料
        const mockCategories = [
            { id: 'cat1', name: '辦公費用', type: 'expense' },
            { id: 'cat2', name: '差旅費用', type: 'expense' },
            { id: 'cat3', name: '銷售收入', type: 'income' },
            { id: 'cat4', name: '其他收入', type: 'income' }
        ];
        
        const mockItems = [
            { id: 'item1', categoryId: 'cat1', accountingCode: '6001', accountingName: '文具用品' },
            { id: 'item2', categoryId: 'cat1', accountingCode: '6002', accountingName: '辦公設備' },
            { id: 'item3', categoryId: 'cat2', accountingCode: '6101', accountingName: '交通費' },
            { id: 'item4', categoryId: 'cat2', accountingCode: '6102', accountingName: '住宿費' },
            { id: 'item5', categoryId: 'cat3', accountingCode: '4001', accountingName: '產品銷售' },
            { id: 'item6', categoryId: 'cat4', accountingCode: '4101', accountingName: '利息收入' }
        ];
        
        // 模擬 API 函式
        window.getTransactionCategoriesAll = async () => mockCategories;
        window.getTransactionCategoryItemsAll = async () => mockItems;
        
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor}`;
            resultItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span class="text-sm">${message}</span>
                    <span class="ml-auto text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            
            resultsDiv.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-600">測試結果已清除</p>';
        }
        
        function testDropdownFunctionality() {
            addTestResult('開始下拉選單功能測試...', 'info');
            
            const button = document.getElementById('demoCommonBtn');
            const dropdown = button.parentElement.querySelector('.payment-desc-dropdown');
            
            if (button && dropdown) {
                addTestResult('✓ 找到下拉選單元素', 'success');
                
                // 模擬點擊按鈕
                button.click();
                
                setTimeout(() => {
                    if (!dropdown.classList.contains('hidden')) {
                        addTestResult('✓ 下拉選單正確顯示', 'success');
                    } else {
                        addTestResult('✗ 下拉選單未顯示', 'error');
                    }
                    
                    // 測試點擊外部關閉
                    document.body.click();
                    setTimeout(() => {
                        if (dropdown.classList.contains('hidden')) {
                            addTestResult('✓ 點擊外部正確關閉選單', 'success');
                        } else {
                            addTestResult('✗ 點擊外部未關閉選單', 'error');
                        }
                    }, 100);
                }, 500);
                
            } else {
                addTestResult('✗ 找不到下拉選單元素', 'error');
            }
            
            addTestResult('下拉選單功能測試完成', 'info');
        }
        
        function testSearchFunctionality() {
            addTestResult('開始搜尋功能測試...', 'info');
            
            const button = document.getElementById('demoCommonBtn');
            const dropdown = button.parentElement.querySelector('.payment-desc-dropdown');
            
            // 先開啟下拉選單
            loadDemoOptions(button, dropdown);
            
            setTimeout(() => {
                const searchInput = dropdown.querySelector('input[type="text"]');
                if (searchInput) {
                    addTestResult('✓ 找到搜尋輸入框', 'success');
                    
                    // 模擬搜尋
                    searchInput.value = '文具';
                    searchInput.dispatchEvent(new Event('input'));
                    
                    setTimeout(() => {
                        const visibleItems = Array.from(dropdown.querySelectorAll('.payment-desc-item'))
                            .filter(item => item.style.display !== 'none');
                        
                        if (visibleItems.length > 0) {
                            addTestResult(`✓ 搜尋「文具」找到 ${visibleItems.length} 個結果`, 'success');
                        } else {
                            addTestResult('✗ 搜尋未找到結果', 'error');
                        }
                    }, 100);
                    
                } else {
                    addTestResult('✗ 找不到搜尋輸入框', 'error');
                }
            }, 500);
            
            addTestResult('搜尋功能測試完成', 'info');
        }
        
        function testCategoryDisplay() {
            addTestResult('開始分類顯示測試...', 'info');
            
            const button = document.getElementById('demoCommonBtn');
            const dropdown = button.parentElement.querySelector('.payment-desc-dropdown');
            
            // 載入選項
            loadDemoOptions(button, dropdown);
            
            setTimeout(() => {
                const categories = dropdown.querySelectorAll('.bg-gray-100');
                const items = dropdown.querySelectorAll('.payment-desc-item');
                
                addTestResult(`✓ 顯示 ${categories.length} 個分類`, 'success');
                addTestResult(`✓ 顯示 ${items.length} 個交易項目`, 'success');
                
                if (categories.length > 0 && items.length > 0) {
                    addTestResult('✓ 分類和項目正確顯示', 'success');
                } else {
                    addTestResult('✗ 分類或項目顯示異常', 'error');
                }
            }, 500);
            
            addTestResult('分類顯示測試完成', 'info');
        }
        
        async function runAllTests() {
            addTestResult('開始執行所有測試...', 'info');
            
            testDropdownFunctionality();
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            testSearchFunctionality();
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            testCategoryDisplay();
            
            addTestResult('所有測試執行完成！', 'success');
        }
        
        function openBatchPage() {
            addTestResult('開啟批次交易頁面...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        // 載入示範選項
        function loadDemoOptions(button, dropdown) {
            const transactionType = document.querySelector('input[name="demoTransactionType"]:checked').value;
            const filteredCategories = mockCategories.filter(cat => cat.type === transactionType);
            
            let html = `
                <div class="p-2 border-b bg-gray-50">
                    <input type="text" class="w-full p-2 text-sm border border-gray-300 rounded" 
                           placeholder="搜尋交易項目...">
                </div>
                <div class="payment-desc-options max-h-48 overflow-y-auto">
            `;
            
            filteredCategories.forEach(category => {
                const categoryItems = mockItems.filter(item => item.categoryId === category.id);
                
                if (categoryItems.length > 0) {
                    html += `<div class="px-3 py-2 bg-gray-100 text-sm font-medium text-gray-700 sticky top-0">${category.name}</div>`;
                    
                    categoryItems.forEach(item => {
                        html += `
                            <div class="payment-desc-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100" 
                                 data-code="${item.accountingCode}" data-name="${item.accountingName}">
                                <div class="font-medium text-sm">${item.accountingName}</div>
                                <div class="text-xs text-gray-500">${item.accountingCode}</div>
                            </div>
                        `;
                    });
                }
            });
            
            html += '</div>';
            dropdown.innerHTML = html;
            dropdown.classList.remove('hidden');
            
            // 添加搜尋功能
            const searchInput = dropdown.querySelector('input[type="text"]');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    filterDemoOptions(dropdown, this.value);
                });
            }
            
            // 添加項目點擊事件
            dropdown.querySelectorAll('.payment-desc-item').forEach(item => {
                item.addEventListener('click', function() {
                    selectDemoItem(button, this.dataset.code, this.dataset.name);
                });
            });
        }
        
        function filterDemoOptions(dropdown, searchTerm) {
            const items = dropdown.querySelectorAll('.payment-desc-item');
            const categories = dropdown.querySelectorAll('.bg-gray-100');
            const term = searchTerm.toLowerCase();
            
            categories.forEach(cat => cat.style.display = 'none');
            
            let hasVisibleItems = false;
            items.forEach(item => {
                const name = item.dataset.name.toLowerCase();
                const code = item.dataset.code.toLowerCase();
                const isMatch = name.includes(term) || code.includes(term);
                
                if (isMatch || !term) {
                    item.style.display = 'block';
                    hasVisibleItems = true;
                    
                    let categoryHeader = item.previousElementSibling;
                    while (categoryHeader && !categoryHeader.classList.contains('bg-gray-100')) {
                        categoryHeader = categoryHeader.previousElementSibling;
                    }
                    if (categoryHeader) {
                        categoryHeader.style.display = 'block';
                    }
                } else {
                    item.style.display = 'none';
                }
            });
        }
        
        function selectDemoItem(button, code, name) {
            button.textContent = name;
            button.title = `${name} (${code})`;
            
            const dropdown = button.parentElement.querySelector('.payment-desc-dropdown');
            dropdown.classList.add('hidden');
            
            // 更新顯示
            if (button.id === 'demoCommonBtn') {
                document.getElementById('selectedCommonCode').textContent = code;
                document.getElementById('selectedCommonName').textContent = name;
                document.getElementById('demoCommonCode').value = code;
                document.getElementById('demoCommonName').value = name;
            } else {
                document.getElementById('selectedIndividualCode').textContent = code;
                document.getElementById('selectedIndividualName').textContent = name;
                document.getElementById('demoIndividualCode').value = code;
                document.getElementById('demoIndividualName').value = name;
            }
            
            addTestResult(`選擇了交易項目：${name} (${code})`, 'success');
        }
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('交易項目選擇測試頁面載入完成', 'success');
            addTestResult('可以開始進行功能測試', 'info');
            
            // 設置示範功能
            setupDemoFunctionality();
        });
        
        function setupDemoFunctionality() {
            // 為示範按鈕添加點擊事件
            const buttons = ['demoCommonBtn', 'demoIndividualBtn'];
            
            buttons.forEach(buttonId => {
                const button = document.getElementById(buttonId);
                const dropdown = button.parentElement.querySelector('.payment-desc-dropdown');
                
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    // 關閉其他下拉選單
                    document.querySelectorAll('.payment-desc-dropdown').forEach(dd => {
                        if (dd !== dropdown) dd.classList.add('hidden');
                    });
                    
                    // 切換當前下拉選單
                    if (dropdown.classList.contains('hidden')) {
                        loadDemoOptions(button, dropdown);
                    } else {
                        dropdown.classList.add('hidden');
                    }
                });
            });
            
            // 點擊外部關閉下拉選單
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.payment-desc-container')) {
                    document.querySelectorAll('.payment-desc-dropdown').forEach(dd => {
                        dd.classList.add('hidden');
                    });
                }
            });
            
            // 交易類型變更事件
            document.querySelectorAll('input[name="demoTransactionType"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    // 重置選擇
                    document.getElementById('demoCommonBtn').textContent = '選擇交易項目';
                    document.getElementById('demoIndividualBtn').textContent = '選擇交易項目';
                    
                    // 清空顯示
                    document.getElementById('selectedCommonCode').textContent = '未選擇';
                    document.getElementById('selectedCommonName').textContent = '未選擇';
                    document.getElementById('selectedIndividualCode').textContent = '未選擇';
                    document.getElementById('selectedIndividualName').textContent = '未選擇';
                    
                    addTestResult(`交易類型變更為：${this.value}`, 'info');
                });
            });
        }
    </script>
</body>
</html>
