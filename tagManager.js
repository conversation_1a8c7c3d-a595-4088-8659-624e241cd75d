// 標籤管理功能
document.addEventListener('DOMContentLoaded', function() {
    const tagInput = document.getElementById('tagInput');
    let tags = [];

    // 使用現有的標籤容器
    const tagContainer = document.getElementById('tagContainer');
    if (!tagContainer) {
        console.error('找不到標籤容器');
        return;
    }

    // 調整輸入框樣式
    tagInput.style.width = 'auto';
    tagInput.style.minWidth = '60px';
    tagInput.style.flex = '1';
    tagInput.style.border = 'none';
    tagInput.style.outline = 'none';
    tagInput.style.padding = '0';
    tagInput.style.boxShadow = 'none';

    // 包裝容器
    const wrapper = document.createElement('div');
    wrapper.className = 'flex flex-wrap items-center gap-1 mt-1 p-2 shadow-sm border border-gray-300 rounded-md focus-within:border-blue-500 focus-within:border-2 bg-white';
    tagInput.parentNode.insertBefore(wrapper, tagInput);
    wrapper.appendChild(tagContainer);
    wrapper.appendChild(tagInput);

    // 監聽Enter鍵
    tagInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const tag = this.value.trim();
            if (tag && !tags.includes(tag)) {
                addTag(tag);
                this.value = '';
            }
        }
    });

    // 監聽Backspace鍵
    tagInput.addEventListener('keydown', function(e) {
        if (e.key === 'Backspace' && this.value === '' && tags.length > 0) {
            e.preventDefault();
            const lastTag = tags[tags.length - 1];
            const tagElements = tagContainer.getElementsByTagName('div');
            const lastTagElement = tagElements[tagElements.length - 1];
            tagContainer.removeChild(lastTagElement);
            tags = tags.filter(t => t !== lastTag);
        }
    });

    // 新增標籤
    function addTag(tag) {
        tags.push(tag);
        const tagElement = document.createElement('div');
        tagElement.className = 'inline-flex items-center bg-blue-100 text-blue-800 font-medium px-3 rounded-full';
        
        const tagText = document.createElement('span');
        tagText.textContent = tag;
        tagElement.appendChild(tagText);

        const deleteButton = document.createElement('button');
        deleteButton.className = 'ml-1 text-blue-400 hover:text-blue-900';
        deleteButton.innerHTML = '&times;';
        deleteButton.type = 'button';//確保它是按鈕而不是提交表單
        deleteButton.onclick = function() {
            tagContainer.removeChild(tagElement);
            tags = tags.filter(t => t !== tag);
        };

        tagElement.appendChild(deleteButton);
        tagContainer.appendChild(tagElement);
    }

    // 設置預選標籤
    function setSelectedTags(selectedTags) {
        // 清空現有標籤
        tags = [];
        tagContainer.innerHTML = '';
        
        // 添加新的預選標籤
        if (Array.isArray(selectedTags)) {
            selectedTags.forEach(tag => {
                if (tag && typeof tag === 'string' && !tags.includes(tag)) {
                    addTag(tag);
                }
            });
        }
    }

    // 獲取當前標籤列表
    function getTags() {
        //檢查現有標籤是否已存在，如果不存在或是無值時檢查表單中是否有資料可取用
        const tagContainer = document.getElementById('tagContainer');
        if (tags.length === 0) {
            tags = [];
            const tagElements = tagContainer.getElementsByTagName('div');
            Array.from(tagElements).forEach(tagElement => {
                const tagText = tagElement.querySelector('span');
                if (tagText) {
                    tags.push(tagText.textContent.trim());
                }
            });
        }
        
        return [...tags];
    }

    // 將函數暴露到全局作用域
    window.tagManager = {
        setSelectedTags,
        getTags,
        addTag
    };
});