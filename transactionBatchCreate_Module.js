/**
 * @file transactionBatchCreate_Module.js
 * @description 這個檔案負責批次交易輸入功能的實現。
 * - 它包括了共同屬性的動態管理、批次表格的渲染和操作、以及批次保存邏輯。
 * - 作為一個獨立的模組，它重用了現有的交易創建相關函數，同時提供批次處理能力。
 */

// 全局變量
let commonPropertyFields = ['transactionType', 'accountId', 'paymentDate', 'paymentStatus', 'taxTypeId'];
let individualPropertyFields = ['amount', 'paymentDescription', 'entityId', 'taxAmount', 'invoiceDate', 'expectedPaymentDate', 'invoiceNumber', 'fee', 'tags', 'notes'];
let rowCounter = 0;
let accountsData = [];
let taxTypesData = [];
let entitySearchManager = null;

// DOM 載入完成後初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 初始化頁面
    await initializePage();

    // 初始化交易描述選擇器
    initializeBatchPage();

    // 添加事件監聽器
    setupEventListeners();

    // 初始化表格結構
    updateCommonPropertiesForm();
    updateBatchTableColumns();

    // 添加第一行
    addBatchRow();
});

/**
 * 初始化頁面2
 */
function initializeBatchPage() {
    // 添加必要的 DOM 元素，用於交易描述選擇器
    const megaMenuContainer = document.createElement('div');
    megaMenuContainer.id = 'paymentDescriptionMegaMenuContainer';
    megaMenuContainer.className = 'hidden fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';
    megaMenuContainer.innerHTML = `
        <div class="bg-white rounded-lg shadow-lg w-3/4 max-w-4xl">
            <div class="p-4 border-b flex justify-between items-center">
                <h3 class="text-lg font-bold">選擇交易描述</h3>
                <button type="button" id="closeMegaMenuBtn" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-4">
                <div id="paymentDescriptionMegaMenu" class="relative inline-block w-full">
                    <button id="paymentDescriptionMenuBtn" type="button" class="w-full text-left px-4 py-2 border rounded bg-white shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span id="paymentDescriptionSelected">請選擇交易項目</span>
                        <i class="fas fa-chevron-down float-right mt-1"></i>
                    </button>
                    <div id="paymentDescriptionMenuDropdown" class="absolute z-20 left-0 mt-1 w-full bg-white border rounded shadow-lg hidden"></div>
                </div>
                <input type="hidden" id="paymentDescription">
            </div>
            <div class="p-4 border-t flex justify-end">
                <button type="button" id="confirmMegaMenuBtn" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    確認選擇
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(megaMenuContainer);
    
    // 添加關閉按鈕事件
    document.getElementById('closeMegaMenuBtn').addEventListener('click', () => {
        megaMenuContainer.classList.add('hidden');
    });
    
    // 添加確認按鈕事件
    document.getElementById('confirmMegaMenuBtn').addEventListener('click', () => {
        megaMenuContainer.classList.add('hidden');
        
        // 觸發自定義事件，通知選擇完成
        const event = new CustomEvent('paymentDescriptionSelected', {
            detail: {
                code: document.getElementById('paymentDescription').value,
                name: document.getElementById('paymentDescriptionSelected').textContent
            }
        });
        document.dispatchEvent(event);
    });
}

/**
 * 初始化頁面
 */
async function initializePage() {
    // 載入選項數據
    await loadOptions();
    
    // 初始化共同屬性表單
    updateCommonPropertiesForm();
    
    // 初始化表格列
    updateBatchTableColumns();
}

/**
 * 載入選項數據
 */
async function loadOptions() {
    try {
        // 載入帳戶數據
        accountsData = await getAccountsAll();
        
        // 載入稅別數據
        taxTypesData = await getTaxTypesRatesAll();
        
        // 初始化交易對象搜尋管理器
        const accounts = await getAccountsAll();
        const employees = await getEmployeesAll();
        const entities = await getEntitiesAll();
        
        // 將交易對象管理器暴露到全局作用域
        window.entitySearchManager = new EntitySearchManager(accounts, employees, entities);
        entitySearchManager = window.entitySearchManager;
        
        // 填充帳戶選擇器
        populateAccountSelectors();
        
        // 填充稅別選擇器
        populateTaxTypeSelectors();
    } catch (error) {
        console.error('載入選項數據時發生錯誤:', error);
        alert('載入數據時發生錯誤，請重新整理頁面或聯絡系統管理員');
    }
}

/**
 * 填充帳戶選擇器
 */
function populateAccountSelectors() {
    // 填充共同屬性中的帳戶選擇器
    const commonAccountSelect = document.getElementById('batch_accountId');
    if (commonAccountSelect) {
        let options = '<option value="">請選擇帳戶</option>';
        accountsData.forEach(account => {
            options += `<option value="${account.id}">${account.name}</option>`;
        });
        commonAccountSelect.innerHTML = options;
    }
    
    // 填充個別屬性中的帳戶選擇器
    document.querySelectorAll('select[name^="batch_accountId_"]').forEach(select => {
        let options = '<option value="">請選擇帳戶</option>';
        accountsData.forEach(account => {
            options += `<option value="${account.id}">${account.name}</option>`;
        });
        select.innerHTML = options;
    });
}

/**
 * 填充稅別選擇器
 */
function populateTaxTypeSelectors() {
    // 填充共同屬性中的稅別選擇器
    const commonTaxTypeSelect = document.getElementById('batch_taxTypeId');
    if (commonTaxTypeSelect) {
        let options = '<option value="">請選擇稅別</option>';
        taxTypesData.forEach(taxType => {
            options += `<option value="${taxType.id}" data-rate="${taxType.rate}">${taxType.name}</option>`;
        });
        commonTaxTypeSelect.innerHTML = options;

        // 添加稅別變更事件監聽器
        commonTaxTypeSelect.addEventListener('change', function() {
            // 當共同稅別變更時，重新計算所有行的稅額
            updateAllRowsTaxCalculation();
        });
    }

    // 填充個別屬性中的稅別選擇器
    document.querySelectorAll('select[name^="batch_taxTypeId_"]').forEach(select => {
        let options = '<option value="">請選擇稅別</option>';
        taxTypesData.forEach(taxType => {
            options += `<option value="${taxType.id}" data-rate="${taxType.rate}">${taxType.name}</option>`;
        });
        select.innerHTML = options;

        // 為每個個別稅別選擇器添加事件監聽器
        select.addEventListener('change', function() {
            const rowNum = this.name.split('_')[2]; // 從 batch_taxTypeId_1 中提取行號
            calculateRowTaxAmount(rowNum);
        });
    });
}

/**
 * 設置事件監聽器
 */
function setupEventListeners() {
    // 共同屬性切換事件
    document.querySelectorAll('.common-property-toggle').forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            // 如果是日期欄位群組，同步其他日期欄位
            if (checkbox.classList.contains('date-field-group')) {
                syncDateFieldsGroup(checkbox);
            }

            updateCommonPropertiesForm();
            updateBatchTableColumns();
        });
    });

    // 快速模式按鈕
    document.getElementById('minimalPropsBtn').addEventListener('click', () => setQuickMode('minimal'));
    document.getElementById('standardPropsBtn').addEventListener('click', () => setQuickMode('standard'));
    document.getElementById('maximalPropsBtn').addEventListener('click', () => setQuickMode('maximal'));

    // 批次操作按鈕
    document.getElementById('addRowBtn').addEventListener('click', addBatchRow);
    document.getElementById('copyLastRowBtn').addEventListener('click', copyLastRow);
    document.getElementById('saveAllBtn').addEventListener('click', saveBatchTransactions);
    document.getElementById('cancelBtn').addEventListener('click', () => window.history.back());

    // 預覽按鈕
    document.getElementById('previewBtn').addEventListener('click', previewBatchTransactions);
    document.getElementById('closePreviewBtn').addEventListener('click', () => {
        document.getElementById('previewModal').classList.add('hidden');
    });
}

/**
 * 計算稅額（使用與原系統相同的邏輯）
 * @param {number} amount 含稅金額
 * @param {number} taxRate 稅率
 * @returns {number} 稅額
 */
function calculateTaxAmount(amount, taxRate) {
    // 因為輸入金額屬於含稅，假設未稅A則A*(1+稅率)=B 則稅額=B/A-1=B-A
    const amountNoTax = Math.round(amount / (1 + Number(taxRate))); // 未稅金額
    const taxAmount = amount - amountNoTax; // 稅額
    return taxAmount;
}

/**
 * 計算單行的稅額
 * @param {number} rowNum 行號
 */
function calculateRowTaxAmount(rowNum) {
    const amountInput = document.querySelector(`input[name="batch_amount_${rowNum}"]`);
    if (!amountInput) return;

    const amount = parseFloat(amountInput.value) || 0;
    if (amount <= 0) return;

    // 獲取稅率
    let taxRate = 0;

    // 先檢查是否有個別的稅別設定
    const individualTaxSelect = document.querySelector(`select[name="batch_taxTypeId_${rowNum}"]`);
    if (individualTaxSelect && individualTaxSelect.value) {
        const selectedOption = individualTaxSelect.options[individualTaxSelect.selectedIndex];
        taxRate = parseFloat(selectedOption.dataset.rate) || 0;
    } else {
        // 使用共同稅別設定
        const commonTaxSelect = document.getElementById('batch_taxTypeId');
        if (commonTaxSelect && commonTaxSelect.value) {
            const selectedOption = commonTaxSelect.options[commonTaxSelect.selectedIndex];
            taxRate = parseFloat(selectedOption.dataset.rate) || 0;
        }
    }

    // 計算並顯示稅額
    if (taxRate > 0) {
        const taxAmount = calculateTaxAmount(amount, taxRate);

        // 尋找稅額顯示元素（可能是輸入框或顯示區域）
        let taxAmountElement = document.querySelector(`input[name="batch_taxAmount_${rowNum}"]`);
        if (!taxAmountElement) {
            // 如果沒有專門的稅額輸入框，創建一個顯示區域
            const amountCell = amountInput.closest('td');
            let taxDisplay = amountCell.querySelector('.tax-amount-display');
            if (!taxDisplay) {
                taxDisplay = document.createElement('div');
                taxDisplay.className = 'tax-amount-display text-xs text-gray-500 mt-1';
                amountCell.appendChild(taxDisplay);
            }
            taxDisplay.textContent = `稅額: $${taxAmount.toFixed(2)}`;
        } else {
            taxAmountElement.value = taxAmount.toFixed(2);
        }
    }
}

/**
 * 更新所有行的稅額計算
 */
function updateAllRowsTaxCalculation() {
    const rows = document.querySelectorAll('#batchTableBody tr');
    rows.forEach((row, index) => {
        const rowNum = index + 1;
        calculateRowTaxAmount(rowNum);
    });
}

/**
 * 更新共同屬性表單
 */
function updateCommonPropertiesForm() {
    // 先處理日期欄位群組邏輯
    enforceDateFieldsGrouping();

    const commonPropertiesForm = document.getElementById('commonPropertiesForm');
    let html = '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';

    // 獲取所有被選為共同屬性的欄位
    const selectedCommonProps = [];
    document.querySelectorAll('.common-property-toggle').forEach(checkbox => {
        if (checkbox.checked) {
            selectedCommonProps.push(checkbox.dataset.field);
        }
    });

    // 更新全局共同屬性列表
    commonPropertyFields = selectedCommonProps;
    
    // 為每個共同屬性創建表單元素
    selectedCommonProps.forEach(field => {
        html += `<div class="form-group">
            <label class="block text-sm font-medium text-gray-700">${getFieldLabel(field)}</label>
            ${getFormControlForField(field)}
        </div>`;
    });
    
    html += '</div>';
    commonPropertiesForm.innerHTML = html;
    
    // 為特殊控件添加事件監聽器
    if (selectedCommonProps.includes('transactionType')) {
        const transactionTypeRadios = document.querySelectorAll('input[name="batch_transactionType"]');
        transactionTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // 如果交易描述也是共同屬性，則需要更新
                if (selectedCommonProps.includes('paymentDescription')) {
                    updateCommonPaymentDescriptionSelector(this.value);
                }
            });
        });
    }
    
    // 如果交易描述是共同屬性，初始化選擇器
    if (selectedCommonProps.includes('paymentDescription')) {
        const transactionType = document.querySelector('input[name="batch_transactionType"]:checked')?.value || 'expense';
        initializeCommonPaymentDescriptionSelector(transactionType);
    }
    
    // 如果交易對象是共同屬性，初始化選擇器
    if (selectedCommonProps.includes('entityId')) {
        initializeCommonEntitySelector();
    }
    
    // 重新填充帳戶和稅別選擇器
    populateAccountSelectors();
    populateTaxTypeSelectors();
}

/**
 * 初始化共同交易描述選擇器
 */
function initializeCommonPaymentDescriptionSelector(transactionType) {
    const commonPaymentDescriptionBtn = document.getElementById('commonPaymentDescriptionBtn');
    if (!commonPaymentDescriptionBtn) return;

    // 設置共同交易項目選擇功能
    setupCommonPaymentDescriptionSelector();
}

/**
 * 設置共同交易項目選擇器功能
 */
function setupCommonPaymentDescriptionSelector() {
    const button = document.getElementById('commonPaymentDescriptionBtn');
    const dropdown = button?.parentElement.querySelector('.payment-desc-dropdown');

    if (!button || !dropdown) return;

    // 按鈕點擊事件
    button.addEventListener('click', function(e) {
        e.stopPropagation();

        // 關閉其他下拉選單
        document.querySelectorAll('.payment-desc-dropdown').forEach(dd => {
            if (dd !== dropdown) dd.classList.add('hidden');
        });

        // 切換當前下拉選單
        if (dropdown.classList.contains('hidden')) {
            loadCommonPaymentDescriptionOptions();
            dropdown.classList.remove('hidden');
        } else {
            dropdown.classList.add('hidden');
        }
    });

    // 點擊外部關閉下拉選單
    document.addEventListener('click', function(e) {
        if (!button.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.classList.add('hidden');
        }
    });
}

/**
 * 載入共同交易項目選項
 */
async function loadCommonPaymentDescriptionOptions() {
    const dropdown = document.getElementById('commonPaymentDescriptionBtn')
        ?.parentElement.querySelector('.payment-desc-dropdown');

    if (!dropdown) return;

    try {
        // 獲取當前交易類型
        const transactionType = document.querySelector('input[name="batch_transactionType"]:checked')?.value || 'expense';

        // 載入交易分類和項目
        const categories = await getTransactionCategoriesAll();
        const items = await getTransactionCategoryItemsAll();

        // 根據交易類型篩選分類
        const filteredCategories = categories.filter(cat => cat.type === transactionType);

        // 創建搜尋框
        let html = `
            <div class="p-2 border-b bg-gray-50">
                <input type="text" class="w-full p-2 text-sm border border-gray-300 rounded common-payment-desc-search"
                       placeholder="搜尋交易項目...">
            </div>
            <div class="payment-desc-options max-h-48 overflow-y-auto">
        `;

        // 渲染分類和項目
        filteredCategories.forEach(category => {
            const categoryItems = items.filter(item => item.categoryId === category.id);

            if (categoryItems.length > 0) {
                html += `<div class="px-3 py-2 bg-gray-100 text-sm font-medium text-gray-700 sticky top-0">${category.name}</div>`;

                categoryItems.forEach(item => {
                    html += `
                        <div class="payment-desc-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                             data-code="${item.accountingCode}" data-name="${item.accountingName}">
                            <div class="font-medium text-sm">${item.accountingName}</div>
                            <div class="text-xs text-gray-500">${item.accountingCode}</div>
                        </div>
                    `;
                });
            }
        });

        html += '</div>';

        // 如果沒有項目
        if (filteredCategories.length === 0 || !filteredCategories.some(cat =>
            items.some(item => item.categoryId === cat.id))) {
            html = '<div class="p-4 text-gray-500 text-center">沒有可用的交易項目</div>';
        }

        dropdown.innerHTML = html;

        // 添加搜尋功能
        const searchInput = dropdown.querySelector('.common-payment-desc-search');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                filterCommonPaymentDescriptionOptions(this.value);
            });

            // 聚焦搜尋框
            setTimeout(() => searchInput.focus(), 100);
        }

        // 添加項目點擊事件
        dropdown.querySelectorAll('.payment-desc-item').forEach(item => {
            item.addEventListener('click', function() {
                selectCommonPaymentDescription(this.dataset.code, this.dataset.name);
            });

            // 添加滑鼠懸停高亮
            item.addEventListener('mouseenter', function() {
                dropdown.querySelectorAll('.payment-desc-item').forEach(i => i.classList.remove('bg-blue-50'));
                this.classList.add('bg-blue-50');
            });
        });

    } catch (error) {
        console.error('載入共同交易項目選項失敗:', error);
        dropdown.innerHTML = '<div class="p-4 text-red-500 text-center">載入失敗，請重試</div>';
    }
}

/**
 * 篩選共同交易項目選項
 * @param {string} searchTerm 搜尋關鍵字
 */
function filterCommonPaymentDescriptionOptions(searchTerm) {
    const dropdown = document.getElementById('commonPaymentDescriptionBtn')
        ?.parentElement.querySelector('.payment-desc-dropdown');

    if (!dropdown) return;

    const items = dropdown.querySelectorAll('.payment-desc-item');
    const categories = dropdown.querySelectorAll('.px-3.py-2.bg-gray-100');

    const term = searchTerm.toLowerCase();

    // 隱藏所有分類標題
    categories.forEach(cat => cat.style.display = 'none');

    let hasVisibleItems = false;
    let currentCategory = null;

    items.forEach(item => {
        const name = item.dataset.name.toLowerCase();
        const code = item.dataset.code.toLowerCase();
        const isMatch = name.includes(term) || code.includes(term);

        if (isMatch || !term) {
            item.style.display = 'block';
            hasVisibleItems = true;

            // 顯示對應的分類標題
            let categoryHeader = item.previousElementSibling;
            while (categoryHeader && !categoryHeader.classList.contains('bg-gray-100')) {
                categoryHeader = categoryHeader.previousElementSibling;
            }
            if (categoryHeader && categoryHeader !== currentCategory) {
                categoryHeader.style.display = 'block';
                currentCategory = categoryHeader;
            }
        } else {
            item.style.display = 'none';
        }
    });

    // 如果沒有匹配項目，顯示提示
    let noResultDiv = dropdown.querySelector('.no-results');
    if (!hasVisibleItems && term) {
        if (!noResultDiv) {
            noResultDiv = document.createElement('div');
            noResultDiv.className = 'no-results p-4 text-gray-500 text-center';
            noResultDiv.textContent = '找不到相符的交易項目';
            dropdown.querySelector('.payment-desc-options').appendChild(noResultDiv);
        }
        noResultDiv.style.display = 'block';
    } else if (noResultDiv) {
        noResultDiv.style.display = 'none';
    }
}

/**
 * 選擇共同交易項目
 * @param {string} code 項目代碼
 * @param {string} name 項目名稱
 */
function selectCommonPaymentDescription(code, name) {
    const button = document.getElementById('commonPaymentDescriptionBtn');
    const codeInput = document.getElementById('batch_paymentDescription');
    const nameInput = document.getElementById('batch_paymentDescriptionName');
    const dropdown = button?.parentElement.querySelector('.payment-desc-dropdown');

    if (button && codeInput) {
        button.textContent = name;
        button.title = `${name} (${code})`;
        codeInput.value = code;

        if (nameInput) {
            nameInput.value = name;
        }

        // 移除錯誤樣式
        button.classList.remove('border-red-500', 'bg-red-50');

        // 隱藏下拉選單
        if (dropdown) {
            dropdown.classList.add('hidden');
        }
    }
}

/**
 * 獲取欄位的顯示名稱
 */
function getFieldDisplayName(field) {
    const fieldNames = {
        'transactionType': '交易類型',
        'accountId': '我方主要帳戶',
        'paymentDate': '收款/付款日期',
        'paymentStatus': '帳款到帳情形',
        'invoiceDate': '憑證/發票日期',
        'expectedPaymentDate': '預計收/付款日期',
        'taxTypeId': '稅別',
        'paymentDescription': '交易項目',
        'entityId': '交易對象/帳戶',
        'amount': '金額 (含稅)',
        'taxAmount': '稅額',
        'invoiceNumber': '發票號碼',
        'fee': '手續費',
        'tags': '標籤',
        'notes': '備註'
    };
    return fieldNames[field] || field;
}

/**
 * 設置標籤輸入功能
 * @param {number} rowNum 行號
 */
function setupTagsInput(rowNum) {
    const tagsInput = document.querySelector(`input.tags-input[data-row="${rowNum}"]`);
    const tagsContainer = document.getElementById(`tagsContainer_${rowNum}`);
    const hiddenInput = document.querySelector(`input[name="batch_tagsData_${rowNum}"]`);

    if (!tagsInput || !tagsContainer || !hiddenInput) return;

    let tags = [];

    // 載入已存在的標籤
    if (hiddenInput.value) {
        try {
            tags = JSON.parse(hiddenInput.value);
            renderTags();
        } catch (e) {
            console.warn('解析標籤資料失敗:', e);
        }
    }

    // Enter 鍵添加標籤
    tagsInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const tagText = this.value.trim();
            if (tagText && !tags.includes(tagText)) {
                tags.push(tagText);
                this.value = '';
                updateTagsData();
                renderTags();
            }
        }
    });

    // 渲染標籤
    function renderTags() {
        tagsContainer.innerHTML = '';
        tags.forEach((tag, index) => {
            const tagElement = document.createElement('span');
            tagElement.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
            tagElement.innerHTML = `
                ${tag}
                <button type="button" class="ml-1 text-blue-600 hover:text-blue-800" onclick="removeTag(${rowNum}, ${index})">
                    <i class="fas fa-times text-xs"></i>
                </button>
            `;
            tagsContainer.appendChild(tagElement);
        });
    }

    // 更新隱藏欄位資料
    function updateTagsData() {
        hiddenInput.value = JSON.stringify(tags);
    }

    // 全域函式：移除標籤
    window[`removeTag_${rowNum}`] = function(tagIndex) {
        tags.splice(tagIndex, 1);
        updateTagsData();
        renderTags();
    };
}

/**
 * 移除標籤的全域函式
 * @param {number} rowNum 行號
 * @param {number} tagIndex 標籤索引
 */
function removeTag(rowNum, tagIndex) {
    if (window[`removeTag_${rowNum}`]) {
        window[`removeTag_${rowNum}`](tagIndex);
    }
}

/**
 * 處理到帳情形變更，動態控制日期欄位顯示
 * 基於 transactionCreate_PaymentStatus_Module.js 的邏輯
 */
function handlePaymentStatusChange(event) {
    const paymentStatusSelect = event ? event.target : document.getElementById('batch_paymentStatus');
    if (!paymentStatusSelect) return;

    const paymentStatus = paymentStatusSelect.value;
    const selectId = paymentStatusSelect.id || paymentStatusSelect.name;

    // 判斷是共同屬性還是個別屬性
    if (selectId === 'batch_paymentStatus') {
        // 共同屬性：更新共同屬性區域的日期欄位
        updateCommonDateFieldsVisibility(paymentStatus);
    } else {
        // 個別屬性：更新當前行的日期欄位
        const rowMatch = selectId.match(/batch_paymentStatus_(\d+)/);
        if (rowMatch) {
            const rowNum = parseInt(rowMatch[1]);
            updateRowDateFieldsVisibility(rowNum, paymentStatus);
        }
    }
}

/**
 * 更新日期欄位的顯示狀態
 * @param {string} paymentStatus 到帳情形
 */
function updateDateFieldsVisibility(paymentStatus) {
    // 根據到帳情形決定哪些日期欄位需要顯示
    const dateFieldsConfig = getDateFieldsConfig(paymentStatus);

    // 更新共同屬性中的日期欄位
    updateCommonDateFields(dateFieldsConfig);

    // 更新個別屬性中的日期欄位
    updateIndividualDateFields(dateFieldsConfig);
}

/**
 * 獲取日期欄位配置
 * @param {string} paymentStatus 到帳情形
 * @returns {Object} 日期欄位配置
 */
function getDateFieldsConfig(paymentStatus) {
    switch (paymentStatus) {
        case 'same_day': // 同日收付款
            return {
                paymentDate: { show: true, required: true, label: '收款/付款日期' },
                invoiceDate: { show: true, required: true, label: '憑證/發票日期' },
                expectedPaymentDate: { show: false, required: false }
            };
        case 'receivable': // 應收付款
            return {
                paymentDate: { show: false, required: false },
                invoiceDate: { show: true, required: true, label: '憑證/發票日期' },
                expectedPaymentDate: { show: true, required: true, label: '預計收/付款日期' }
            };
        case 'prepayment': // 暫收付款
            return {
                paymentDate: { show: true, required: true, label: '收款/付款日期' },
                invoiceDate: { show: false, required: false },
                expectedPaymentDate: { show: false, required: false }
            };
        case 'different_day': // 非同日收款
            return {
                paymentDate: { show: true, required: true, label: '收款/付款日期' },
                invoiceDate: { show: true, required: true, label: '憑證/發票日期' },
                expectedPaymentDate: { show: true, required: true, label: '預計收/付款日期' }
            };
        default:
            return {
                paymentDate: { show: true, required: true, label: '收款/付款日期' },
                invoiceDate: { show: true, required: true, label: '憑證/發票日期' },
                expectedPaymentDate: { show: false, required: false }
            };
    }
}

/**
 * 更新共同日期欄位
 * @param {Object} config 日期欄位配置
 */
function updateCommonDateFields(config) {
    // 更新共同屬性中的日期欄位顯示
    ['paymentDate', 'invoiceDate', 'expectedPaymentDate'].forEach(field => {
        const checkbox = document.querySelector(`.common-property-toggle[data-field="${field}"]`);
        const container = checkbox?.closest('.flex.items-center');

        if (container) {
            if (config[field]?.show) {
                container.style.display = 'flex';
                // 更新標籤
                const label = container.querySelector('span');
                if (label && config[field].label) {
                    label.textContent = config[field].label;
                }
            } else {
                container.style.display = 'none';
                // 取消勾選
                if (checkbox) {
                    checkbox.checked = false;
                }
            }
        }
    });
}

/**
 * 更新共同屬性區域的日期欄位顯示
 * @param {string} paymentStatus 到帳情形
 */
function updateCommonDateFieldsVisibility(paymentStatus) {
    const config = getDateFieldsConfig(paymentStatus);

    // 更新共同屬性表單中的日期欄位
    ['paymentDate', 'invoiceDate', 'expectedPaymentDate'].forEach(field => {
        const fieldContainer = document.querySelector(`#commonPropertiesForm .form-group:has([name="batch_${field}"])`);
        if (fieldContainer) {
            if (config[field]?.show) {
                fieldContainer.style.display = 'block';
                const input = fieldContainer.querySelector(`[name="batch_${field}"]`);
                if (input) {
                    input.disabled = false;
                    input.style.backgroundColor = '';
                }
            } else {
                fieldContainer.style.display = 'none';
            }
        }
    });
}

/**
 * 更新個別行的日期欄位顯示
 * @param {number} rowNum 行號
 * @param {string} paymentStatus 到帳情形
 */
function updateRowDateFieldsVisibility(rowNum, paymentStatus) {
    const config = getDateFieldsConfig(paymentStatus);

    // 更新該行的日期欄位
    ['paymentDate', 'invoiceDate', 'expectedPaymentDate'].forEach(field => {
        const input = document.querySelector(`[name="batch_${field}_${rowNum}"]`);
        if (input) {
            if (config[field]?.show) {
                input.disabled = false;
                input.style.backgroundColor = '';
                input.placeholder = config[field].label || '';
            } else {
                input.disabled = true;
                input.style.backgroundColor = '#f3f4f6';
                input.value = '';
                input.placeholder = '不適用';
            }
        }
    });
}

/**
 * 根據到帳情形獲取應該顯示的日期欄位
 * @param {string} paymentStatus 到帳情形
 * @returns {Array} 應該顯示的日期欄位陣列
 */
function getVisibleDateFields(paymentStatus) {
    const config = getDateFieldsConfig(paymentStatus);
    const visibleFields = [];

    if (config.paymentDate?.show) {
        visibleFields.push('paymentDate');
    }
    if (config.invoiceDate?.show) {
        visibleFields.push('invoiceDate');
    }
    if (config.expectedPaymentDate?.show) {
        visibleFields.push('expectedPaymentDate');
    }

    return visibleFields;
}

/**
 * 根據到帳情形獲取應該顯示的日期與到帳情形欄位
 * @param {string} paymentStatus 到帳情形
 * @returns {Array} 應該顯示的日期與到帳情形欄位陣列
 */
function getVisibleDateAndPaymentFields(paymentStatus) {
    const visibleFields = ['paymentStatus']; // 到帳情形始終顯示
    const dateFields = getVisibleDateFields(paymentStatus);
    return [...visibleFields, ...dateFields];
}

/**
 * 強制日期與到帳情形欄位群組的一致性
 * 確保日期與到帳情形相關欄位要麼全部在共同屬性，要麼全部在個別屬性
 */
function enforceDateFieldsGrouping() {
    const dateAndPaymentFields = ['paymentStatus', 'paymentDate', 'invoiceDate', 'expectedPaymentDate'];

    // 檢查日期與到帳情形欄位的當前狀態
    const fieldsStatus = dateAndPaymentFields.map(field => {
        const checkbox = document.querySelector(`.common-property-toggle[data-field="${field}"]`);
        return {
            field: field,
            checkbox: checkbox,
            checked: checkbox ? checkbox.checked : false
        };
    });

    // 計算已選中的欄位數量
    const checkedCount = fieldsStatus.filter(item => item.checked).length;

    // 如果部分選中，則根據多數決定統一狀態
    if (checkedCount > 0 && checkedCount < dateAndPaymentFields.length) {
        const shouldAllBeChecked = checkedCount >= dateAndPaymentFields.length / 2;

        fieldsStatus.forEach(item => {
            if (item.checkbox) {
                item.checkbox.checked = shouldAllBeChecked;
            }
        });

        // 顯示提示訊息
        console.log(`日期與到帳情形群組已統一設置為: ${shouldAllBeChecked ? '共同屬性' : '個別屬性'}`);
    }
}

/**
 * 同步日期與到帳情形欄位群組
 * 當用戶點擊任何一個群組欄位時，同步其他欄位的狀態
 * @param {HTMLElement} changedCheckbox 被改變的複選框
 */
function syncDateFieldsGroup(changedCheckbox) {
    const dateAndPaymentFields = ['paymentStatus', 'paymentDate', 'invoiceDate', 'expectedPaymentDate'];
    const newState = changedCheckbox.checked;

    // 同步所有日期與到帳情形欄位到相同狀態
    dateAndPaymentFields.forEach(field => {
        const checkbox = document.querySelector(`.common-property-toggle[data-field="${field}"]`);
        if (checkbox && checkbox !== changedCheckbox) {
            checkbox.checked = newState;
        }
    });

    // 顯示提示訊息
    const statusText = newState ? '共同屬性' : '個別屬性';
    console.log(`日期與到帳情形群組已同步設置為: ${statusText}`);

    // 可選：顯示用戶友好的提示
    showDateFieldsGroupNotification(statusText);
}

/**
 * 顯示日期與到帳情形欄位群組變更通知
 * @param {string} statusText 狀態文字
 */
function showDateFieldsGroupNotification(statusText) {
    // 創建臨時通知
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity duration-300';
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-calendar-alt mr-2"></i>
            <span class="text-sm">日期與到帳情形群組已設為: ${statusText}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // 2秒後自動移除
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 2000);
}

/**
 * 處理稅別變更，自動計算稅額
 * @param {HTMLElement} taxSelect 稅別選擇器
 * @param {number} rowNum 行號，null表示共同屬性
 */
function handleTaxTypeChange(taxSelect, rowNum) {
    const selectedOption = taxSelect.options[taxSelect.selectedIndex];
    const taxRate = selectedOption ? parseFloat(selectedOption.dataset.rate) || 0 : 0;

    if (rowNum === null) {
        // 共同屬性：更新所有行的稅額計算
        updateAllRowsTaxCalculation(taxRate);
    } else {
        // 個別屬性：更新當前行的稅額計算
        updateRowTaxCalculation(rowNum, taxRate);
    }
}

/**
 * 更新所有行的稅額計算
 * @param {number} taxRate 稅率
 */
function updateAllRowsTaxCalculation(taxRate) {
    const rows = document.querySelectorAll('#batchTableBody tr');
    rows.forEach((row, index) => {
        const rowNum = index + 1;
        updateRowTaxCalculation(rowNum, taxRate);
    });
}

/**
 * 更新指定行的稅額計算
 * @param {number} rowNum 行號
 * @param {number} taxRate 稅率
 */
function updateRowTaxCalculation(rowNum, taxRate) {
    const amountInput = document.querySelector(`[name="batch_amount_${rowNum}"]`);
    const taxAmountInput = document.querySelector(`[name="batch_taxAmount_${rowNum}"]`);

    if (amountInput && taxAmountInput) {
        const amount = parseFloat(amountInput.value) || 0;
        if (amount > 0 && taxRate > 0) {
            const taxAmount = calculateTaxAmount(amount, taxRate);
            taxAmountInput.value = taxAmount.toFixed(2);
        } else {
            taxAmountInput.value = '';
        }
    }
}

/**
 * 更新共同交易描述選擇器
 */
function updateCommonPaymentDescriptionSelector(transactionType) {
    const commonPaymentDescriptionBtn = document.getElementById('commonPaymentDescriptionBtn');
    const commonPaymentDescriptionInput = document.getElementById('batch_paymentDescription');
    
    if (commonPaymentDescriptionBtn && commonPaymentDescriptionInput) {
        // 重置選擇
        commonPaymentDescriptionBtn.textContent = '選擇交易描述';
        commonPaymentDescriptionInput.value = '';
        
        // 更新點擊事件
        const newBtn = commonPaymentDescriptionBtn.cloneNode(true);
        commonPaymentDescriptionBtn.parentNode.replaceChild(newBtn, commonPaymentDescriptionBtn);
        
        newBtn.addEventListener('click', async () => {
            await showPaymentDescriptionMegaMenu(transactionType, (code, name) => {
                document.getElementById('batch_paymentDescription').value = code;
                newBtn.textContent = name;
            });
        });
    }
}

/**
 * 初始化共同交易對象選擇器
 */
function initializeCommonEntitySelector() {
    const commonEntitySearch = document.getElementById('commonEntitySearch');
    if (!commonEntitySearch) return;

    // 設置共同交易對象搜尋功能
    setupCommonEntitySearch();
}

/**
 * 設置共同交易對象搜尋功能
 */
function setupCommonEntitySearch() {
    const searchInput = document.getElementById('commonEntitySearch');
    const dropdown = searchInput?.parentElement.querySelector('.entity-search-dropdown');

    if (!searchInput || !dropdown) return;

    let searchTimeout;

    // 搜尋輸入事件
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        searchTimeout = setTimeout(() => {
            if (query.length >= 1) {
                performCommonEntitySearch(query);
            } else {
                showCommonRecentEntities();
            }
        }, 300);
    });

    // 聚焦時顯示最近使用的交易對象
    searchInput.addEventListener('focus', function() {
        if (!this.value.trim()) {
            showCommonRecentEntities();
        } else {
            dropdown.classList.remove('hidden');
        }
    });

    // 失焦時隱藏下拉選單
    searchInput.addEventListener('blur', function() {
        setTimeout(() => {
            dropdown.classList.add('hidden');
        }, 200);
    });

    // 鍵盤導航支援
    searchInput.addEventListener('keydown', function(e) {
        const items = dropdown.querySelectorAll('.entity-search-item');
        let currentIndex = Array.from(items).findIndex(item => item.classList.contains('highlighted'));

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentIndex < items.length - 1) {
                    if (currentIndex >= 0) items[currentIndex].classList.remove('highlighted');
                    items[currentIndex + 1].classList.add('highlighted');
                }
                break;
            case 'ArrowUp':
                e.preventDefault();
                if (currentIndex > 0) {
                    items[currentIndex].classList.remove('highlighted');
                    items[currentIndex - 1].classList.add('highlighted');
                }
                break;
            case 'Enter':
                e.preventDefault();
                if (currentIndex >= 0) {
                    items[currentIndex].click();
                }
                break;
            case 'Escape':
                dropdown.classList.add('hidden');
                break;
        }
    });
}

/**
 * 執行共同交易對象搜尋
 * @param {string} query 搜尋關鍵字
 */
async function performCommonEntitySearch(query) {
    const dropdown = document.getElementById('commonEntitySearch')
        ?.parentElement.querySelector('.entity-search-dropdown');

    if (!dropdown) return;

    try {
        const employees = await getEmployeesAll();
        const matchedEmployees = employees.filter(emp =>
            emp.name.toLowerCase().includes(query.toLowerCase()) ||
            (emp.code && emp.code.toLowerCase().includes(query.toLowerCase()))
        ).slice(0, 5);

        const entities = await getEntitiesAll();
        const matchedEntities = entities.filter(entity =>
            entity.name.toLowerCase().includes(query.toLowerCase()) ||
            (entity.code && entity.code.toLowerCase().includes(query.toLowerCase()))
        ).slice(0, 5);

        displayCommonEntitySearchResults(dropdown, matchedEmployees, matchedEntities);
        dropdown.classList.remove('hidden');

    } catch (error) {
        console.error('搜尋共同交易對象失敗:', error);
        dropdown.innerHTML = '<div class="p-2 text-red-500 text-sm">搜尋失敗，請重試</div>';
        dropdown.classList.remove('hidden');
    }
}

/**
 * 顯示共同最近使用的交易對象
 */
async function showCommonRecentEntities() {
    const dropdown = document.getElementById('commonEntitySearch')
        ?.parentElement.querySelector('.entity-search-dropdown');

    if (!dropdown) return;

    try {
        const employees = await getEmployeesAll();
        const entities = await getEntitiesAll();

        const recentEmployees = employees.slice(0, 3);
        const recentEntities = entities.slice(0, 3);

        displayCommonEntitySearchResults(dropdown, recentEmployees, recentEntities, true);
        dropdown.classList.remove('hidden');

    } catch (error) {
        console.error('載入最近交易對象失敗:', error);
    }
}

/**
 * 顯示共同交易對象搜尋結果
 * @param {HTMLElement} dropdown 下拉選單元素
 * @param {Array} employees 員工陣列
 * @param {Array} entities 交易對象陣列
 * @param {boolean} isRecent 是否為最近使用
 */
function displayCommonEntitySearchResults(dropdown, employees, entities, isRecent = false) {
    let html = '';

    if (isRecent) {
        html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">最近使用</div>';
    }

    // 添加員工結果
    if (employees.length > 0) {
        if (!isRecent) {
            html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">員工</div>';
        }

        employees.forEach(employee => {
            html += `
                <div class="entity-search-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                     data-id="${employee.id}" data-type="employee" data-name="${employee.name}">
                    <div class="font-medium text-sm">${employee.name}</div>
                    <div class="text-xs text-gray-500">${employee.code || '員工'}</div>
                </div>
            `;
        });
    }

    // 添加交易對象結果
    if (entities.length > 0) {
        if (!isRecent && employees.length > 0) {
            html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">交易對象</div>';
        }

        entities.forEach(entity => {
            html += `
                <div class="entity-search-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                     data-id="${entity.id}" data-type="entity" data-name="${entity.name}">
                    <div class="font-medium text-sm">${entity.name}</div>
                    <div class="text-xs text-gray-500">${entity.code || getEntityTypeName(entity.type || 'company')}</div>
                </div>
            `;
        });
    }

    if (employees.length === 0 && entities.length === 0) {
        html = '<div class="p-2 text-gray-500 text-sm">找不到相符的交易對象</div>';
    }

    // 添加新增臨時對象選項
    if (!isRecent) {
        html += `
            <div class="border-t border-gray-200 mt-1">
                <div class="entity-search-item p-2 hover:bg-green-50 cursor-pointer text-green-600"
                     data-action="create-temp">
                    <div class="font-medium text-sm">
                        <i class="fas fa-plus mr-1"></i>新增臨時交易對象
                    </div>
                </div>
            </div>
        `;
    }

    dropdown.innerHTML = html;

    // 添加點擊事件
    dropdown.querySelectorAll('.entity-search-item').forEach(item => {
        item.addEventListener('click', function() {
            if (this.dataset.action === 'create-temp') {
                createCommonTemporaryEntity();
            } else {
                selectCommonEntity(this.dataset.id, this.dataset.type, this.dataset.name);
            }
        });

        item.addEventListener('mouseenter', function() {
            dropdown.querySelectorAll('.entity-search-item').forEach(i => i.classList.remove('highlighted'));
            this.classList.add('highlighted');
        });
    });
}

/**
 * 選擇共同交易對象
 * @param {string} entityId 交易對象 ID
 * @param {string} entityType 交易對象類型
 * @param {string} entityName 交易對象名稱
 */
function selectCommonEntity(entityId, entityType, entityName) {
    const searchInput = document.getElementById('commonEntitySearch');
    const idInput = document.getElementById('batch_entityId');
    const typeInput = document.getElementById('batch_entityType');
    const dropdown = searchInput?.parentElement.querySelector('.entity-search-dropdown');

    if (searchInput && idInput && typeInput) {
        searchInput.value = entityName;
        idInput.value = entityId;
        typeInput.value = entityType;

        searchInput.classList.remove('border-red-500', 'bg-red-50');

        if (dropdown) {
            dropdown.classList.add('hidden');
        }
    }
}

/**
 * 創建共同臨時交易對象
 */
async function createCommonTemporaryEntity() {
    const name = prompt('請輸入臨時交易對象名稱：');
    if (!name || !name.trim()) return;

    try {
        const entity = await entitySearchManager.createTemporaryEntity(name.trim());
        if (entity) {
            selectCommonEntity(entity.id, entity.type, entity.name);
        }
    } catch (error) {
        console.error('創建臨時交易對象失敗:', error);
        alert('創建臨時交易對象失敗，請重試');
    }
}

/**
 * 為指定行設置交易項目選擇器功能
 * @param {number} rowNum 行號
 */
function setupPaymentDescriptionSelector(rowNum) {
    const button = document.querySelector(`.payment-desc-btn[data-row="${rowNum}"]`);
    const dropdown = button?.parentElement.querySelector('.payment-desc-dropdown');

    if (!button || !dropdown) return;

    // 按鈕點擊事件
    button.addEventListener('click', function(e) {
        e.stopPropagation();

        // 關閉其他下拉選單
        document.querySelectorAll('.payment-desc-dropdown').forEach(dd => {
            if (dd !== dropdown) dd.classList.add('hidden');
        });

        // 切換當前下拉選單
        if (dropdown.classList.contains('hidden')) {
            loadPaymentDescriptionOptions(rowNum);
            dropdown.classList.remove('hidden');
        } else {
            dropdown.classList.add('hidden');
        }
    });

    // 點擊外部關閉下拉選單
    document.addEventListener('click', function(e) {
        if (!button.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.classList.add('hidden');
        }
    });
}

/**
 * 載入交易項目選項
 * @param {number} rowNum 行號
 */
async function loadPaymentDescriptionOptions(rowNum) {
    const dropdown = document.querySelector(`.payment-desc-btn[data-row="${rowNum}"]`)
        ?.parentElement.querySelector('.payment-desc-dropdown');

    if (!dropdown) return;

    try {
        // 獲取當前交易類型
        const transactionType = document.querySelector('input[name="batch_transactionType"]:checked')?.value || 'expense';

        // 載入交易分類和項目
        const categories = await getTransactionCategoriesAll();
        const items = await getTransactionCategoryItemsAll();

        // 根據交易類型篩選分類
        const filteredCategories = categories.filter(cat => cat.type === transactionType);

        // 創建搜尋框
        let html = `
            <div class="p-2 border-b bg-gray-50">
                <input type="text" class="w-full p-2 text-sm border border-gray-300 rounded payment-desc-search"
                       placeholder="搜尋交易項目..." data-row="${rowNum}">
            </div>
            <div class="payment-desc-options max-h-48 overflow-y-auto">
        `;

        // 渲染分類和項目
        filteredCategories.forEach(category => {
            const categoryItems = items.filter(item => item.categoryId === category.id);

            if (categoryItems.length > 0) {
                html += `<div class="px-3 py-2 bg-gray-100 text-sm font-medium text-gray-700 sticky top-0">${category.name}</div>`;

                categoryItems.forEach(item => {
                    html += `
                        <div class="payment-desc-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                             data-code="${item.accountingCode}" data-name="${item.accountingName}">
                            <div class="font-medium text-sm">${item.accountingName}</div>
                            <div class="text-xs text-gray-500">${item.accountingCode}</div>
                        </div>
                    `;
                });
            }
        });

        html += '</div>';

        // 如果沒有項目
        if (filteredCategories.length === 0 || !filteredCategories.some(cat =>
            items.some(item => item.categoryId === cat.id))) {
            html = '<div class="p-4 text-gray-500 text-center">沒有可用的交易項目</div>';
        }

        dropdown.innerHTML = html;

        // 添加搜尋功能
        const searchInput = dropdown.querySelector('.payment-desc-search');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                filterPaymentDescriptionOptions(rowNum, this.value);
            });

            // 聚焦搜尋框
            setTimeout(() => searchInput.focus(), 100);
        }

        // 添加項目點擊事件
        dropdown.querySelectorAll('.payment-desc-item').forEach(item => {
            item.addEventListener('click', function() {
                selectPaymentDescription(rowNum, this.dataset.code, this.dataset.name);
            });

            // 添加滑鼠懸停高亮
            item.addEventListener('mouseenter', function() {
                dropdown.querySelectorAll('.payment-desc-item').forEach(i => i.classList.remove('bg-blue-50'));
                this.classList.add('bg-blue-50');
            });
        });

    } catch (error) {
        console.error('載入交易項目選項失敗:', error);
        dropdown.innerHTML = '<div class="p-4 text-red-500 text-center">載入失敗，請重試</div>';
    }
}

/**
 * 篩選交易項目選項
 * @param {number} rowNum 行號
 * @param {string} searchTerm 搜尋關鍵字
 */
function filterPaymentDescriptionOptions(rowNum, searchTerm) {
    const dropdown = document.querySelector(`.payment-desc-btn[data-row="${rowNum}"]`)
        ?.parentElement.querySelector('.payment-desc-dropdown');

    if (!dropdown) return;

    const items = dropdown.querySelectorAll('.payment-desc-item');
    const categories = dropdown.querySelectorAll('.px-3.py-2.bg-gray-100');

    const term = searchTerm.toLowerCase();

    // 隱藏所有分類標題
    categories.forEach(cat => cat.style.display = 'none');

    let hasVisibleItems = false;
    let currentCategory = null;

    items.forEach(item => {
        const name = item.dataset.name.toLowerCase();
        const code = item.dataset.code.toLowerCase();
        const isMatch = name.includes(term) || code.includes(term);

        if (isMatch || !term) {
            item.style.display = 'block';
            hasVisibleItems = true;

            // 顯示對應的分類標題
            let categoryHeader = item.previousElementSibling;
            while (categoryHeader && !categoryHeader.classList.contains('bg-gray-100')) {
                categoryHeader = categoryHeader.previousElementSibling;
            }
            if (categoryHeader && categoryHeader !== currentCategory) {
                categoryHeader.style.display = 'block';
                currentCategory = categoryHeader;
            }
        } else {
            item.style.display = 'none';
        }
    });

    // 如果沒有匹配項目，顯示提示
    let noResultDiv = dropdown.querySelector('.no-results');
    if (!hasVisibleItems && term) {
        if (!noResultDiv) {
            noResultDiv = document.createElement('div');
            noResultDiv.className = 'no-results p-4 text-gray-500 text-center';
            noResultDiv.textContent = '找不到相符的交易項目';
            dropdown.querySelector('.payment-desc-options').appendChild(noResultDiv);
        }
        noResultDiv.style.display = 'block';
    } else if (noResultDiv) {
        noResultDiv.style.display = 'none';
    }
}

/**
 * 選擇交易項目
 * @param {number} rowNum 行號
 * @param {string} code 項目代碼
 * @param {string} name 項目名稱
 */
function selectPaymentDescription(rowNum, code, name) {
    const button = document.querySelector(`.payment-desc-btn[data-row="${rowNum}"]`);
    const codeInput = document.querySelector(`input[name="batch_paymentDescription_${rowNum}"]`);
    const nameInput = document.querySelector(`input[name="batch_paymentDescriptionName_${rowNum}"]`);
    const dropdown = button?.parentElement.querySelector('.payment-desc-dropdown');

    if (button && codeInput) {
        button.textContent = name;
        button.title = `${name} (${code})`;
        codeInput.value = code;

        if (nameInput) {
            nameInput.value = name;
        }

        // 移除錯誤樣式
        button.classList.remove('border-red-500', 'bg-red-50');

        // 隱藏下拉選單
        if (dropdown) {
            dropdown.classList.add('hidden');
        }
    }
}

/**
 * 更新批次表格列
 */
function updateBatchTableColumns() {
    // 定義日期與到帳情形群組
    const dateAndPaymentFields = ['paymentStatus', 'paymentDate', 'invoiceDate', 'expectedPaymentDate'];

    // 獲取所有未被選為共同屬性的欄位
    const individualFields = [];
    const allFields = ['transactionType', 'accountId', 'taxTypeId', 'paymentDescription', 'entityId'];

    // 檢查日期與到帳情形群組的狀態
    const dateAndPaymentFieldsChecked = dateAndPaymentFields.map(field => {
        const checkbox = document.querySelector(`.common-property-toggle[data-field="${field}"]`);
        return checkbox && checkbox.checked;
    });

    // 日期與到帳情形群組邏輯：如果任何一個欄位被選為共同屬性，則所有欄位都作為共同屬性
    const anyDateAndPaymentFieldChecked = dateAndPaymentFieldsChecked.some(checked => checked);
    const allDateAndPaymentFieldsChecked = dateAndPaymentFieldsChecked.every(checked => checked);

    // 如果日期與到帳情形欄位狀態不一致，統一設置
    if (anyDateAndPaymentFieldChecked && !allDateAndPaymentFieldsChecked) {
        // 將所有日期與到帳情形欄位設為共同屬性
        dateAndPaymentFields.forEach(field => {
            const checkbox = document.querySelector(`.common-property-toggle[data-field="${field}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    // 處理非日期與到帳情形的欄位
    allFields.forEach(field => {
        const checkbox = document.querySelector(`.common-property-toggle[data-field="${field}"]`);
        if (checkbox && !checkbox.checked) {
            individualFields.push(field);
        }
    });

    // 如果日期與到帳情形欄位不是全部作為共同屬性，則添加到個別屬性
    if (!anyDateAndPaymentFieldChecked || !allDateAndPaymentFieldsChecked) {
        // 根據到帳情形決定顯示哪些日期欄位
        const paymentStatus = document.getElementById('batch_paymentStatus')?.value || '';
        const visibleDateAndPaymentFields = getVisibleDateAndPaymentFields(paymentStatus);
        individualFields.push(...visibleDateAndPaymentFields);
    }

    // 更新全局個別屬性列表 - 包含所有個別屬性欄位
    individualPropertyFields = ['amount', 'taxAmount', 'invoiceNumber', 'fee', 'tags', ...individualFields, 'notes'];

    // 重建表格頭 - 兩列式佈局
    const tableHeader = document.querySelector('#batchTableHeader');
    const tableSubHeader = document.querySelector('#batchTableSubHeader');

    // 清空現有標題
    tableHeader.innerHTML = '<th class="p-2 border text-center" rowspan="2">序號</th>';
    tableSubHeader.innerHTML = '';

    // 將欄位分成兩列，每個欄位只出現一次
    const fieldsPerRow = Math.ceil(individualPropertyFields.length / 2);
    const firstRowFields = individualPropertyFields.slice(0, fieldsPerRow);
    const secondRowFields = individualPropertyFields.slice(fieldsPerRow);

    // 添加第一列標題
    firstRowFields.forEach(field => {
        tableHeader.innerHTML += `<th class="p-2 border text-center">${getFieldLabel(field)}</th>`;
    });

    // 添加操作列
    tableHeader.innerHTML += '<th class="p-2 border text-center" rowspan="2">操作</th>';

    // 添加第二列標題
    secondRowFields.forEach(field => {
        tableSubHeader.innerHTML += `<th class="p-2 border text-center">${getFieldLabel(field)}</th>`;
    });

    // 如果第二列欄位較少，添加空白欄位以對齊
    const emptyColumns = firstRowFields.length - secondRowFields.length;
    for (let i = 0; i < emptyColumns; i++) {
        tableSubHeader.innerHTML += '<th class="p-2 border text-center">-</th>';
    }

    // 儲存欄位分配資訊供行生成使用
    window.tableFieldsLayout = {
        firstRowFields: firstRowFields,
        secondRowFields: secondRowFields
    };
    
    // 更新現有行
    updateExistingBatchRows();
}

/**
 * 更新現有的批次行
 */
function updateExistingBatchRows() {
    // 獲取所有邏輯行（每個邏輯行包含兩個 tr 元素）
    const firstRows = document.querySelectorAll('#batchTableBody tr.batch-row-first');

    firstRows.forEach((firstRow) => {
        const rowNum = parseInt(firstRow.dataset.rowNum);
        
        // 保存現有值
        const savedValues = {};
        individualPropertyFields.forEach(field => {
            const input = document.querySelector(`[name="batch_${field}_${rowNum}"]`);
            if (input) {
                if (input.type === 'checkbox') {
                    savedValues[field] = input.checked;
                } else {
                    savedValues[field] = input.value || '';
                }
            }

            // 特殊處理某些欄位的額外資料
            if (field === 'paymentDescription') {
                const btn = document.querySelector(`[data-row="${rowNum}"].payment-desc-btn`);
                savedValues[`${field}_text`] = btn?.textContent || '選擇交易項目';
            } else if (field === 'entityId') {
                const typeInput = document.querySelector(`input[name="batch_entityType_${rowNum}"]`);
                savedValues['entityType'] = typeInput?.value || 'company';
                const searchInput = document.querySelector(`[data-row="${rowNum}"].entity-search-input`);
                savedValues[`${field}_text`] = searchInput?.value || '';
            } else if (field === 'tags') {
                const tagsDataInput = document.querySelector(`input[name="batch_tagsData_${rowNum}"]`);
                savedValues[field] = tagsDataInput?.value || '';
            }
        });

        // 獲取對應的第二行
        const secondRow = document.querySelector(`#batchTableBody tr.batch-row-second[data-row-num="${rowNum}"]`);

        // 移除舊的行
        if (firstRow.parentNode) {
            firstRow.parentNode.removeChild(firstRow);
        }
        if (secondRow && secondRow.parentNode) {
            secondRow.parentNode.removeChild(secondRow);
        }

        // 重新創建行
        createBatchRowElements(rowNum);

        // 恢復保存的值並設置事件監聽器
        setTimeout(() => {
            restoreRowValues(rowNum, savedValues);
            setupRowEventListeners(rowNum);
        }, 0);
    });
}

/**
 * 設置行的事件監聽器和功能
 * @param {number} rowNum 行號
 */
function setupRowEventListeners(rowNum) {
    console.log(`設置行 ${rowNum} 的事件監聽器`);

    // 設置交易項目選擇器
    const paymentDescBtn = document.querySelector(`.payment-desc-btn[data-row="${rowNum}"]`);
    console.log(`交易項目按鈕:`, paymentDescBtn);
    if (paymentDescBtn) {
        setupPaymentDescriptionSelector(rowNum);
    }

    // 設置交易對象搜尋
    const entitySearchInput = document.querySelector(`.entity-search-input[data-row="${rowNum}"]`);
    console.log(`交易對象輸入:`, entitySearchInput);
    if (entitySearchInput) {
        setupEntitySearch(rowNum);
    }

    // 設置標籤輸入
    const tagsInput = document.querySelector(`.tags-input[data-row="${rowNum}"]`);
    console.log(`標籤輸入:`, tagsInput);
    if (tagsInput) {
        setupTagsInput(rowNum);
    }

    // 設置金額輸入的稅額計算
    const amountInput = document.querySelector(`input[name="batch_amount_${rowNum}"]`);
    console.log(`金額輸入:`, amountInput);
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            calculateRowTaxAmount(rowNum);
        });

        // 初始計算稅額
        if (amountInput.value) {
            calculateRowTaxAmount(rowNum);
        }
    }

    // 設置到帳情形變更
    const paymentStatusSelect = document.querySelector(`select[name="batch_paymentStatus_${rowNum}"]`);
    console.log(`到帳情形選擇:`, paymentStatusSelect);
    if (paymentStatusSelect) {
        paymentStatusSelect.addEventListener('change', handlePaymentStatusChange);
    }

    // 設置稅別變更
    const taxTypeSelect = document.querySelector(`select[name="batch_taxTypeId_${rowNum}"]`);
    console.log(`稅別選擇:`, taxTypeSelect);
    if (taxTypeSelect) {
        taxTypeSelect.addEventListener('change', function() {
            handleTaxTypeChange(this, rowNum);
        });
    }
}

/**
 * 恢復行的保存值
 * @param {number} rowNum 行號
 * @param {Object} savedValues 保存的值
 */
function restoreRowValues(rowNum, savedValues) {
    individualPropertyFields.forEach(field => {
        const input = document.querySelector(`[name="batch_${field}_${rowNum}"]`);
        if (input && savedValues[field] !== undefined) {
            if (input.type === 'checkbox') {
                input.checked = savedValues[field];
            } else {
                input.value = savedValues[field];
            }
        }

        // 特殊處理某些欄位的額外資料
        if (field === 'paymentDescription' && savedValues[`${field}_text`]) {
            const btn = document.querySelector(`[data-row="${rowNum}"].payment-desc-btn`);
            if (btn) {
                btn.textContent = savedValues[`${field}_text`];
            }
        } else if (field === 'entityId') {
            const typeInput = document.querySelector(`input[name="batch_entityType_${rowNum}"]`);
            if (typeInput && savedValues['entityType']) {
                typeInput.value = savedValues['entityType'];
            }
            const searchInput = document.querySelector(`[data-row="${rowNum}"].entity-search-input`);
            if (searchInput && savedValues[`${field}_text`]) {
                searchInput.value = savedValues[`${field}_text`];
            }
        } else if (field === 'tags' && savedValues[field]) {
            const tagsDataInput = document.querySelector(`input[name="batch_tagsData_${rowNum}"]`);
            if (tagsDataInput) {
                tagsDataInput.value = savedValues[field];
                // 重新顯示標籤
                if (savedValues[field]) {
                    try {
                        const tags = JSON.parse(savedValues[field]);
                        const container = document.getElementById(`tagsContainer_${rowNum}`);
                        if (container && Array.isArray(tags)) {
                            container.innerHTML = '';
                            tags.forEach(tag => {
                                const tagElement = document.createElement('span');
                                tagElement.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
                                tagElement.innerHTML = `
                                    ${tag}
                                    <button type="button" class="ml-1 text-blue-600 hover:text-blue-800" onclick="removeTag('${tag}', ${rowNum})">
                                        <i class="fas fa-times text-xs"></i>
                                    </button>
                                `;
                                container.appendChild(tagElement);
                            });
                        }
                    } catch (e) {
                        console.error('解析標籤資料失敗:', e);
                    }
                }
            }
        }
    });
}




/**
 * 處理交易描述按鈕點擊
 */
function handleDescBtnClick(event) {
    const btn = event.currentTarget;
    const rowNum = btn.dataset.row;
    const transactionType = document.querySelector(`select[name="batch_transactionType_${rowNum}"]`)?.value || 
                           document.querySelector('input[name="batch_transactionType"]:checked')?.value || 
                           'expense';
    
    showPaymentDescriptionMegaMenu(transactionType, (code, name) => {
        btn.textContent = name;
        document.querySelector(`input[name="batch_paymentDescription_${rowNum}"]`).value = code;
    });
}

/**
 * 處理交易對象按鈕點擊
 */
function handleEntityBtnClick(event) {
    const btn = event.currentTarget;
    const rowNum = btn.dataset.row;
    
    showEntitySearchModal(entity => {
        if (entity) {
            btn.textContent = entity.name;
            document.querySelector(`input[name="batch_entityId_${rowNum}"]`).value = entity.id;
            document.querySelector(`input[name="batch_entityType_${rowNum}"]`).value = entity.type;
        }
    });
}

/**
 * 顯示交易對象搜尋模態框
 */
function showEntitySearchModal(transactionType, callback) {
    // 創建模態框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.id = 'entitySearchModal';
    
    modal.innerHTML = `
        <div class="bg-white p-4 rounded-lg w-96 max-h-[80vh] overflow-auto">
            <h3 class="text-lg font-bold mb-2">選擇交易對象</h3>
            <div class="mb-4">
                <input type="text" id="entitySearchInput" class="w-full p-2 border rounded" placeholder="搜尋交易對象...">
            </div>
            <div id="entitySearchResults" class="max-h-60 overflow-y-auto mb-4">
                <!-- 搜尋結果將在這裡顯示 -->
            </div>
            <div class="flex justify-between">
                <button id="createTempEntityBtn" class="px-3 py-1 bg-green-500 text-white rounded">新增臨時對象</button>
                <button id="closeEntitySearchBtn" class="px-3 py-1 bg-gray-300 rounded">取消</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 搜尋輸入框事件
    const searchInput = document.getElementById('entitySearchInput');
    searchInput.addEventListener('input', async () => {
        const query = searchInput.value.trim();
        const results = await entitySearchManager.searchAllEntities(query, transactionType);
        displayEntitySearchResults(results.slice(0, 30), callback);
    });
    
    // 初始顯示所有結果
    entitySearchManager.searchAllEntities('', transactionType).then(results => {
        displayEntitySearchResults(results.slice(0, 30), callback);
    });
    
    // 關閉按鈕事件
    document.getElementById('closeEntitySearchBtn').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    // 新增臨時對象按鈕事件
    document.getElementById('createTempEntityBtn').addEventListener('click', async () => {
        const name = prompt('請輸入臨時交易對象名稱');
        if (name && name.trim()) {
            const entity = await entitySearchManager.createTemporaryEntity(name.trim());
            if (entity) {
                callback(entity);
                document.body.removeChild(modal);
            }
        }
    });
}

/**
 * 顯示實體搜尋結果
 */
function displayEntitySearchResults(results, callback) {
    const resultsContainer = document.getElementById('entitySearchResults');
    
    if (!results || results.length === 0) {
        resultsContainer.innerHTML = '<p class="text-gray-500 p-2">沒有找到符合的交易對象</p>';
        return;
    }
    
    let html = '<ul class="divide-y">';
    results.forEach(entity => {
        html += `
            <li class="p-2 hover:bg-gray-100 cursor-pointer entity-result" 
                data-id="${entity.id}" data-type="${entity.type}" data-name="${entity.name}">
                <div class="font-medium">${entity.name}</div>
                <div class="text-xs text-gray-500">${getEntityTypeName(entity.type)}</div>
            </li>
        `;
    });
    html += '</ul>';
    
    resultsContainer.innerHTML = html;
    
    // 添加點擊事件
    document.querySelectorAll('.entity-result').forEach(item => {
        item.addEventListener('click', () => {
            const entity = {
                id: item.dataset.id,
                type: item.dataset.type,
                name: item.dataset.name
            };
            
            callback(entity);
            document.body.removeChild(document.getElementById('entitySearchModal'));
        });
    });
}

/**
 * 獲取實體類型的顯示名稱
 */
function getEntityTypeName(type) {
    const typeNames = {
        'account': '帳戶',
        'employee': '員工',
        'client': '客戶',
        'supplier': '供應商',
        'temporary': '臨時對象'
    };
    
    return typeNames[type] || type;
}

/**
 * 生成欄位輸入控件
 * @param {string} field 欄位名稱
 * @param {number} rowNum 行號
 * @returns {string} HTML 字符串
 */
function generateFieldInput(field, rowNum) {
    return getFormControlForField(field, rowNum);
}

/**
 * 添加批次行 - 兩列式佈局
 */
function addBatchRow() {
    rowCounter++;

    // 先確保表格標題已正確設置
    if (!window.tableFieldsLayout) {
        updateBatchTableColumns();
    }

    // 創建行容器
    createBatchRowElements(rowCounter);

    // 為新行設置稅額計算
    setTimeout(() => {
        setupRowTaxCalculation(rowCounter);
    }, 100);
}

/**
 * 創建批次行元素
 * @param {number} rowNum 行號
 */
function createBatchRowElements(rowNum) {
    // 創建兩行：第一行和第二行
    const firstRow = document.createElement('tr');
    const secondRow = document.createElement('tr');

    firstRow.className = 'border-b-0 batch-row-first';
    secondRow.className = 'border-t-0 batch-row-second';
    firstRow.dataset.rowNum = rowNum;
    secondRow.dataset.rowNum = rowNum;
    firstRow.dataset.rowType = 'first';
    secondRow.dataset.rowType = 'second';

    // 序號單元格 (跨兩行)
    const indexCell = document.createElement('td');
    indexCell.className = 'p-2 border text-center align-middle';
    indexCell.textContent = rowNum;
    indexCell.rowSpan = 2;
    firstRow.appendChild(indexCell);

    // 獲取欄位佈局
    const layout = window.tableFieldsLayout;
    if (!layout) {
        console.error('表格佈局未初始化');
        return;
    }

    // 添加第一行的欄位
    layout.firstRowFields.forEach(field => {
        const cell = document.createElement('td');
        cell.className = 'p-2 border';
        cell.innerHTML = generateFieldInput(field, rowNum);
        firstRow.appendChild(cell);
    });

    // 操作單元格 (跨兩行)
    const actionCell = document.createElement('td');
    actionCell.className = 'p-2 border text-center align-middle';
    actionCell.rowSpan = 2;
    actionCell.innerHTML = `
        <button onclick="deleteBatchRow(${rowNum})" class="px-2 py-1 bg-red-500 text-white rounded text-sm">
            <i class="fas fa-trash"></i>
        </button>
    `;
    firstRow.appendChild(actionCell);

    // 添加第二行的欄位
    layout.secondRowFields.forEach(field => {
        const cell = document.createElement('td');
        cell.className = 'p-2 border';
        cell.innerHTML = generateFieldInput(field, rowNum);
        secondRow.appendChild(cell);
    });

    // 如果第二行欄位較少，添加空白單元格
    const emptyColumns = layout.firstRowFields.length - layout.secondRowFields.length;
    for (let i = 0; i < emptyColumns; i++) {
        const emptyCell = document.createElement('td');
        emptyCell.className = 'p-2 border bg-gray-50';
        emptyCell.innerHTML = '-';
        secondRow.appendChild(emptyCell);
    }

    // 添加到表格
    const tbody = document.getElementById('batchTableBody');
    tbody.appendChild(firstRow);
    tbody.appendChild(secondRow);

    // 設置事件監聽器和功能
    setTimeout(() => {
        setupRowEventListeners(rowNum);
    }, 0);
}

/**
 * 為指定行設置稅額計算功能
 * @param {number} rowNum 行號
 */
function setupRowTaxCalculation(rowNum) {
    const amountInput = document.querySelector(`input[name="batch_amount_${rowNum}"]`);
    if (amountInput) {
        // 移除舊的事件監聽器（如果存在）
        amountInput.removeEventListener('input', amountInput._taxCalculationHandler);

        // 創建新的事件處理器
        amountInput._taxCalculationHandler = function() {
            calculateRowTaxAmount(rowNum);
        };

        // 添加事件監聽器
        amountInput.addEventListener('input', amountInput._taxCalculationHandler);

        // 初始計算稅額
        if (amountInput.value) {
            calculateRowTaxAmount(rowNum);
        }
    }
}

/**
 * 為指定行設置交易對象搜尋功能
 * @param {number} rowNum 行號
 */
function setupEntitySearch(rowNum) {
    const searchInput = document.querySelector(`input.entity-search-input[data-row="${rowNum}"]`);
    const dropdown = searchInput?.parentElement.querySelector('.entity-search-dropdown');

    if (!searchInput || !dropdown) return;

    let searchTimeout;

    // 搜尋輸入事件
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        searchTimeout = setTimeout(() => {
            if (query.length >= 1) {
                performEntitySearch(query, rowNum);
            } else {
                showRecentEntities(rowNum);
            }
        }, 300); // 300ms 延遲搜尋
    });

    // 聚焦時顯示最近使用的交易對象
    searchInput.addEventListener('focus', function() {
        if (!this.value.trim()) {
            showRecentEntities(rowNum);
        } else {
            dropdown.classList.remove('hidden');
        }
    });

    // 失焦時隱藏下拉選單（延遲以允許點擊選項）
    searchInput.addEventListener('blur', function() {
        setTimeout(() => {
            dropdown.classList.add('hidden');
        }, 200);
    });

    // 鍵盤導航支援
    searchInput.addEventListener('keydown', function(e) {
        const items = dropdown.querySelectorAll('.entity-search-item');
        let currentIndex = Array.from(items).findIndex(item => item.classList.contains('highlighted'));

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentIndex < items.length - 1) {
                    if (currentIndex >= 0) items[currentIndex].classList.remove('highlighted');
                    items[currentIndex + 1].classList.add('highlighted');
                }
                break;
            case 'ArrowUp':
                e.preventDefault();
                if (currentIndex > 0) {
                    items[currentIndex].classList.remove('highlighted');
                    items[currentIndex - 1].classList.add('highlighted');
                }
                break;
            case 'Enter':
                e.preventDefault();
                if (currentIndex >= 0) {
                    items[currentIndex].click();
                }
                break;
            case 'Escape':
                dropdown.classList.add('hidden');
                break;
        }
    });
}

/**
 * 執行交易對象搜尋
 * @param {string} query 搜尋關鍵字
 * @param {number} rowNum 行號
 */
async function performEntitySearch(query, rowNum) {
    const dropdown = document.querySelector(`input.entity-search-input[data-row="${rowNum}"]`)
        ?.parentElement.querySelector('.entity-search-dropdown');

    if (!dropdown) return;

    try {
        // 搜尋員工
        const employees = await getEmployeesAll();
        const matchedEmployees = employees.filter(emp =>
            emp.name.toLowerCase().includes(query.toLowerCase()) ||
            (emp.code && emp.code.toLowerCase().includes(query.toLowerCase()))
        ).slice(0, 5);

        // 搜尋交易對象
        const entities = await getEntitiesAll();
        const matchedEntities = entities.filter(entity =>
            entity.name.toLowerCase().includes(query.toLowerCase()) ||
            (entity.code && entity.code.toLowerCase().includes(query.toLowerCase()))
        ).slice(0, 5);

        // 顯示搜尋結果
        displayEntitySearchResults(dropdown, matchedEmployees, matchedEntities, rowNum);
        dropdown.classList.remove('hidden');

    } catch (error) {
        console.error('搜尋交易對象失敗:', error);
        dropdown.innerHTML = '<div class="p-2 text-red-500 text-sm">搜尋失敗，請重試</div>';
        dropdown.classList.remove('hidden');
    }
}

/**
 * 顯示最近使用的交易對象
 * @param {number} rowNum 行號
 */
async function showRecentEntities(rowNum) {
    const dropdown = document.querySelector(`input.entity-search-input[data-row="${rowNum}"]`)
        ?.parentElement.querySelector('.entity-search-dropdown');

    if (!dropdown) return;

    try {
        // 獲取最近使用的交易對象（簡化版本，顯示前幾個）
        const employees = await getEmployeesAll();
        const entities = await getEntitiesAll();

        const recentEmployees = employees.slice(0, 3);
        const recentEntities = entities.slice(0, 3);

        displayEntitySearchResults(dropdown, recentEmployees, recentEntities, rowNum, true);
        dropdown.classList.remove('hidden');

    } catch (error) {
        console.error('載入最近交易對象失敗:', error);
    }
}

/**
 * 顯示交易對象搜尋結果
 * @param {HTMLElement} dropdown 下拉選單元素
 * @param {Array} employees 員工陣列
 * @param {Array} entities 交易對象陣列
 * @param {number} rowNum 行號
 * @param {boolean} isRecent 是否為最近使用
 */
function displayEntitySearchResults(dropdown, employees, entities, rowNum, isRecent = false) {
    let html = '';

    if (isRecent) {
        html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">最近使用</div>';
    }

    // 添加員工結果
    if (employees.length > 0) {
        if (!isRecent) {
            html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">員工</div>';
        }

        employees.forEach(employee => {
            html += `
                <div class="entity-search-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                     data-id="${employee.id}" data-type="employee" data-name="${employee.name}">
                    <div class="font-medium text-sm">${employee.name}</div>
                    <div class="text-xs text-gray-500">${employee.code || '員工'}</div>
                </div>
            `;
        });
    }

    // 添加交易對象結果
    if (entities.length > 0) {
        if (!isRecent && employees.length > 0) {
            html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">交易對象</div>';
        }

        entities.forEach(entity => {
            html += `
                <div class="entity-search-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100"
                     data-id="${entity.id}" data-type="entity" data-name="${entity.name}">
                    <div class="font-medium text-sm">${entity.name}</div>
                    <div class="text-xs text-gray-500">${entity.code || getEntityTypeName(entity.type || 'company')}</div>
                </div>
            `;
        });
    }

    // 如果沒有結果
    if (employees.length === 0 && entities.length === 0) {
        html = '<div class="p-2 text-gray-500 text-sm">找不到相符的交易對象</div>';
    }

    // 添加新增臨時對象選項
    if (!isRecent) {
        html += `
            <div class="border-t border-gray-200 mt-1">
                <div class="entity-search-item p-2 hover:bg-green-50 cursor-pointer text-green-600"
                     data-action="create-temp">
                    <div class="font-medium text-sm">
                        <i class="fas fa-plus mr-1"></i>新增臨時交易對象
                    </div>
                </div>
            </div>
        `;
    }

    dropdown.innerHTML = html;

    // 添加點擊事件
    dropdown.querySelectorAll('.entity-search-item').forEach(item => {
        item.addEventListener('click', function() {
            if (this.dataset.action === 'create-temp') {
                createTemporaryEntity(rowNum);
            } else {
                selectEntity(rowNum, this.dataset.id, this.dataset.type, this.dataset.name);
            }
        });

        // 添加滑鼠懸停高亮
        item.addEventListener('mouseenter', function() {
            dropdown.querySelectorAll('.entity-search-item').forEach(i => i.classList.remove('highlighted'));
            this.classList.add('highlighted');
        });
    });
}

/**
 * 選擇交易對象
 * @param {number} rowNum 行號
 * @param {string} entityId 交易對象 ID
 * @param {string} entityType 交易對象類型
 * @param {string} entityName 交易對象名稱
 */
function selectEntity(rowNum, entityId, entityType, entityName) {
    const searchInput = document.querySelector(`input.entity-search-input[data-row="${rowNum}"]`);
    const idInput = document.querySelector(`input[name="batch_entityId_${rowNum}"]`);
    const typeInput = document.querySelector(`input[name="batch_entityType_${rowNum}"]`);
    const dropdown = searchInput?.parentElement.querySelector('.entity-search-dropdown');

    if (searchInput && idInput && typeInput) {
        searchInput.value = entityName;
        idInput.value = entityId;
        typeInput.value = entityType;

        // 移除錯誤樣式
        searchInput.classList.remove('border-red-500', 'bg-red-50');

        // 隱藏下拉選單
        if (dropdown) {
            dropdown.classList.add('hidden');
        }
    }
}

/**
 * 創建臨時交易對象
 * @param {number} rowNum 行號
 */
async function createTemporaryEntity(rowNum) {
    const name = prompt('請輸入臨時交易對象名稱：');
    if (!name || !name.trim()) return;

    try {
        // 使用現有的 EntitySearchManager 創建臨時對象
        const entity = await entitySearchManager.createTemporaryEntity(name.trim());
        if (entity) {
            selectEntity(rowNum, entity.id, entity.type, entity.name);
        }
    } catch (error) {
        console.error('創建臨時交易對象失敗:', error);
        alert('創建臨時交易對象失敗，請重試');
    }
}

/**
 * 複製上一行
 */
function copyLastRow() {
    const rows = document.querySelectorAll('#batchTableBody tr');
    if (rows.length === 0) {
        addBatchRow();
        return;
    }
    
    const lastRow = rows[rows.length - 1];
    const lastRowNum = rows.length;
    
    // 添加新行
    addBatchRow();
    
    // 複製值
    individualPropertyFields.forEach(field => {
        if (field === 'amount') {
            const sourceValue = document.querySelector(`input[name="batch_amount_${lastRowNum}"]`)?.value;
            if (sourceValue) {
                document.querySelector(`input[name="batch_amount_${rowCounter}"]`).value = sourceValue;
            }
        } else if (field === 'notes') {
            const sourceValue = document.querySelector(`input[name="batch_notes_${lastRowNum}"]`)?.value;
            if (sourceValue) {
                document.querySelector(`input[name="batch_notes_${rowCounter}"]`).value = sourceValue;
            }
        } else if (field === 'paymentDescription') {
            const sourceValue = document.querySelector(`input[name="batch_paymentDescription_${lastRowNum}"]`)?.value;
            const sourceText = lastRow.querySelector('.batch-desc-btn')?.textContent;
            
            if (sourceValue) {
                document.querySelector(`input[name="batch_paymentDescription_${rowCounter}"]`).value = sourceValue;
                document.querySelector(`button.batch-desc-btn[data-row="${rowCounter}"]`).textContent = sourceText;
            }
        } else if (field === 'entityId') {
            const sourceId = document.querySelector(`input[name="batch_entityId_${lastRowNum}"]`)?.value;
            const sourceType = document.querySelector(`input[name="batch_entityType_${lastRowNum}"]`)?.value;
            const sourceText = lastRow.querySelector('.batch-entity-btn')?.textContent;
            
            if (sourceId) {
                document.querySelector(`input[name="batch_entityId_${rowCounter}"]`).value = sourceId;
                document.querySelector(`input[name="batch_entityType_${rowCounter}"]`).value = sourceType;
                document.querySelector(`button.batch-entity-btn[data-row="${rowCounter}"]`).textContent = sourceText;
            }
        } else {
            // 其他欄位
            const sourceElement = document.querySelector(`[name="batch_${field}_${lastRowNum}"]`);
            const targetElement = document.querySelector(`[name="batch_${field}_${rowCounter}"]`);
            
            if (sourceElement && targetElement) {
                targetElement.value = sourceElement.value;
            }
        }
    });
}

/**
 * 移除批次行
 */
function removeBatchRow(button) {
    const row = button.closest('tr');
    if (document.querySelectorAll('#batchTableBody tr').length > 1) {
        row.remove();
        
        // 重新編號
        document.querySelectorAll('#batchTableBody tr').forEach((row, index) => {
            row.firstChild.textContent = index + 1;
        });
    }
}

/**
 * 設置快速模式
 */
function setQuickMode(mode) {
    // 重置所有共同屬性選擇
    document.querySelectorAll('.common-property-toggle').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // 根據模式設置共同屬性
    switch (mode) {
        case 'minimal':
            // 只選擇最基本的共同屬性
            document.querySelector('.common-property-toggle[data-field="transactionType"]').checked = true;
            document.querySelector('.common-property-toggle[data-field="accountId"]').checked = true;
            break;
        case 'standard':
            // 選擇標準共同屬性
            document.querySelector('.common-property-toggle[data-field="transactionType"]').checked = true;
            document.querySelector('.common-property-toggle[data-field="accountId"]').checked = true;
            document.querySelector('.common-property-toggle[data-field="paymentDate"]').checked = true;
            document.querySelector('.common-property-toggle[data-field="paymentStatus"]').checked = true;
            document.querySelector('.common-property-toggle[data-field="taxTypeId"]').checked = true;
            break;
        case 'maximal':
            // 選擇所有可能的共同屬性
            document.querySelectorAll('.common-property-toggle').forEach(checkbox => {
                checkbox.checked = true;
            });
            break;
    }
    
    // 更新共同屬性表單和表格
    updateCommonPropertiesForm();
    updateBatchTableColumns();
}

/**
 * 獲取欄位的表單控件HTML
 */
function getFormControlForField(field, rowNum = null) {
    const namePrefix = rowNum ? `batch_${field}_${rowNum}` : `batch_${field}`;
    
    switch (field) {
        case 'transactionType':
            return `
                <div class="flex gap-4">
                    <label><input type="radio" name="${namePrefix}" value="expense" checked> 支出</label>
                    <label><input type="radio" name="${namePrefix}" value="income"> 收入</label>
                    <label><input type="radio" name="${namePrefix}" value="transfer"> 轉帳</label>
                </div>
            `;
        case 'accountId':
            return `<select class="w-full p-1 border rounded" name="${namePrefix}" id="${namePrefix}">
                <option value="">請選擇帳戶</option>
                <!-- 帳戶選項將在JS中動態添加 -->
            </select>`;
        case 'paymentDate':
        case 'invoiceDate':
        case 'expectedPaymentDate':
            const today = new Date().toISOString().split('T')[0];
            return `<input type="date" class="w-full p-1 border rounded" name="${namePrefix}" value="${today}">`;
        case 'paymentStatus':
            const selectHtml = `<select class="w-full p-1 border rounded" name="${namePrefix}" id="${namePrefix}">
                <option value="">請選擇到帳情形</option>
                <option value="same_day">同日收付款（現金基礎）</option>
                <option value="receivable">應收付款（權責基礎）（已開立發票未收款）</option>
                <option value="prepayment">暫收付款（權責基礎）（未開立發票已收款）</option>
                <option value="different_day">非同日收款（現金基礎）(補未紀錄之已收款項)</option>
            </select>`;

            // 添加事件監聽器
            setTimeout(() => {
                const select = document.querySelector(`[name="${namePrefix}"]`);
                if (select) {
                    select.addEventListener('change', handlePaymentStatusChange);
                }
            }, 0);

            return selectHtml;
        case 'taxTypeId':
            let taxOptions = '<option value="">請選擇稅別</option>';
            if (taxTypesData && taxTypesData.length > 0) {
                taxTypesData.forEach(tax => {
                    taxOptions += `<option value="${tax.id}" data-rate="${tax.rate}">${tax.name} (${(tax.rate * 100).toFixed(1)}%)</option>`;
                });
            }

            const taxSelectHtml = `<select class="w-full p-1 border rounded" name="${namePrefix}">${taxOptions}</select>`;

            // 添加稅別變更事件監聽器
            setTimeout(() => {
                const select = document.querySelector(`[name="${namePrefix}"]`);
                if (select) {
                    select.addEventListener('change', function() {
                        handleTaxTypeChange(this, rowNum);
                    });
                }
            }, 0);

            return taxSelectHtml;
        case 'paymentDescription':
            if (rowNum === null) {
                // 共同屬性版本
                return `
                    <div class="relative payment-desc-container">
                        <button type="button" id="commonPaymentDescriptionBtn" class="w-full p-1 border rounded text-left">
                            選擇交易項目
                        </button>
                        <div class="payment-desc-dropdown absolute z-20 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-64 overflow-y-auto" style="min-width: 300px;">
                            <!-- 交易項目選單將在此顯示 -->
                        </div>
                        <input type="hidden" name="${namePrefix}" id="${namePrefix}">
                        <input type="hidden" name="batch_paymentDescriptionName" id="batch_paymentDescriptionName">
                    </div>
                `;
            } else {
                // 個別屬性版本（已在上面處理）
                return `
                    <div class="relative payment-desc-container">
                        <button type="button" class="w-full p-1 border rounded text-left payment-desc-btn"
                                data-row="${rowNum}">選擇交易項目</button>
                        <div class="payment-desc-dropdown absolute z-20 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-64 overflow-y-auto" style="min-width: 300px;">
                            <!-- 交易項目選單將在此顯示 -->
                        </div>
                        <input type="hidden" name="${namePrefix}">
                        <input type="hidden" name="batch_paymentDescriptionName_${rowNum}">
                    </div>
                `;
            }
        case 'entityId':
            if (rowNum === null) {
                // 共同屬性版本
                return `
                    <div class="relative entity-search-container">
                        <input type="text" id="commonEntitySearch" class="w-full p-1 border rounded"
                               placeholder="搜尋交易對象...">
                        <div class="entity-search-dropdown absolute z-10 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-48 overflow-y-auto">
                            <!-- 搜尋結果將在此顯示 -->
                        </div>
                        <input type="hidden" name="${namePrefix}" id="${namePrefix}">
                        <input type="hidden" name="batch_entityType" id="batch_entityType" value="company">
                    </div>
                `;
            } else {
                // 個別屬性版本（已在上面處理）
                return `
                    <div class="relative entity-search-container">
                        <input type="text" class="w-full p-1 border rounded entity-search-input"
                               placeholder="搜尋交易對象..." data-row="${rowNum}">
                        <div class="entity-search-dropdown absolute z-10 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-48 overflow-y-auto">
                            <!-- 搜尋結果將在此顯示 -->
                        </div>
                        <input type="hidden" name="${namePrefix}">
                        <input type="hidden" name="batch_entityType_${rowNum}" value="company">
                    </div>
                `;
            }
        case 'taxAmount':
            return `
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                        <span class="text-gray-500 text-sm">$</span>
                    </div>
                    <input type="number" class="w-full pl-6 p-1 border rounded bg-gray-50" name="${namePrefix}"
                           step="0.01" min="0" placeholder="自動計算" readonly>
                </div>
            `;
        case 'invoiceNumber':
            return `<input type="text" class="w-full p-1 border rounded" name="${namePrefix}" placeholder="發票號碼">`;
        case 'fee':
            return `
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                        <span class="text-gray-500 text-sm">$</span>
                    </div>
                    <input type="number" class="w-full pl-6 p-1 border rounded" name="${namePrefix}"
                           step="0.01" min="0" placeholder="手續費">
                </div>
            `;
        case 'tags':
            return `
                <div class="relative">
                    <input type="text" class="w-full p-1 border rounded tags-input" name="batch_tags_${rowNum || 'common'}"
                           placeholder="輸入標籤後按Enter" data-row="${rowNum || 'common'}">
                    <div class="tags-container mt-1 flex flex-wrap gap-1" id="tagsContainer_${rowNum || 'common'}">
                        <!-- 標籤將在此顯示 -->
                    </div>
                    <input type="hidden" name="batch_tagsData_${rowNum || 'common'}" value="">
                </div>
            `;
        case 'notes':
            return `<textarea class="w-full p-1 border rounded" name="${namePrefix}" rows="2" placeholder="備註"></textarea>`;
        default:
            return `<input type="text" class="w-full p-1 border rounded" name="${namePrefix}">`;
    }
}

/**
 * 獲取欄位的顯示名稱
 */
function getFieldLabel(fieldName) {
    const labels = {
        'transactionType': '交易類型',
        'accountId': '我方主要帳戶',
        'paymentDate': '收款/付款日期',
        'paymentStatus': '帳款到帳情形',
        'invoiceDate': '憑證/發票日期',
        'expectedPaymentDate': '預計收/付款日期',
        'taxTypeId': '稅別',
        'paymentDescription': '交易項目',
        'entityId': '交易對象/帳戶',
        'amount': '金額 (含稅)',
        'taxAmount': '稅額',
        'invoiceNumber': '發票號碼',
        'fee': '手續費',
        'tags': '標籤',
        'notes': '備註'
    };

    return labels[fieldName] || fieldName;
}

/**
 * 顯示交易描述選擇器
 */
function showPaymentDescriptionMegaMenu(transactionType, callback) {
    // 顯示選擇器容器
    const megaMenuContainer = document.getElementById('paymentDescriptionMegaMenuContainer');
    megaMenuContainer.classList.remove('hidden');
    
    // 渲染選單內容
    renderPaymentDescriptionMegaMenu(transactionType);
    
    // 監聽選擇完成事件
    const handleSelection = (event) => {
        const { code, name } = event.detail;
        if (code) {
            callback(code, name);
        }
        document.removeEventListener('paymentDescriptionSelected', handleSelection);
    };
    
    document.addEventListener('paymentDescriptionSelected', handleSelection);
}

/**
 * 預覽批次交易
 */
async function previewBatchTransactions() {
    try {
        // 獲取所有交易數據
        const transactions = collectBatchTransactions();
        
        if (transactions.length === 0) {
            alert('請至少添加一筆交易');
            return;
        }
        
        // 顯示預覽模態框
        const previewModal = document.getElementById('previewModal');
        const previewContent = document.getElementById('previewContent');
        
        // 生成預覽內容
        let html = `
            <div class="mb-4">
                <h3 class="text-lg font-bold">批次交易預覽 (共 ${transactions.length} 筆)</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full border">
                    <thead>
                        <tr>
                            <th class="p-2 border">序號</th>
                            <th class="p-2 border">交易類型</th>
                            <th class="p-2 border">帳戶</th>
                            <th class="p-2 border">交易對象</th>
                            <th class="p-2 border">金額</th>
                            <th class="p-2 border">交易描述</th>
                            <th class="p-2 border">收款/付款日期</th>
                            <th class="p-2 border">備註</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        // 添加每筆交易的預覽
        for (let i = 0; i < transactions.length; i++) {
            const tx = transactions[i];
            
            // 獲取帳戶名稱
            const account = accountsData.find(a => a.id === tx.accountId);
            const accountName = account ? account.name : '未指定';
            
            // 獲取交易對象名稱
            let entityName = '未指定';
            if (tx.entityId) {
                const entity = await entitySearchManager.getEntity(tx.entityId, tx.entityType);
                if (entity) {
                    entityName = entity.name;
                }
            }
            
            // 獲取交易描述名稱
            let descriptionName = '未指定';
            if (tx.paymentDescription) {
                descriptionName = await getPaymentDescriptionName(tx.paymentDescription) || tx.paymentDescription;
            }
            
            // 交易類型顯示
            const typeLabels = {
                'expense': '支出',
                'income': '收入',
                'transfer': '轉帳'
            };
            
            html += `
                <tr>
                    <td class="p-2 border">${i + 1}</td>
                    <td class="p-2 border">${typeLabels[tx.transactionType] || tx.transactionType}</td>
                    <td class="p-2 border">${accountName}</td>
                    <td class="p-2 border">${entityName}</td>
                    <td class="p-2 border text-right">${parseFloat(tx.amount).toLocaleString()}</td>
                    <td class="p-2 border">${descriptionName}</td>
                    <td class="p-2 border">${tx.paymentDate}</td>
                    <td class="p-2 border">${tx.notes || ''}</td>
                </tr>
            `;
        }
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        previewContent.innerHTML = html;
        previewModal.classList.remove('hidden');
        
    } catch (error) {
        console.error('預覽批次交易時發生錯誤:', error);
        alert('預覽批次交易時發生錯誤');
    }
}

/**
 * 收集批次交易數據
 */
function collectBatchTransactions() {
    const transactions = [];
    const rows = document.querySelectorAll('#batchTableBody tr');
    
    // 獲取共同屬性值
    const commonProps = {};
    commonPropertyFields.forEach(field => {
        if (field === 'transactionType') {
            commonProps[field] = document.querySelector(`input[name="batch_${field}"]:checked`)?.value || 'expense';
        } else {
            commonProps[field] = document.querySelector(`[name="batch_${field}"]`)?.value || '';
        }
    });
    
    // 如果交易對象是共同屬性，獲取交易對象類型
    if (commonPropertyFields.includes('entityId')) {
        commonProps.entityType = document.getElementById('batch_entityType')?.value || 'company';
    }
    
    // 處理每一行
    rows.forEach((row, index) => {
        const rowNum = index + 1;
        const transaction = { ...commonProps };
        
        // 獲取個別屬性值
        individualPropertyFields.forEach(field => {
            if (field === 'transactionType') {
                transaction[field] = document.querySelector(`select[name="batch_${field}_${rowNum}"]`)?.value || commonProps[field] || 'expense';
            } else if (field === 'entityId') {
                transaction[field] = document.querySelector(`input[name="batch_${field}_${rowNum}"]`)?.value || commonProps[field] || '';
                transaction.entityType = document.querySelector(`input[name="batch_entityType_${rowNum}"]`)?.value || commonProps.entityType || 'company';
            } else {
                transaction[field] = document.querySelector(`[name="batch_${field}_${rowNum}"]`)?.value || commonProps[field] || '';
            }
        });
        
        // 只有當金額大於0時才添加交易
        if (transaction.amount && parseFloat(transaction.amount) > 0) {
            transactions.push(transaction);
        }
    });
    
    return transactions;
}

/**
 * 保存批次交易
 */
async function saveBatchTransactions() {
    try {
        // 獲取所有交易數據
        const transactions = collectBatchTransactions();
        
        if (transactions.length === 0) {
            alert('請至少添加一筆有效的交易（金額大於0）');
            return;
        }
        
        // 顯示確認對話框
        if (!confirm(`確定要保存這 ${transactions.length} 筆交易嗎？`)) {
            return;
        }
        
        // 顯示進度提示
        const progressModal = document.createElement('div');
        progressModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        progressModal.innerHTML = `
            <div class="bg-white p-4 rounded-lg w-96">
                <h3 class="text-lg font-bold mb-2">正在保存交易</h3>
                <div class="mb-2">
                    <div id="progressText">處理中 (0/${transactions.length})...</div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div id="progressBar" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
                </div>
            </div>
        `;
        document.body.appendChild(progressModal);
        
        // 逐個保存交易
        const results = [];
        for (let i = 0; i < transactions.length; i++) {
            try {
                // 更新進度
                document.getElementById('progressText').textContent = `處理中 (${i+1}/${transactions.length})...`;
                document.getElementById('progressBar').style.width = `${((i+1) / transactions.length) * 100}%`;
                
                // 保存交易
                const savedId = await saveTransaction(transactions[i]);
                
                if (savedId) {
                    results.push({ success: true, index: i, id: savedId });
                } else {
                    results.push({ success: false, index: i, error: '保存失敗' });
                }
            } catch (error) {
                console.error(`保存第 ${i+1} 筆交易時發生錯誤:`, error);
                results.push({ success: false, index: i, error: error.message });
            }
            
            // 短暫延遲，避免界面凍結
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // 移除進度模態框
        document.body.removeChild(progressModal);
        
        // 顯示結果
        const successCount = results.filter(r => r.success).length;
        
        if (successCount === transactions.length) {
            alert(`成功保存所有 ${successCount} 筆交易！`);
            
            // 清空表格，重新開始
            document.getElementById('batchTableBody').innerHTML = '';
            rowCounter = 0;
            addBatchRow();
        } else {
            alert(`已保存 ${successCount}/${transactions.length} 筆交易。\n${transactions.length - successCount} 筆交易保存失敗。`);
            
            // 移除成功的行
            results.forEach(result => {
                if (result.success) {
                    const rows = document.querySelectorAll('#batchTableBody tr');
                    if (rows[result.index]) {
                        rows[result.index].remove();
                    }
                }
            });
            
            // 重新編號
            document.querySelectorAll('#batchTableBody tr').forEach((row, index) => {
                row.firstChild.textContent = index + 1;
            });
            
            // 如果沒有行了，添加一行
            if (document.querySelectorAll('#batchTableBody tr').length === 0) {
                rowCounter = 0;
                addBatchRow();
            }
        }
        
    } catch (error) {
        console.error('保存批次交易時發生錯誤:', error);
        alert('保存批次交易時發生錯誤: ' + error.message);
    }
}

/**
 * 保存單筆交易
 */
async function saveTransaction(transactionData) {
    // 構建交易數據
    const formData = {
        transactionType: transactionData.transactionType || 'expense',
        accountId: transactionData.accountId || '',
        paymentDate: transactionData.paymentDate || new Date().toISOString().split('T')[0],
        paymentStatus: transactionData.paymentStatus || 'same_day',
        invoiceDate: transactionData.invoiceDate || transactionData.paymentDate || new Date().toISOString().split('T')[0],
        taxTypeId: transactionData.taxTypeId || '',
        amount: parseFloat(transactionData.amount) || 0,
        paymentDescription: transactionData.paymentDescription || '',
        entityId: transactionData.entityId || '',
        entityType: transactionData.entityType || 'company',
        notes: transactionData.notes || '',
        // 其他必要欄位
        taxAmount: 0, // 預設為0，可以根據稅別計算
        fee: 0, // 預設為0
        invoiceNumber: '', // 預設為空
        tags: [] // 預設為空陣列
    };
    
    // 如果有稅別，計算稅額
    if (formData.taxTypeId) {
        const taxType = taxTypesData.find(t => t.id === formData.taxTypeId);
        if (taxType && taxType.rate) {
            formData.taxAmount = calculateTaxAmount(formData.amount, taxType.rate);
        }
    }
    
    // 保存交易
    const savedId = await saveTransactionToDB(formData);
    
    // 如果保存成功，處理會計分錄
    if (savedId) {
        try {
            await saveNewJournal(savedId, formData);
        } catch (journalError) {
            console.error('儲存會計分錄失敗：', journalError);
            // 會計分錄儲存失敗不影響交易儲存，但要記錄錯誤
        }
    }
    
    return savedId;
}
                    
