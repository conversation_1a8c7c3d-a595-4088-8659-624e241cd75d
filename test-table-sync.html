<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格標題與欄位同步測試</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">表格標題與欄位同步測試</h1>
        
        <!-- 測試控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">測試控制面板</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="testTableHeaderSync()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    測試表格標題同步
                </button>
                <button onclick="testFieldOrder()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    測試欄位順序
                </button>
                <button onclick="testCommonPropertyToggle()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    測試共同屬性切換
                </button>
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    開啟批次頁面
                </button>
                <button onclick="clearResults()" 
                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    清除結果
                </button>
                <button onclick="runAllTests()" 
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    執行所有測試
                </button>
            </div>
        </div>
        
        <!-- 欄位映射檢查 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">欄位映射檢查</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-gray-800 mb-3">預期的欄位順序</h4>
                    <div class="space-y-2 text-sm">
                        <div class="p-2 bg-gray-50 rounded">1. 序號</div>
                        <div class="p-2 bg-blue-50 rounded">2. 金額 (含稅)</div>
                        <div class="p-2 bg-blue-50 rounded">3. 稅額</div>
                        <div class="p-2 bg-blue-50 rounded">4. 發票號碼</div>
                        <div class="p-2 bg-blue-50 rounded">5. 手續費</div>
                        <div class="p-2 bg-blue-50 rounded">6. 標籤</div>
                        <div class="p-2 bg-green-50 rounded">7. 其他個別屬性...</div>
                        <div class="p-2 bg-blue-50 rounded">N. 備註</div>
                        <div class="p-2 bg-gray-50 rounded">N+1. 操作</div>
                    </div>
                </div>
                <div>
                    <h4 class="font-medium text-gray-800 mb-3">實際檢測結果</h4>
                    <div id="fieldOrderResult" class="space-y-2 text-sm">
                        <div class="p-2 bg-gray-100 rounded text-gray-500">點擊測試按鈕查看結果</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 共同屬性測試 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4">共同屬性切換測試</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testTransactionType">
                    <span class="text-sm">交易類型</span>
                </label>
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testAccountId">
                    <span class="text-sm">我方主要帳戶</span>
                </label>
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testPaymentDate">
                    <span class="text-sm">收款/付款日期</span>
                </label>
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testPaymentStatus">
                    <span class="text-sm">帳款到帳情形</span>
                </label>
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testInvoiceDate">
                    <span class="text-sm">憑證/發票日期</span>
                </label>
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testExpectedPaymentDate">
                    <span class="text-sm">預計收/付款日期</span>
                </label>
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testTaxTypeId">
                    <span class="text-sm">稅別</span>
                </label>
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testPaymentDescription">
                    <span class="text-sm">交易項目</span>
                </label>
                <label class="flex items-center p-3 border rounded hover:bg-gray-50">
                    <input type="checkbox" class="mr-2" id="testEntityId">
                    <span class="text-sm">交易對象/帳戶</span>
                </label>
            </div>
            <div class="mt-4">
                <button onclick="simulateCommonPropertyChange()" 
                    class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600">
                    模擬共同屬性變更
                </button>
            </div>
        </div>
        
        <!-- 測試結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">測試結果</h2>
            <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto">
                <p class="text-gray-600">點擊上方按鈕開始測試...</p>
            </div>
        </div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor}`;
            resultItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span class="text-sm">${message}</span>
                    <span class="ml-auto text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            
            resultsDiv.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-600">測試結果已清除</p>';
        }
        
        function testTableHeaderSync() {
            addTestResult('開始測試表格標題同步...', 'info');
            
            // 模擬欄位標籤映射
            const fieldLabels = {
                'amount': '金額 (含稅)',
                'taxAmount': '稅額',
                'invoiceNumber': '發票號碼',
                'fee': '手續費',
                'tags': '標籤',
                'notes': '備註',
                'transactionType': '交易類型',
                'accountId': '我方主要帳戶',
                'paymentDate': '收款/付款日期',
                'paymentStatus': '帳款到帳情形',
                'invoiceDate': '憑證/發票日期',
                'expectedPaymentDate': '預計收/付款日期',
                'taxTypeId': '稅別',
                'paymentDescription': '交易項目',
                'entityId': '交易對象/帳戶'
            };
            
            // 檢查每個欄位是否有對應的標籤
            let passCount = 0;
            const totalFields = Object.keys(fieldLabels).length;
            
            Object.entries(fieldLabels).forEach(([field, label]) => {
                if (label && label.trim() !== '') {
                    addTestResult(`✓ ${field} → ${label}`, 'success');
                    passCount++;
                } else {
                    addTestResult(`✗ ${field} 缺少標籤`, 'error');
                }
            });
            
            addTestResult(`標籤映射測試完成 (${passCount}/${totalFields})`, passCount === totalFields ? 'success' : 'warning');
        }
        
        function testFieldOrder() {
            addTestResult('開始測試欄位順序...', 'info');
            
            // 模擬 individualPropertyFields 的順序
            const expectedOrder = [
                'amount',        // 金額 (含稅)
                'taxAmount',     // 稅額
                'invoiceNumber', // 發票號碼
                'fee',           // 手續費
                'tags',          // 標籤
                'notes'          // 備註
            ];
            
            // 顯示預期順序
            const resultDiv = document.getElementById('fieldOrderResult');
            resultDiv.innerHTML = '';
            
            expectedOrder.forEach((field, index) => {
                const fieldLabels = {
                    'amount': '金額 (含稅)',
                    'taxAmount': '稅額',
                    'invoiceNumber': '發票號碼',
                    'fee': '手續費',
                    'tags': '標籤',
                    'notes': '備註'
                };
                
                const div = document.createElement('div');
                div.className = 'p-2 bg-blue-50 rounded';
                div.textContent = `${index + 2}. ${fieldLabels[field]}`;
                resultDiv.appendChild(div);
                
                addTestResult(`✓ 位置 ${index + 2}: ${fieldLabels[field]}`, 'success');
            });
            
            addTestResult('欄位順序測試完成', 'success');
        }
        
        function testCommonPropertyToggle() {
            addTestResult('開始測試共同屬性切換...', 'info');
            
            const commonFields = [
                'transactionType', 'accountId', 'paymentDate', 'paymentStatus',
                'invoiceDate', 'expectedPaymentDate', 'taxTypeId', 'paymentDescription', 'entityId'
            ];
            
            commonFields.forEach(field => {
                const checkbox = document.getElementById(`test${field.charAt(0).toUpperCase() + field.slice(1)}`);
                if (checkbox) {
                    const isChecked = checkbox.checked;
                    const status = isChecked ? '設為共同屬性' : '設為個別屬性';
                    addTestResult(`✓ ${field}: ${status}`, 'info');
                } else {
                    addTestResult(`✗ 找不到 ${field} 的測試控件`, 'warning');
                }
            });
            
            addTestResult('共同屬性切換測試完成', 'success');
        }
        
        function simulateCommonPropertyChange() {
            addTestResult('模擬共同屬性變更...', 'info');
            
            // 模擬表格重建邏輯
            const checkedFields = [];
            const uncheckedFields = [];
            
            const commonFields = [
                'transactionType', 'accountId', 'paymentDate', 'paymentStatus',
                'invoiceDate', 'expectedPaymentDate', 'taxTypeId', 'paymentDescription', 'entityId'
            ];
            
            commonFields.forEach(field => {
                const checkbox = document.getElementById(`test${field.charAt(0).toUpperCase() + field.slice(1)}`);
                if (checkbox && checkbox.checked) {
                    checkedFields.push(field);
                } else {
                    uncheckedFields.push(field);
                }
            });
            
            // 模擬 individualPropertyFields 重建
            const alwaysIndividual = ['amount', 'taxAmount', 'invoiceNumber', 'fee', 'tags', 'notes'];
            const newIndividualFields = [...alwaysIndividual, ...uncheckedFields];
            
            addTestResult(`共同屬性: ${checkedFields.join(', ') || '無'}`, 'info');
            addTestResult(`個別屬性: ${newIndividualFields.join(', ')}`, 'info');
            
            // 模擬表格標題重建
            const newHeaders = ['序號', ...newIndividualFields.map(field => {
                const fieldLabels = {
                    'amount': '金額 (含稅)',
                    'taxAmount': '稅額',
                    'invoiceNumber': '發票號碼',
                    'fee': '手續費',
                    'tags': '標籤',
                    'notes': '備註',
                    'transactionType': '交易類型',
                    'accountId': '我方主要帳戶',
                    'paymentDate': '收款/付款日期',
                    'paymentStatus': '帳款到帳情形',
                    'invoiceDate': '憑證/發票日期',
                    'expectedPaymentDate': '預計收/付款日期',
                    'taxTypeId': '稅別',
                    'paymentDescription': '交易項目',
                    'entityId': '交易對象/帳戶'
                };
                return fieldLabels[field] || field;
            }), '操作'];
            
            addTestResult(`新表格標題: ${newHeaders.join(' | ')}`, 'success');
            addTestResult('共同屬性變更模擬完成', 'success');
        }
        
        async function runAllTests() {
            addTestResult('開始執行所有測試...', 'info');
            
            testTableHeaderSync();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testFieldOrder();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testCommonPropertyToggle();
            
            addTestResult('所有測試執行完成！', 'success');
        }
        
        function openBatchPage() {
            addTestResult('開啟批次交易頁面...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('表格同步測試頁面載入完成', 'success');
            addTestResult('可以開始進行同步測試', 'info');
        });
    </script>
</body>
</html>
