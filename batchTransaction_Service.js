/**
 * @file batchTransaction_Service.js
 * @description 批次交易服務層
 * 負責處理批次交易的業務邏輯和資料處理
 */

/**
 * 切換交易項目下拉選單
 * @param {number} rowIndex - 行索引
 */
async function togglePaymentDescriptionDropdown(rowIndex) {
    const dropdown = document.getElementById(`paymentDescription_${rowIndex}_dropdown`);
    if (!dropdown) return;
    
    if (dropdown.classList.contains('hidden')) {
        await loadPaymentDescriptionOptions(rowIndex);
        dropdown.classList.remove('hidden');
    } else {
        dropdown.classList.add('hidden');
    }
}

/**
 * 載入交易項目選項
 * @param {number} rowIndex - 行索引
 */
async function loadPaymentDescriptionOptions(rowIndex) {
    try {
        const dropdown = document.getElementById(`paymentDescription_${rowIndex}_dropdown`);
        if (!dropdown) return;
        
        // 獲取當前交易類型
        const transactionType = document.querySelector('input[name="batchTransactionType"]:checked')?.value || 'expense';
        
        // 載入交易分類和項目
        const categories = await getTransactionCategoriesAll();
        const items = await getTransactionCategoryItemsAll();
        
        dropdown.innerHTML = '';
        
        // 創建搜尋框
        const searchContainer = document.createElement('div');
        searchContainer.className = 'p-2 border-b';
        
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = '搜尋交易項目...';
        searchInput.className = 'w-full p-1 text-sm border border-gray-300 rounded';
        
        searchContainer.appendChild(searchInput);
        dropdown.appendChild(searchContainer);
        
        // 創建選項容器
        const optionsContainer = document.createElement('div');
        optionsContainer.className = 'max-h-48 overflow-y-auto';
        dropdown.appendChild(optionsContainer);
        
        // 根據交易類型篩選分類
        const filteredCategories = categories.filter(cat => cat.type === transactionType);
        
        // 渲染分類和項目
        renderPaymentDescriptionOptions(optionsContainer, filteredCategories, items, rowIndex);
        
        // 添加搜尋功能
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterPaymentDescriptionOptions(optionsContainer, filteredCategories, items, searchTerm, rowIndex);
        });
        
    } catch (error) {
        console.error('載入交易項目選項失敗:', error);
    }
}

/**
 * 渲染交易項目選項
 * @param {HTMLElement} container - 容器元素
 * @param {Array} categories - 分類陣列
 * @param {Array} items - 項目陣列
 * @param {number} rowIndex - 行索引
 * @param {string} searchTerm - 搜尋關鍵字
 */
function renderPaymentDescriptionOptions(container, categories, items, rowIndex, searchTerm = '') {
    container.innerHTML = '';
    
    categories.forEach(category => {
        // 獲取該分類下的項目
        const categoryItems = items.filter(item => item.categoryId === category.id);
        
        // 如果有搜尋詞，篩選項目
        const filteredItems = searchTerm ? 
            categoryItems.filter(item => 
                item.accountingName.toLowerCase().includes(searchTerm) ||
                item.accountingCode.toLowerCase().includes(searchTerm)
            ) : categoryItems;
        
        // 如果沒有符合的項目，跳過此分類
        if (filteredItems.length === 0) return;
        
        // 分類標題
        const categoryHeader = document.createElement('div');
        categoryHeader.className = 'px-3 py-2 bg-gray-100 text-sm font-medium text-gray-700 sticky top-0';
        categoryHeader.textContent = category.name;
        container.appendChild(categoryHeader);
        
        // 添加該分類下的項目
        filteredItems.forEach(item => {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'dropdown-item';
            
            // 高亮搜尋詞
            let displayName = item.accountingName;
            let displayCode = item.accountingCode;
            
            if (searchTerm) {
                displayName = highlightSearchTerm(displayName, searchTerm);
                displayCode = highlightSearchTerm(displayCode, searchTerm);
            }
            
            itemDiv.innerHTML = `
                <div class="text-sm font-medium">${displayName}</div>
                <div class="text-xs text-gray-500">${displayCode}</div>
            `;
            
            itemDiv.onclick = () => selectPaymentDescription(rowIndex, item.accountingCode, item.accountingName);
            container.appendChild(itemDiv);
        });
    });
    
    // 如果沒有結果
    if (container.children.length === 0) {
        const noResult = document.createElement('div');
        noResult.className = 'dropdown-item text-gray-500';
        noResult.textContent = searchTerm ? '找不到相符的交易項目' : '沒有可用的交易項目';
        container.appendChild(noResult);
    }
}

/**
 * 篩選交易項目選項
 * @param {HTMLElement} container - 容器元素
 * @param {Array} categories - 分類陣列
 * @param {Array} items - 項目陣列
 * @param {string} searchTerm - 搜尋關鍵字
 * @param {number} rowIndex - 行索引
 */
function filterPaymentDescriptionOptions(container, categories, items, searchTerm, rowIndex) {
    renderPaymentDescriptionOptions(container, categories, items, rowIndex, searchTerm);
}

/**
 * 高亮搜尋詞
 * @param {string} text - 原始文字
 * @param {string} searchTerm - 搜尋關鍵字
 * @returns {string} 高亮後的文字
 */
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
}

/**
 * 選擇交易項目
 * @param {number} rowIndex - 行索引
 * @param {string} code - 項目代碼
 * @param {string} name - 項目名稱
 */
function selectPaymentDescription(rowIndex, code, name) {
    const button = document.getElementById(`paymentDescription_${rowIndex}_btn`);
    const hiddenInput = document.getElementById(`paymentDescription_${rowIndex}`);
    const hiddenNameInput = document.getElementById(`paymentDescription_${rowIndex}_name`);
    const dropdown = document.getElementById(`paymentDescription_${rowIndex}_dropdown`);
    
    if (button && hiddenInput) {
        button.querySelector('span').textContent = name;
        button.title = `${name} (${code})`;
        hiddenInput.value = code;
        
        // 移除錯誤樣式
        button.classList.remove('error-input');
    }
    
    if (hiddenNameInput) {
        hiddenNameInput.value = name;
    }
    
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

/**
 * 更新所有行的交易項目選單
 * @param {string} transactionType - 交易類型
 */
function updateAllRowsPaymentDescription(transactionType) {
    const rows = document.querySelectorAll('#batchTableBody tr');
    rows.forEach(row => {
        const rowIndex = row.dataset.rowIndex;
        if (rowIndex !== undefined) {
            // 清空當前選擇
            const button = document.getElementById(`paymentDescription_${rowIndex}_btn`);
            const hiddenInput = document.getElementById(`paymentDescription_${rowIndex}`);
            const hiddenNameInput = document.getElementById(`paymentDescription_${rowIndex}_name`);
            
            if (button && hiddenInput) {
                button.querySelector('span').textContent = '選擇交易項目';
                button.title = '';
                hiddenInput.value = '';
            }
            
            if (hiddenNameInput) {
                hiddenNameInput.value = '';
            }
        }
    });
}

/**
 * 顯示批次處理結果
 * @param {Array} results - 處理結果陣列
 */
function displayBatchResults(results) {
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    
    // 更新結果統計
    document.getElementById('successCount').textContent = successCount;
    document.getElementById('failureCount').textContent = failureCount;
    document.getElementById('totalCount').textContent = results.length;
    
    // 顯示詳細結果
    const resultDetails = document.getElementById('resultDetails');
    resultDetails.innerHTML = '';
    
    results.forEach(result => {
        const resultItem = document.createElement('div');
        resultItem.className = `p-3 rounded border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`;
        
        resultItem.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas ${result.success ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500'} mr-2"></i>
                    <span class="font-medium">第 ${result.displayIndex} 筆交易</span>
                </div>
                <span class="text-sm ${result.success ? 'text-green-600' : 'text-red-600'}">${result.message}</span>
            </div>
            ${result.transactionId ? `<div class="text-xs text-gray-500 mt-1">交易ID: ${result.transactionId}</div>` : ''}
        `;
        
        resultDetails.appendChild(resultItem);
    });
    
    // 顯示結果區域
    document.getElementById('resultSection').classList.remove('hidden');
    
    // 滾動到結果區域
    document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
    
    // 顯示總結訊息
    if (failureCount > 0) {
        alert(`批次處理完成！\n成功: ${successCount} 筆\n失敗: ${failureCount} 筆\n\n請查看詳細結果。`);
    } else {
        alert(`批次處理完成！所有 ${successCount} 筆交易都已成功儲存。`);
    }
}

// 匯出函式供其他模組使用
window.togglePaymentDescriptionDropdown = togglePaymentDescriptionDropdown;
window.loadPaymentDescriptionOptions = loadPaymentDescriptionOptions;
window.selectPaymentDescription = selectPaymentDescription;
window.updateAllRowsPaymentDescription = updateAllRowsPaymentDescription;
window.displayBatchResults = displayBatchResults;
