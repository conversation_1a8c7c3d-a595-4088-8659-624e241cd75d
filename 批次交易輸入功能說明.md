# 批次交易輸入功能說明

## 概述

批次交易輸入功能允許使用者在單一頁面一次性輸入並提交多筆交易資料，大幅提高資料輸入效率。此功能完全整合現有的交易處理邏輯，包括會計分錄生成、資料驗證等機制。

## 檔案結構

### 主要檔案
- `transactions-batch-create.html` - 批次交易輸入主頁面
- `transactionsBatch_Controller.js` - 批次交易控制器
- `transactionsBatch_UI.js` - 批次交易 UI 管理模組
- `transactionsBatch_Service.js` - 批次交易服務層
- `test-batch-transactions.html` - 功能測試頁面

### 相依檔案
- 現有的交易創建模組（`transactionCreate_*.js`）
- 共用工具庫（`../../common/`）

## 功能特色

### 1. 共同屬性動態選擇
- 使用者可自行定義哪些欄位作為共同屬性
- 支援勾選機制選擇要套用為共同值的欄位
- 減少重複輸入，提高效率

### 2. 智慧表格介面
- 動態表格只顯示非共同屬性欄位
- 支援新增/刪除行功能
- 即時資料驗證和錯誤提示

### 3. 完整下拉選單支援
- 保留原有系統的所有下拉選單功能
- 帳戶、稅別、交易項目、交易對象等選單
- 支援搜尋和選擇功能

### 4. 批次資料驗證
- 即時驗證每筆交易資料
- 必填欄位檢查
- 業務邏輯驗證（如日期合理性、稅額計算等）
- 視覺化錯誤提示

### 5. 批次儲存處理
- 一次性處理多筆交易
- 支援部分成功的情況
- 完整的進度顯示
- 詳細的處理結果報告

## 使用方式

### 1. 開啟批次交易頁面
```
開啟 transactions-batch-create.html
```

### 2. 設定共同屬性
1. 在「共同屬性設定」區域勾選要作為共同值的欄位
2. 設定對應的共同值
3. 這些值會自動套用到所有交易行

### 3. 輸入個別交易資料
1. 在批次交易資料表格中輸入每筆交易的特定資料
2. 只需輸入非共同屬性的欄位
3. 使用「新增交易」按鈕增加更多行

### 4. 驗證資料
1. 點擊「驗證資料」按鈕檢查所有資料
2. 查看錯誤提示並修正問題
3. 成功的行會顯示為綠色，有錯誤的行會顯示為紅色

### 5. 批次儲存
1. 點擊「批次儲存」按鈕開始處理
2. 觀看處理進度
3. 查看處理結果摘要

## 支援的欄位

### 共同屬性欄位
- 交易類型（支出/收入/轉移）
- 我方主要帳戶
- 帳款到帳情形
- 收款/付款日期
- 憑證/發票日期
- 稅別
- 手續費
- 狀態

### 個別交易欄位
- 交易對象（支援搜尋）
- 交易項目（支援分類選單）
- 金額
- 稅額（自動計算）
- 發票號碼
- 備註

## 資料驗證規則

### 必填欄位
- 交易類型
- 主要帳戶
- 帳款到帳情形
- 交易對象
- 交易項目
- 金額（必須大於0）
- 稅別
- 狀態
- 付款日期
- 發票日期

### 業務邏輯驗證
- 付款日期不應早於發票日期
- 稅額計算正確性檢查
- 金額格式驗證

## 錯誤處理

### 驗證錯誤
- 即時顯示錯誤訊息
- 行級別的錯誤標示
- 詳細的錯誤說明

### 儲存錯誤
- 支援部分成功的批次處理
- 詳細的錯誤報告
- 可選擇只儲存有效資料

## 效能考量

### 批次處理優化
- 逐筆處理避免記憶體溢出
- 進度顯示提升使用者體驗
- 錯誤隔離避免全部失敗

### 使用者介面優化
- 動態表格減少不必要的欄位
- 智慧預設值減少輸入
- 即時驗證提早發現問題

## 測試方式

### 使用測試頁面
1. 開啟 `test-batch-transactions.html`
2. 執行各項功能測試
3. 檢查測試結果

### 手動測試步驟
1. 測試共同屬性設定
2. 測試表格動態生成
3. 測試資料驗證
4. 測試批次儲存
5. 測試錯誤處理

## 注意事項

### 資料安全
- 批次處理前會進行完整驗證
- 支援取消操作
- 處理結果可匯出備查

### 系統整合
- 完全相容現有交易系統
- 使用相同的會計分錄邏輯
- 保持資料一致性

### 使用限制
- 建議單次處理不超過100筆交易
- 複雜交易建議使用單筆輸入
- 需要明細資料的交易暫不支援批次處理

## 故障排除

### 常見問題
1. **頁面載入失敗**：檢查檔案路徑和相依性
2. **資料載入錯誤**：確認資料庫連線正常
3. **驗證失敗**：檢查必填欄位和資料格式
4. **儲存失敗**：查看錯誤訊息並修正資料

### 除錯方式
1. 開啟瀏覽器開發者工具
2. 查看控制台錯誤訊息
3. 使用測試頁面檢查功能
4. 檢查網路請求狀態

## 未來擴展

### 計劃功能
- 支援 CSV 匯入
- 範本儲存和載入
- 更多欄位的批次處理
- 明細資料的批次輸入

### 效能優化
- 虛擬滾動支援大量資料
- 背景處理長時間操作
- 快取機制提升載入速度
