<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欄位佈局調試工具</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🔍 欄位佈局調試工具</h1>
        
        <!-- 調試說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-blue-600">🎯 調試目標</h2>
            <p class="text-gray-700 mb-4">
                檢查兩列式佈局中是否有重複欄位，確保上下列中沒有相同的欄位出現。
            </p>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-medium text-blue-800 mb-2">檢查重點</h3>
                <ul class="text-blue-700 text-sm space-y-1">
                    <li>• individualPropertyFields 陣列是否有重複</li>
                    <li>• firstRowFields 和 secondRowFields 是否有交集</li>
                    <li>• 實際表格生成時的欄位分配</li>
                    <li>• 欄位的事件監聽器綁定情況</li>
                </ul>
            </div>
        </div>
        
        <!-- 調試工具 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🛠️ 調試工具</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    開啟批次頁面
                </button>
                <button onclick="checkFieldArrays()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    <i class="fas fa-search mr-2"></i>
                    檢查欄位陣列
                </button>
                <button onclick="checkTableLayout()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    <i class="fas fa-table mr-2"></i>
                    檢查表格佈局
                </button>
                <button onclick="checkEventListeners()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    <i class="fas fa-bolt mr-2"></i>
                    檢查事件監聽器
                </button>
            </div>
            
            <!-- 調試結果顯示區 -->
            <div id="debugResults" class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">調試結果</h3>
                    <div id="resultContent" class="text-sm text-gray-600">
                        點擊上方按鈕開始調試...
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 手動測試指引 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibent mb-4">📋 手動測試指引</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 1: 檢查預設狀態</h3>
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <ol class="text-sm text-blue-700 space-y-1">
                            <li>1. 開啟批次交易頁面</li>
                            <li>2. 觀察預設的表格標題</li>
                            <li>3. 檢查第一列和第二列的欄位</li>
                            <li>4. 確認沒有重複的欄位名稱</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 2: 動態變更測試</h3>
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <ol class="text-sm text-green-700 space-y-1">
                            <li>1. 勾選/取消不同的共同屬性</li>
                            <li>2. 觀察表格重建後的欄位分配</li>
                            <li>3. 檢查是否有欄位重複出現</li>
                            <li>4. 測試不同的欄位組合</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 3: 功能測試</h3>
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <ol class="text-sm text-yellow-700 space-y-1">
                            <li>1. 在每個欄位中輸入測試資料</li>
                            <li>2. 檢查是否有欄位無法輸入</li>
                            <li>3. 測試特殊功能（標籤、搜尋等）</li>
                            <li>4. 確認所有欄位都有效</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 4: 控制台檢查</h3>
                    <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <ol class="text-sm text-purple-700 space-y-1">
                            <li>1. 按F12開啟開發者工具</li>
                            <li>2. 在Console中執行調試命令</li>
                            <li>3. 檢查是否有錯誤訊息</li>
                            <li>4. 查看欄位陣列的實際內容</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 調試命令 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">💻 調試命令</h2>
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">在瀏覽器控制台中執行以下命令：</h3>
                    <div class="space-y-2">
                        <div class="p-2 bg-white border rounded">
                            <code class="text-sm text-gray-700">
                                // 檢查個別屬性欄位陣列<br>
                                console.log('individualPropertyFields:', individualPropertyFields);
                            </code>
                        </div>
                        <div class="p-2 bg-white border rounded">
                            <code class="text-sm text-gray-700">
                                // 檢查表格佈局<br>
                                console.log('tableFieldsLayout:', window.tableFieldsLayout);
                            </code>
                        </div>
                        <div class="p-2 bg-white border rounded">
                            <code class="text-sm text-gray-700">
                                // 檢查重複欄位<br>
                                const first = window.tableFieldsLayout?.firstRowFields || [];<br>
                                const second = window.tableFieldsLayout?.secondRowFields || [];<br>
                                const duplicates = first.filter(field => second.includes(field));<br>
                                console.log('重複欄位:', duplicates);
                            </code>
                        </div>
                        <div class="p-2 bg-white border rounded">
                            <code class="text-sm text-gray-700">
                                // 檢查所有欄位<br>
                                const allIndividualFields = [...first, ...second];<br>
                                const uniqueFields = [...new Set(allIndividualFields)];<br>
                                console.log('總欄位數:', allIndividualFields.length);<br>
                                console.log('唯一欄位數:', uniqueFields.length);<br>
                                console.log('是否有重複:', allIndividualFields.length !== uniqueFields.length);
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openBatchPage() {
            addResult('開啟批次交易頁面...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function checkFieldArrays() {
            addResult('請在批次頁面的控制台中執行調試命令', 'info');
            addResult('命令: console.log("individualPropertyFields:", individualPropertyFields);', 'code');
        }
        
        function checkTableLayout() {
            addResult('請在批次頁面的控制台中檢查表格佈局', 'info');
            addResult('命令: console.log("tableFieldsLayout:", window.tableFieldsLayout);', 'code');
        }
        
        function checkEventListeners() {
            addResult('檢查事件監聽器綁定情況...', 'info');
            addResult('請在批次頁面中測試各欄位的功能', 'warning');
        }
        
        function addResult(message, type = 'info') {
            const resultContent = document.getElementById('resultContent');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            } else if (type === 'code') {
                bgColor = 'bg-gray-50 border-gray-200 text-gray-800';
                icon = 'fa-code';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor} mb-2`;
            resultItem.innerHTML = `
                <div class="flex items-start">
                    <i class="fas ${icon} mr-2 mt-0.5"></i>
                    <div class="flex-1">
                        <span class="text-sm">${message}</span>
                        <span class="ml-auto text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                    </div>
                </div>
            `;
            
            if (resultContent.textContent.includes('點擊上方按鈕開始調試...')) {
                resultContent.innerHTML = '';
            }
            
            resultContent.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 頁面載入時顯示說明
        document.addEventListener('DOMContentLoaded', function() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-search mr-2"></i>
                    <span>欄位佈局調試工具已準備就緒</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        });
    </script>
</body>
</html>
