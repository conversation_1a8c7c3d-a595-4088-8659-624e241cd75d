/**
 * @file transactionCreate_UI.js
 * @description 這個檔案是模組的UI操作。
 * - 它負責處理頁面上的 UI 事件和操作。
 * - 作為整個模組的處理DOM元素顯示及更新。
 */

/**
 * @description 獲取頁面上 有關會計分錄 的 DOM 元素
 * @returns {Object} DOM 元素物件
 */
function getJournalCard_DOMElements() {
    return {
        //會計分錄UI
        creditContainer : document.getElementById('creditAccountingContainer'),
        debitContainer : document.getElementById('debitAccountingContainer'),
        creditEntityEl  : document.getElementById('creditEntity'),
        debitEntityEl : document.getElementById('debitEntity')
    };
}

/**
 * @description 獲取頁面上 有關輸入表單 的 DOM 元素
 * @returns {Object} DOM 元素物件
 */
function getTransactionFormCard_DOMElements() {
    return {
        //交易表單UI
        form  : document.getElementById('transactionForm'),
        //切換交易類型
        transactionType_incomeCheckbox : document.getElementById('transactionType_income'),
        transactionType_expenseCheckbox : document.getElementById('transactionType_expense'),
        transactionType_transferCheckbox : document.getElementById('transactionType_transfer'),
        transactionType : document.querySelector('input[name="transactionType"]:checked'),
        //我方主要帳戶
        accountIdSelect: document.getElementById('accountSelect'),
        //日期相關
        paymentStatusSelect: document.getElementById('paymentStatusSelect'),//帳款到帳情形
        invoiceDateSelect: document.getElementById('invoiceDate'),//發票日期
        paymentDateSelect: document.getElementById('paymentDate'),//付款日期
        expectedPaymentDateSelect : document.getElementById('expectedPaymentDate'),//預計付款日期
        //交易項目描述(會計科目代號)
        paymentDescriptionMegaMenuContainer : document.getElementById('paymentDescriptionMegaMenuContainer'),//整個容器包含科目代號欄位
        paymentDescriptionMegaMenu : document.getElementById('paymentDescriptionMegaMenu'),//整個操作容器
        paymentDescriptionMenuBtn : document.getElementById('paymentDescriptionMenuBtn'),//展開按鈕
        paymentDescriptionSelectedEl : document.getElementById('paymentDescriptionSelected'),//顯示選擇的科目
        paymentDescriptionMenuDropdown : document.getElementById('paymentDescriptionMenuDropdown'),//科目選單
        paymentDescriptionCodeInput : document.getElementById('paymentDescription'),//會計科目代號
        //交易對象
        selectedEntityContainer : document.getElementById('selectedEntityContainer'),//整個容器包含交易對象欄位
        entitySearchInput : document.getElementById('entitySearch'),//交易對象 輸入之搜尋欄位
        selectedEntityId : document.getElementById('selectedEntityId'),//交易對象 隱藏之交易對象ID
        selectedEntityType : document.getElementById('selectedEntityType'),//交易對象 隱藏之交易對象類型
        //金額相關
        amountInput : document.getElementById('amount'),
        //稅別及稅額
        taxTypeIdSelect: document.getElementById('taxTypeSelect'),
        taxAmountInput : document.getElementById('taxAmount'),
        //發票號碼
        invoiceNumberContainer : document.getElementById('invoiceNumberContainer'),
        invoiceNumberInput : document.getElementById('invoiceNumber'),
        //手續費
        feeInputContainer : document.getElementById('feeInputContainer'),
        feeToggle : document.getElementById('feeToggle'),
        feeAmountInput : document.getElementById('fee'),
        //備註文字相關
        notesInput : document.getElementById('notes'),
        //標籤TAG
        tagContainer : document.getElementById('tagContainer'),//已輸入標籤 顯示之容器
        tagInput : document.getElementById('tagInput'),//標籤 輸入之欄位
        //交易是否完成之狀態
        transactionStatusSelect: document.getElementById('transactionStatusSelect')
    };
}

/**
 * @description 獲取頁面上 有關交易明細 的 DOM 元素
 * @returns {Object} DOM 元素物件
 */

function getTransactionDetailCard_DOMElements() {
    return {
        //交易明細UI
        detailSwitchBtn : document.getElementById('detailSwitch'),
        detailRows : document.getElementById('detailTableBody').querySelectorAll('tr'),//明細表列數
    };
}

/**
 * @description 獲取 打包後的表單資料
 * @returns {Object} 打包後的表單資料
 */
function getTransactionFormData() {
    const AllTransactionFormData= getTransactionFormCard_DOMElements();

    const formData = {
        transactionType : AllTransactionFormData.transactionType.value,
        paymentStatus : AllTransactionFormData.paymentStatusSelect.value,
        transactionStatus : AllTransactionFormData.transactionStatusSelect.value,
        accountId : AllTransactionFormData.accountIdSelect.value,
        entityId : AllTransactionFormData.selectedEntityId.value,
        entityType : AllTransactionFormData.selectedEntityType.value,
        amount : parseFloat(AllTransactionFormData.amountInput.value) || 0,
        fee : parseFloat(AllTransactionFormData.feeAmountInput.value) || 0,
        taxTypeId : AllTransactionFormData.taxTypeIdSelect.value,
        invoiceNumber : AllTransactionFormData.invoiceNumberInput.value,
        taxAmount : parseFloat(AllTransactionFormData.taxAmountInput.value) || 0,
        invoiceDate : AllTransactionFormData.invoiceDateSelect.value,
        paymentDate : AllTransactionFormData.paymentDateSelect.value,
        expectedPaymentDate : AllTransactionFormData.expectedPaymentDateSelect.value,
        paymentDescription : Number(AllTransactionFormData.paymentDescriptionCodeInput.value),
        notes : AllTransactionFormData.notesInput.value,
        tags : window.tagManager.getTags()
    };

    return formData;
}

/**
 * @description 獲取 打包後 的 交易明細資料
 * @param {Object} savedTransactionId 已儲存的交易ID
 * @returns {Object} 打包後的交易明細資料 
 */
function getTransactionDetailFormData(savedTransactionId) {
    const { detailSwitchBtn,detailRows } = getTransactionDetailCard_DOMElements();
    const detailData = [];

    const rows = document.querySelectorAll('#detailTableBody tr');

    rows.forEach((row, index) => {
        const itemInput = row.querySelector('input[name^="detail_item_"]');
        const priceInput = row.querySelector('input[name^="detail_price_"]');
        const quantityInput = row.querySelector('[name^="detail_quantity_"]');
        const unitInput = row.querySelector('[name^="detail_unit_"]');
        const totalInput = row.querySelector('input[name^="detail_total_"]');


        const itemValue = itemInput?.value ?? '';
        const priceValue = priceInput?.value ?? '';
        const quantityValue = quantityInput?.value ?? '';
        const unitValue = unitInput?.value ?? '';
        const totalValue = totalInput?.value ?? '';
        
        const detail = {
            transactionId: savedTransactionId,
            itemNo: index + 1,
            item: itemValue,
            price: parseFloat(priceValue),
            quantity: parseFloat(quantityValue),
            unit: unitValue,
            total: parseFloat(totalValue)
        };
        detailData.push(detail);
    });

    return detailData;
}
