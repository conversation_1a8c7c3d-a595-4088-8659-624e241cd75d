<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批次交易功能測試 v2</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">批次交易功能測試 v2</h1>
        
        <!-- 功能測試區域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <!-- 基本功能測試 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">基本功能測試</h2>
                <div class="space-y-3">
                    <button onclick="testBasicFunctions()" 
                        class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        測試基本函式載入
                    </button>
                    <button onclick="testDataLoading()" 
                        class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        測試資料載入
                    </button>
                    <button onclick="testUIComponents()" 
                        class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                        測試 UI 元件
                    </button>
                </div>
            </div>
            
            <!-- 互動功能測試 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">互動功能測試</h2>
                <div class="space-y-3">
                    <button onclick="testCommonFields()" 
                        class="w-full px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                        測試共同欄位
                    </button>
                    <button onclick="testEntitySearch()" 
                        class="w-full px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600">
                        測試交易對象搜尋
                    </button>
                    <button onclick="testPaymentDescription()" 
                        class="w-full px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600">
                        測試交易項目選單
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">快速操作</h2>
            <div class="flex flex-wrap gap-3">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    <i class="fas fa-external-link-alt mr-2"></i>開啟批次頁面
                </button>
                <button onclick="clearResults()" 
                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    <i class="fas fa-eraser mr-2"></i>清除結果
                </button>
                <button onclick="runAllTests()" 
                    class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                    <i class="fas fa-play mr-2"></i>執行所有測試
                </button>
            </div>
        </div>
        
        <!-- 測試結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">測試結果</h2>
            <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto">
                <p class="text-gray-600">點擊上方按鈕開始測試...</p>
            </div>
        </div>
    </div>

    <!-- 載入必要的腳本 -->
    <script src="../../common/firebaseAPI/auth.js"></script>
    <script src="../../common/db/db.js"></script>
    <script src="../../common/db/preload.js"></script>
    <script src="../../common/utils/CommonUtils.js"></script>
    <script src="../../common/utils/pageTransfer.js"></script>
    <script src="../../common/utils/ModalUtils.js"></script>
    <script src="../../common/utils/DatabaseErrors.js"></script>
    
    <!-- 現有模組 -->
    <script src="transactionCreate_Service.js"></script>
    <script src="transactionCreate_JournalManagement_Module.js"></script>
    
    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor}`;
            resultItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span class="text-sm">${message}</span>
                    <span class="ml-auto text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            
            resultsDiv.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-600">測試結果已清除</p>';
        }
        
        async function testBasicFunctions() {
            addTestResult('開始測試基本函式...', 'info');
            
            const functions = [
                'getAccountsAll',
                'getTaxTypesRatesAll', 
                'getEmployeesAll',
                'getEntitiesAll',
                'getTransactionCategoriesAll',
                'getTransactionCategoryItemsAll',
                'saveTransactionToDB'
            ];
            
            let passCount = 0;
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addTestResult(`✓ ${funcName} 函式可用`, 'success');
                    passCount++;
                } else {
                    addTestResult(`✗ ${funcName} 函式不可用`, 'error');
                }
            });
            
            addTestResult(`基本函式測試完成 (${passCount}/${functions.length})`, 'info');
        }
        
        async function testDataLoading() {
            addTestResult('開始測試資料載入...', 'info');
            
            try {
                const accounts = await getAccountsAll();
                addTestResult(`✓ 成功載入 ${accounts.length} 個帳戶`, 'success');
                
                const taxTypes = await getTaxTypesRatesAll();
                addTestResult(`✓ 成功載入 ${taxTypes.length} 個稅別`, 'success');
                
                const employees = await getEmployeesAll();
                addTestResult(`✓ 成功載入 ${employees.length} 個員工`, 'success');
                
                const entities = await getEntitiesAll();
                addTestResult(`✓ 成功載入 ${entities.length} 個交易對象`, 'success');
                
                const categories = await getTransactionCategoriesAll();
                addTestResult(`✓ 成功載入 ${categories.length} 個交易分類`, 'success');
                
                const items = await getTransactionCategoryItemsAll();
                addTestResult(`✓ 成功載入 ${items.length} 個交易項目`, 'success');
                
                addTestResult('資料載入測試完成', 'info');
                
            } catch (error) {
                addTestResult(`資料載入測試失敗: ${error.message}`, 'error');
            }
        }
        
        async function testUIComponents() {
            addTestResult('開始測試 UI 元件...', 'info');
            
            // 測試是否能創建基本 UI 元素
            try {
                const testDiv = document.createElement('div');
                testDiv.innerHTML = `
                    <input type="text" class="test-input">
                    <select class="test-select"><option>測試</option></select>
                    <button class="test-button">測試按鈕</button>
                `;
                
                addTestResult('✓ 基本 UI 元素創建成功', 'success');
                
                // 測試事件監聽
                const testButton = testDiv.querySelector('.test-button');
                testButton.addEventListener('click', () => {
                    addTestResult('✓ 事件監聽器運作正常', 'success');
                });
                
                testButton.click();
                
                addTestResult('UI 元件測試完成', 'info');
                
            } catch (error) {
                addTestResult(`UI 元件測試失敗: ${error.message}`, 'error');
            }
        }
        
        async function testCommonFields() {
            addTestResult('開始測試共同欄位功能...', 'info');
            
            // 模擬共同欄位設定
            window.commonFields = { transactionType: true, account: true };
            addTestResult('✓ 共同欄位設定模擬成功', 'success');
            
            addTestResult('共同欄位測試完成', 'info');
        }
        
        async function testEntitySearch() {
            addTestResult('開始測試交易對象搜尋...', 'info');
            
            try {
                const employees = await getEmployeesAll();
                const entities = await getEntitiesAll();
                
                // 模擬搜尋
                const searchTerm = '測試';
                const matchedEmployees = employees.filter(emp => 
                    emp.name.includes(searchTerm)
                ).slice(0, 3);
                
                const matchedEntities = entities.filter(entity => 
                    entity.name.includes(searchTerm)
                ).slice(0, 3);
                
                addTestResult(`✓ 搜尋到 ${matchedEmployees.length} 個員工`, 'success');
                addTestResult(`✓ 搜尋到 ${matchedEntities.length} 個交易對象`, 'success');
                
                addTestResult('交易對象搜尋測試完成', 'info');
                
            } catch (error) {
                addTestResult(`交易對象搜尋測試失敗: ${error.message}`, 'error');
            }
        }
        
        async function testPaymentDescription() {
            addTestResult('開始測試交易項目選單...', 'info');
            
            try {
                const categories = await getTransactionCategoriesAll();
                const items = await getTransactionCategoryItemsAll();
                
                const expenseCategories = categories.filter(cat => cat.type === 'expense');
                const incomeCategories = categories.filter(cat => cat.type === 'income');
                
                addTestResult(`✓ 載入 ${expenseCategories.length} 個支出分類`, 'success');
                addTestResult(`✓ 載入 ${incomeCategories.length} 個收入分類`, 'success');
                addTestResult(`✓ 載入 ${items.length} 個交易項目`, 'success');
                
                addTestResult('交易項目選單測試完成', 'info');
                
            } catch (error) {
                addTestResult(`交易項目選單測試失敗: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            addTestResult('開始執行所有測試...', 'info');
            
            await testBasicFunctions();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDataLoading();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testUIComponents();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCommonFields();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testEntitySearch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPaymentDescription();
            
            addTestResult('所有測試執行完成！', 'success');
        }
        
        function openBatchPage() {
            addTestResult('開啟批次交易頁面...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        // 頁面載入時自動執行基本測試
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('測試頁面 v2 載入完成', 'success');
            addTestResult('可以開始進行功能測試', 'info');
        });
    </script>
</body>
</html>
