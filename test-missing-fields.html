<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缺失欄位修正驗證</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">📋 缺失欄位修正驗證</h1>
        
        <!-- 問題說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-blue-600">🎯 修正目標</h2>
            <p class="text-gray-700 mb-4">
                確保批次交易頁面包含單筆交易頁面的所有欄位，讓使用者可以在批次操作中輸入所有必要資訊。
            </p>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-medium text-blue-800 mb-2">原則</h3>
                <p class="text-blue-700 text-sm">
                    單筆交易頁面的每個欄位都應該在批次交易頁面中可用，無論是作為共同屬性還是個別屬性。
                </p>
            </div>
        </div>
        
        <!-- 欄位對比 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">📊 欄位對比</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">單筆交易頁面欄位</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>交易類型</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>我方主要帳戶</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>帳款到帳情形</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>收款/付款日期</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>憑證/發票日期</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>預計收/付款日期</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>交易項目</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>交易對象/帳戶</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            <span>金額</span>
                        </div>
                    </div>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">新增到批次頁面的欄位</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-plus text-blue-500 mr-2"></i>
                            <span>稅別</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-plus text-blue-500 mr-2"></i>
                            <span>稅額</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-plus text-blue-500 mr-2"></i>
                            <span>發票號碼</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-plus text-blue-500 mr-2"></i>
                            <span>手續費</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-plus text-blue-500 mr-2"></i>
                            <span>備註</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-plus text-blue-500 mr-2"></i>
                            <span>標籤</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-plus text-red-500 mr-2"></i>
                            <span class="font-medium text-red-600">狀態 (重點修正)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 修正詳情 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🔧 修正詳情</h2>
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">1. 更新欄位陣列</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 修正前：缺少多個欄位
const allFields = ['transactionType', 'accountId', 'taxTypeId', 'paymentDescription', 'entityId'];

// 修正後：包含所有欄位
const allFields = [
    'transactionType', 'accountId', 'taxTypeId', 'paymentDescription', 'entityId',
    'amount', 'taxAmount', 'invoiceNumber', 'fee', 'notes', 'tags', 'transactionStatus'
];</code></pre>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">2. 添加狀態欄位處理</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>case 'transactionStatus':
    return `
        &lt;select class="w-full p-1 border rounded" name="${namePrefix}"&gt;
            &lt;option value=""&gt;請選擇狀態&lt;/option&gt;
            &lt;option value="completed"&gt;已完成&lt;/option&gt;
            &lt;option value="pending"&gt;未撥款&lt;/option&gt;
        &lt;/select&gt;
    `;</code></pre>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">3. 添加HTML複選框</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>&lt;!-- 新增的欄位複選框 --&gt;
&lt;label class="flex items-center"&gt;
    &lt;input type="checkbox" class="common-property-toggle" data-field="amount"&gt;
    &lt;span class="ml-2 text-sm"&gt;金額 (含稅)&lt;/span&gt;
&lt;/label&gt;
&lt;label class="flex items-center"&gt;
    &lt;input type="checkbox" class="common-property-toggle" data-field="transactionStatus"&gt;
    &lt;span class="ml-2 text-sm"&gt;狀態&lt;/span&gt;
&lt;/label&gt;</code></pre>
                </div>
            </div>
        </div>
        
        <!-- 測試指引 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🧪 測試指引</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 1: 共同屬性檢查</h3>
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <ol class="text-sm text-blue-700 space-y-1">
                            <li>1. 開啟批次交易頁面</li>
                            <li>2. 檢查左側共同屬性區域</li>
                            <li>3. 確認所有新增欄位的複選框存在</li>
                            <li>4. 特別檢查"狀態"複選框</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 2: 欄位功能測試</h3>
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <ol class="text-sm text-green-700 space-y-1">
                            <li>1. 勾選"狀態"為共同屬性</li>
                            <li>2. 檢查共同屬性區是否出現狀態選單</li>
                            <li>3. 測試選擇"已完成"或"未撥款"</li>
                            <li>4. 確認功能正常運作</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 3: 個別屬性測試</h3>
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <ol class="text-sm text-yellow-700 space-y-1">
                            <li>1. 取消勾選"狀態"共同屬性</li>
                            <li>2. 檢查表格中是否出現狀態欄位</li>
                            <li>3. 測試每行的狀態選擇功能</li>
                            <li>4. 確認可以為不同行設置不同狀態</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">測試 4: 完整性檢查</h3>
                    <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <ol class="text-sm text-purple-700 space-y-1">
                            <li>1. 對比單筆交易頁面的所有欄位</li>
                            <li>2. 確認批次頁面都有對應欄位</li>
                            <li>3. 測試所有新增欄位的功能</li>
                            <li>4. 驗證資料輸入和保存</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 測試按鈕 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">🚀 開始測試</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    批次交易頁面
                </button>
                <button onclick="openCreatePage()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    單筆交易頁面
                </button>
                <button onclick="showFieldComparison()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    <i class="fas fa-list mr-2"></i>
                    欄位對比
                </button>
                <button onclick="showTestResults()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    <i class="fas fa-check-circle mr-2"></i>
                    測試結果
                </button>
            </div>
            
            <!-- 欄位對比 -->
            <div id="fieldComparison" class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg hidden">
                <h3 class="font-medium text-yellow-800 mb-3">📋 欄位對比清單</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="text-sm font-medium text-yellow-700 mb-2">原有欄位</h4>
                        <div class="space-y-1 text-xs text-yellow-600">
                            <div>✅ 交易類型</div>
                            <div>✅ 我方主要帳戶</div>
                            <div>✅ 帳款到帳情形</div>
                            <div>✅ 日期欄位群組</div>
                            <div>✅ 稅別</div>
                            <div>✅ 交易項目</div>
                            <div>✅ 交易對象</div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-yellow-700 mb-2">新增欄位</h4>
                        <div class="space-y-1 text-xs text-yellow-600">
                            <div>🆕 金額 (含稅)</div>
                            <div>🆕 稅額</div>
                            <div>🆕 發票號碼</div>
                            <div>🆕 手續費</div>
                            <div>🆕 備註</div>
                            <div>🆕 標籤</div>
                            <div>🔥 狀態 (重點)</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 測試結果 -->
            <div id="testResults" class="mt-6 p-4 bg-purple-50 border border-purple-200 rounded-lg hidden">
                <h3 class="font-medium text-purple-800 mb-3">📊 測試結果記錄</h3>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-purple-700">所有新增欄位的複選框都存在</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-purple-700">狀態欄位可以設為共同屬性</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-purple-700">狀態欄位可以設為個別屬性</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-purple-700">狀態選單選項正確（已完成、未撥款）</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-purple-700">所有欄位功能正常運作</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="mr-2">
                        <span class="text-sm text-purple-700">批次頁面欄位完整性達到100%</span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openBatchPage() {
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function openCreatePage() {
            window.open('transactionCreate.html', '_blank');
        }
        
        function showFieldComparison() {
            const element = document.getElementById('fieldComparison');
            element.classList.toggle('hidden');
        }
        
        function showTestResults() {
            const element = document.getElementById('testResults');
            element.classList.toggle('hidden');
        }
        
        // 頁面載入時顯示成功訊息
        document.addEventListener('DOMContentLoaded', function() {
            // 顯示成功通知
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>缺失欄位已全部添加完成！</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            // 5秒後自動隱藏
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 5000);
        });
    </script>
</body>
</html>
