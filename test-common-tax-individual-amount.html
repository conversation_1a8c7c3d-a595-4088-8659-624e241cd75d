<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共同稅別個別金額測試</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🧪 共同稅別個別金額測試</h1>
        
        <!-- 場景說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-blue-600">📋 測試場景</h2>
            <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="font-medium text-blue-800 mb-2">當前配置</h3>
                <ul class="text-blue-700 text-sm space-y-1">
                    <li>• <strong>稅別：</strong>共同屬性（在左側共同屬性區）</li>
                    <li>• <strong>金額：</strong>個別屬性（在表格的每一行）</li>
                    <li>• <strong>稅額：</strong>個別屬性（在表格的每一行）</li>
                    <li>• <strong>預期行為：</strong>當共同稅別變更時，所有行的稅額應該重新計算</li>
                </ul>
            </div>
        </div>
        
        <!-- 測試步驟 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibent mb-4">🔧 測試步驟</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 1: 設置共同屬性</h3>
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <ol class="text-sm text-green-700 space-y-1">
                            <li>1. 開啟批次交易頁面</li>
                            <li>2. 確認"稅別"已勾選為共同屬性</li>
                            <li>3. 確認"金額"和"稅額"未勾選（個別屬性）</li>
                            <li>4. 在共同屬性區選擇稅別（如5%營業稅）</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 2: 輸入測試資料</h3>
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <ol class="text-sm text-yellow-700 space-y-1">
                            <li>1. 在第1行的金額欄位輸入 1000</li>
                            <li>2. 檢查第1行的稅額是否自動計算</li>
                            <li>3. 添加第2行，輸入金額 2000</li>
                            <li>4. 檢查第2行的稅額是否自動計算</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 3: 測試稅別變更</h3>
                    <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <ol class="text-sm text-purple-700 space-y-1">
                            <li>1. 在共同屬性區變更稅別（如改為免稅）</li>
                            <li>2. 檢查所有行的稅額是否重新計算</li>
                            <li>3. 再次變更稅別（如改為10%稅率）</li>
                            <li>4. 確認所有行的稅額都正確更新</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 4: 調試驗證</h3>
                    <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <ol class="text-sm text-red-700 space-y-1">
                            <li>1. 按F12開啟開發者工具</li>
                            <li>2. 執行下方的調試命令</li>
                            <li>3. 檢查控制台輸出的結果</li>
                            <li>4. 確認事件監聽器正確綁定</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 調試命令 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibent mb-4">💻 調試命令</h2>
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">在批次頁面控制台中執行：</h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">1. 檢查共同稅別設置</h4>
                            <code class="text-xs text-gray-600 block">
                                // 檢查共同稅別<br>
                                const commonTaxSelect = document.getElementById('batch_taxTypeId');<br>
                                console.log('共同稅別選擇器:', commonTaxSelect);<br>
                                if (commonTaxSelect && commonTaxSelect.value) {<br>
                                &nbsp;&nbsp;const option = commonTaxSelect.options[commonTaxSelect.selectedIndex];<br>
                                &nbsp;&nbsp;console.log('選中的稅別:', option.text);<br>
                                &nbsp;&nbsp;console.log('稅率:', option.dataset.rate);<br>
                                } else {<br>
                                &nbsp;&nbsp;console.log('❌ 沒有選擇共同稅別');<br>
                                }
                            </code>
                        </div>
                        
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">2. 檢查個別金額和稅額欄位</h4>
                            <code class="text-xs text-gray-600 block">
                                // 檢查所有行的金額和稅額欄位<br>
                                const firstRows = document.querySelectorAll('#batchTableBody tr.batch-row-first');<br>
                                console.log('邏輯行數量:', firstRows.length);<br><br>
                                firstRows.forEach((row, index) => {<br>
                                &nbsp;&nbsp;const rowNum = parseInt(row.dataset.rowNum);<br>
                                &nbsp;&nbsp;const amountInput = document.querySelector(`input[name="batch_amount_${rowNum}"]`);<br>
                                &nbsp;&nbsp;const taxInput = document.querySelector(`input[name="batch_taxAmount_${rowNum}"]`);<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;console.log(`第${rowNum}行:`);<br>
                                &nbsp;&nbsp;console.log('  金額輸入框:', amountInput ? `存在，值=${amountInput.value}` : '不存在');<br>
                                &nbsp;&nbsp;console.log('  稅額輸入框:', taxInput ? `存在，值=${taxInput.value}` : '不存在');<br>
                                });
                            </code>
                        </div>
                        
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">3. 手動觸發稅額計算</h4>
                            <code class="text-xs text-gray-600 block">
                                // 手動觸發所有行的稅額計算<br>
                                const commonTaxSelect = document.getElementById('batch_taxTypeId');<br>
                                if (commonTaxSelect && commonTaxSelect.value) {<br>
                                &nbsp;&nbsp;const selectedOption = commonTaxSelect.options[commonTaxSelect.selectedIndex];<br>
                                &nbsp;&nbsp;const taxRate = parseFloat(selectedOption.dataset.rate) || 0;<br>
                                &nbsp;&nbsp;console.log('使用稅率:', taxRate);<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;// 觸發所有行的稅額計算<br>
                                &nbsp;&nbsp;updateAllRowsTaxCalculationWithRate(taxRate);<br>
                                &nbsp;&nbsp;console.log('稅額計算完成');<br>
                                } else {<br>
                                &nbsp;&nbsp;console.log('❌ 無法獲取稅率');<br>
                                }
                            </code>
                        </div>
                        
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">4. 測試事件監聽器</h4>
                            <code class="text-xs text-gray-600 block">
                                // 測試金額輸入框的事件監聽器<br>
                                const amountInput1 = document.querySelector('input[name="batch_amount_1"]');<br>
                                if (amountInput1) {<br>
                                &nbsp;&nbsp;console.log('測試第1行金額輸入框事件監聽器...');<br>
                                &nbsp;&nbsp;amountInput1.value = '1000';<br>
                                &nbsp;&nbsp;amountInput1.dispatchEvent(new Event('input'));<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;setTimeout(() => {<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;const taxInput1 = document.querySelector('input[name="batch_taxAmount_1"]');<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;console.log('計算後的稅額:', taxInput1?.value || '無');<br>
                                &nbsp;&nbsp;}, 100);<br>
                                } else {<br>
                                &nbsp;&nbsp;console.log('❌ 第1行金額輸入框不存在');<br>
                                }
                            </code>
                        </div>
                        
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">5. 完整測試流程</h4>
                            <code class="text-xs text-gray-600 block">
                                // 完整測試流程<br>
                                function testCommonTaxIndividualAmount() {<br>
                                &nbsp;&nbsp;console.log('=== 開始測試共同稅別個別金額 ===');<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;// 1. 檢查配置<br>
                                &nbsp;&nbsp;const commonTaxSelect = document.getElementById('batch_taxTypeId');<br>
                                &nbsp;&nbsp;const taxRate = commonTaxSelect?.options[commonTaxSelect.selectedIndex]?.dataset.rate || 0;<br>
                                &nbsp;&nbsp;console.log('共同稅率:', taxRate);<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;// 2. 設置測試資料<br>
                                &nbsp;&nbsp;const amountInput1 = document.querySelector('input[name="batch_amount_1"]');<br>
                                &nbsp;&nbsp;const amountInput2 = document.querySelector('input[name="batch_amount_2"]');<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;if (amountInput1) {<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;amountInput1.value = '1000';<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;calculateRowTaxAmount(1);<br>
                                &nbsp;&nbsp;}<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;if (amountInput2) {<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;amountInput2.value = '2000';<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;calculateRowTaxAmount(2);<br>
                                &nbsp;&nbsp;}<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;// 3. 檢查結果<br>
                                &nbsp;&nbsp;setTimeout(() => {<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;const taxInput1 = document.querySelector('input[name="batch_taxAmount_1"]');<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;const taxInput2 = document.querySelector('input[name="batch_taxAmount_2"]');<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;console.log('第1行稅額:', taxInput1?.value || '無');<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;console.log('第2行稅額:', taxInput2?.value || '無');<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;const expectedTax1 = (1000 * taxRate / (1 + parseFloat(taxRate))).toFixed(2);<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;const expectedTax2 = (2000 * taxRate / (1 + parseFloat(taxRate))).toFixed(2);<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;console.log('預期第1行稅額:', expectedTax1);<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;console.log('預期第2行稅額:', expectedTax2);<br>
                                &nbsp;&nbsp;}, 200);<br>
                                }<br><br>
                                // 執行測試<br>
                                testCommonTaxIndividualAmount();
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 預期結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibent mb-4">🎯 預期結果</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-medium text-green-800 mb-2">正常情況</h3>
                    <ul class="text-green-700 text-sm space-y-1">
                        <li>• 金額輸入後稅額自動計算</li>
                        <li>• 共同稅別變更時所有行稅額重新計算</li>
                        <li>• 稅額顯示在對應的輸入框中</li>
                        <li>• 計算結果準確無誤</li>
                    </ul>
                </div>
                <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h3 class="font-medium text-red-800 mb-2">異常情況</h3>
                    <ul class="text-red-700 text-sm space-y-1">
                        <li>• 稅額輸入框為空或顯示0</li>
                        <li>• 共同稅別變更時稅額不更新</li>
                        <li>• 控制台出現錯誤訊息</li>
                        <li>• 事件監聽器未正確綁定</li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    開啟批次頁面
                </button>
                <button onclick="copyTestCode()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    <i class="fas fa-copy mr-2"></i>
                    複製測試代碼
                </button>
                <button onclick="showTroubleshooting()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    <i class="fas fa-wrench mr-2"></i>
                    問題排除
                </button>
            </div>
        </div>
    </div>

    <script>
        function openBatchPage() {
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function copyTestCode() {
            const testCode = `
function testCommonTaxIndividualAmount() {
    console.log('=== 開始測試共同稅別個別金額 ===');
    
    const commonTaxSelect = document.getElementById('batch_taxTypeId');
    const taxRate = commonTaxSelect?.options[commonTaxSelect.selectedIndex]?.dataset.rate || 0;
    console.log('共同稅率:', taxRate);
    
    const amountInput1 = document.querySelector('input[name="batch_amount_1"]');
    const amountInput2 = document.querySelector('input[name="batch_amount_2"]');
    
    if (amountInput1) {
        amountInput1.value = '1000';
        calculateRowTaxAmount(1);
    }
    
    if (amountInput2) {
        amountInput2.value = '2000';
        calculateRowTaxAmount(2);
    }
    
    setTimeout(() => {
        const taxInput1 = document.querySelector('input[name="batch_taxAmount_1"]');
        const taxInput2 = document.querySelector('input[name="batch_taxAmount_2"]');
        
        console.log('第1行稅額:', taxInput1?.value || '無');
        console.log('第2行稅額:', taxInput2?.value || '無');
        
        const expectedTax1 = (1000 * taxRate / (1 + parseFloat(taxRate))).toFixed(2);
        const expectedTax2 = (2000 * taxRate / (1 + parseFloat(taxRate))).toFixed(2);
        
        console.log('預期第1行稅額:', expectedTax1);
        console.log('預期第2行稅額:', expectedTax2);
    }, 200);
}

testCommonTaxIndividualAmount();
            `;
            
            navigator.clipboard.writeText(testCode).then(() => {
                alert('測試代碼已複製到剪貼板！請在批次頁面的控制台中貼上執行。');
            });
        }
        
        function showTroubleshooting() {
            alert('常見問題排除：\n1. 確認稅別為共同屬性，金額和稅額為個別屬性\n2. 選擇有稅率的稅別選項\n3. 檢查控制台是否有錯誤訊息\n4. 手動執行測試代碼驗證');
        }
        
        // 頁面載入時顯示說明
        document.addEventListener('DOMContentLoaded', function() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-calculator mr-2"></i>
                    <span>共同稅別個別金額測試工具已準備就緒</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        });
    </script>
</body>
</html>
