//載入初始化帳款到帳情形之選項欄位
function loadPaymentStatusOptions() {
    const paymentStatusSelect = document.getElementById('paymentStatusSelect');
    if(!paymentStatusSelect) return;
    const paymentStatusOptions = [
        {value: '', text: "請選擇狀態"},
        {value: 'same_day', text: '同日收付款（現金基礎）'},
        {value: 'receivable', text: '應收付款（權責基礎）（已開立發票未收款）'},
        {value: 'prepayment', text: '暫收付款（權責基礎）（未開立發票已收款'},
        //目前暫收款有衝突後續須修正
        // 'prepayment'使用在會計分錄中判斷
        // 'temporary'使用在交易單中判斷
        //修正方式為了向後支持，先在交易單中的判斷進行雙條件 prepayment || temporary
        {value: 'different_day', text: '非同日收款（現金基礎）(補未紀錄之已收款項)'}
    ];
    paymentStatusSelect.innerHTML = '';
    paymentStatusOptions.forEach(option => {
        const opt = document.createElement('option');
        opt.value = option.value;
        opt.textContent = option.text;
        paymentStatusSelect.appendChild(opt);
    });

    /** HTML原始情況，
     * <option value="">請選擇狀態</option>
    <option value="same_day">同日收付款（現金基礎）</option>
    <option value="receivable">應收付款（權責基礎）（已開立發票未收款）</option>
    <option value="prepayment">暫收付款（權責基礎）（未開立發票已收款）</option>
    <option value="different_day">非同日收款（現金基礎）(補未紀錄之已收款項)</option>
     */
}


// **公用**設定帳款到帳情形，自動調整相關欄位狀態及必填情形，
// 需盡量避免其他地方直接透過paymentStatusSelect.value或paymentStatusSelect.selectedIndex
// 進行強制設定值。
function setPaymentStatusSetting(paymentStatus){
    const paymentStatusSelect = document.getElementById('paymentStatusSelect');
    if(!paymentStatusSelect) {
        console.error('找不到帳款到帳情形選項');
        paymentStatusSelect.value = '';//預設為空值，以免影響其他頁面使用到paymentStatusSelect.value或paymentStatusSelect.selectedIndex時的預設值。
        paymentStatusSelect.selectedIndex = 0;//
        return;
    }
    
    if(paymentStatus==='same_day')
        //paymentStatusSelect.value = paymentStatus;
        paymentStatusSelect.selectedIndex = 1;
    else if(paymentStatus==='receivable')
        paymentStatusSelect.selectedIndex = 2;
    else if(paymentStatus==='prepayment' || paymentStatus==='temporary')
        paymentStatusSelect.selectedIndex = 3;
    else if(paymentStatus==='different_day')
        paymentStatusSelect.selectedIndex = 4;
    else
        paymentStatusSelect.value = '';
        

    // 依照到帳情形進行設置，設置收付款、預計收付款、發票日期等是否顯示及必填
    callPaymentDateFieldsSetting();
    
}

function callPaymentDateFieldsSetting(){
    const paymentStatusSelect = document.getElementById('paymentStatusSelect');
    const paymentDateInput = document.getElementById('paymentDate');
    const invoiceDateInput = document.getElementById('invoiceDate');
    const expectedPaymentDateContainer = document.getElementById('expectedPaymentDateContainer');
    const expectedPaymentDateInput = document.getElementById('expectedPaymentDate');


    const selectedStatus = paymentStatusSelect.value;

    // 重置所有欄位狀態
    paymentDateInput.disabled = false;
    paymentDateInput.classList.remove('opacity-50', 'cursor-not-allowed');
    invoiceDateInput.disabled = false;
    invoiceDateInput.classList.remove('opacity-50', 'cursor-not-allowed');
    expectedPaymentDateContainer.classList.add('hidden');
    
    switch(selectedStatus) {
        case 'same_day':
            // 同日收款
            paymentDateInput.disabled = false;
            invoiceDateInput.disabled = true;
            invoiceDateInput.classList.add('opacity-50', 'cursor-not-allowed');
            invoiceDateInput.value = paymentDateInput.value;
            expectedPaymentDateContainer.classList.add('hidden');
            //設置必填情形
            paymentDateInput.required = true;
            invoiceDateInput.required = true;
            expectedPaymentDateInput.required = false;
            break;

        case 'receivable':
            // 應收款
            paymentDateInput.disabled = true;
            paymentDateInput.classList.add('opacity-50', 'cursor-not-allowed');
            paymentDateInput.value = '';
            invoiceDateInput.disabled = false;
            expectedPaymentDateContainer.classList.remove('hidden');
            //設置必填情形
            paymentDateInput.required = false;
            invoiceDateInput.required = true;
            expectedPaymentDateInput.required = true;
            break;

        case 'temporary' || 'prepayment':
            // 暫收款
            paymentDateInput.disabled = false;
            invoiceDateInput.disabled = true;
            invoiceDateInput.classList.add('opacity-50', 'cursor-not-allowed');
            invoiceDateInput.value = '';
            expectedPaymentDateContainer.classList.add('hidden');
            //設置必填情形
            paymentDateInput.required = true;
            invoiceDateInput.required = false;
            expectedPaymentDateInput.required = false;
            break;

        case 'different_day':
            // 非日收款
            paymentDateInput.disabled = false;
            invoiceDateInput.disabled = false;
            expectedPaymentDateInput.disabled = true;
			expectedPaymentDateInput.value = paymentDateInput.value;
            expectedPaymentDateContainer.classList.remove('hidden');
            //設置必填情形
            paymentDateInput.required = true;
            invoiceDateInput.required = true;
            expectedPaymentDateInput.required = true;
            break;
    }

}


function load_PaymentStatus_EventListener() {
    const paymentStatusSelect = document.getElementById('paymentStatusSelect');
    const paymentDateInput = document.getElementById('paymentDate');
    const invoiceDateInput = document.getElementById('invoiceDate');
    const expectedPaymentDateInput = document.getElementById('expectedPaymentDate');

    // 監聽帳款到帳情形選項變化
    paymentStatusSelect.addEventListener('change', function() {
        callPaymentDateFieldsSetting();
    });

    // 監聽收款日期變化，用於同步發票日期（僅在同日收款時）
    paymentDateInput.addEventListener('change', function() {
        if (paymentStatusSelect.value === 'same_day') {
			//用於同步發票日期（僅在同日收款時）
            invoiceDateInput.value = this.value;
        }else if(paymentStatusSelect.value === 'different_day'){
			//用於同步預計付款日期（僅在非同日收款時）
			expectedPaymentDateInput.value = this.value;
		}
    });
	
}