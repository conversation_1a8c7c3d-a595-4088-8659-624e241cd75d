<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易對象搜尋功能測試</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">交易對象搜尋功能測試</h1>
        
        <!-- 測試控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">測試控制面板</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="testSearchFunctionality()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    搜尋功能測試
                </button>
                <button onclick="testKeyboardNavigation()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    鍵盤導航測試
                </button>
                <button onclick="testEntitySelection()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    選擇功能測試
                </button>
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    開啟批次頁面
                </button>
                <button onclick="clearResults()" 
                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    清除結果
                </button>
                <button onclick="runAllTests()" 
                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    執行所有測試
                </button>
            </div>
        </div>
        
        <!-- 搜尋功能示範 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">交易對象搜尋示範</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 共同交易對象搜尋 -->
                <div>
                    <h3 class="text-lg font-medium mb-3">共同交易對象搜尋</h3>
                    <div class="relative entity-search-container">
                        <input type="text" id="demoCommonSearch" class="w-full p-3 border rounded-lg" 
                               placeholder="搜尋交易對象...">
                        <div class="entity-search-dropdown absolute z-10 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-48 overflow-y-auto">
                            <!-- 搜尋結果將在此顯示 -->
                        </div>
                        <input type="hidden" id="demoCommonEntityId">
                        <input type="hidden" id="demoCommonEntityType">
                    </div>
                    <div class="mt-2 text-sm text-gray-600">
                        <div>選擇的交易對象 ID: <span id="selectedCommonId" class="font-mono">未選擇</span></div>
                        <div>交易對象類型: <span id="selectedCommonType" class="font-mono">未選擇</span></div>
                    </div>
                </div>
                
                <!-- 個別交易對象搜尋 -->
                <div>
                    <h3 class="text-lg font-medium mb-3">個別交易對象搜尋</h3>
                    <div class="relative entity-search-container">
                        <input type="text" id="demoIndividualSearch" class="w-full p-3 border rounded-lg entity-search-input" 
                               placeholder="搜尋交易對象..." data-row="demo">
                        <div class="entity-search-dropdown absolute z-10 w-full bg-white border rounded shadow-lg mt-1 hidden max-h-48 overflow-y-auto">
                            <!-- 搜尋結果將在此顯示 -->
                        </div>
                        <input type="hidden" id="demoIndividualEntityId">
                        <input type="hidden" id="demoIndividualEntityType">
                    </div>
                    <div class="mt-2 text-sm text-gray-600">
                        <div>選擇的交易對象 ID: <span id="selectedIndividualId" class="font-mono">未選擇</span></div>
                        <div>交易對象類型: <span id="selectedIndividualType" class="font-mono">未選擇</span></div>
                    </div>
                </div>
            </div>
            
            <!-- 搜尋提示 -->
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-medium text-blue-800 mb-2">搜尋功能說明</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• 輸入 1 個字元即開始搜尋</li>
                    <li>• 支援員工和交易對象搜尋</li>
                    <li>• 支援名稱和代碼搜尋</li>
                    <li>• 使用方向鍵和 Enter 鍵導航</li>
                    <li>• 按 Escape 鍵關閉下拉選單</li>
                    <li>• 聚焦時顯示最近使用的交易對象</li>
                </ul>
            </div>
        </div>
        
        <!-- 測試結果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">測試結果</h2>
            <div id="testResults" class="space-y-2 max-h-96 overflow-y-auto">
                <p class="text-gray-600">點擊上方按鈕開始測試...</p>
            </div>
        </div>
    </div>

    <!-- 載入必要的腳本 -->
    <script src="../../common/firebaseAPI/auth.js"></script>
    <script src="../../common/db/db.js"></script>
    <script src="../../common/db/preload.js"></script>
    <script src="../../common/utils/CommonUtils.js"></script>

    <script>
        // 模擬資料
        const mockEmployees = [
            { id: 'emp1', name: '張三', code: 'E001' },
            { id: 'emp2', name: '李四', code: 'E002' },
            { id: 'emp3', name: '王五', code: 'E003' }
        ];
        
        const mockEntities = [
            { id: 'ent1', name: '台積電', code: 'C001', type: 'company' },
            { id: 'ent2', name: '鴻海科技', code: 'C002', type: 'company' },
            { id: 'ent3', name: '聯發科', code: 'C003', type: 'company' }
        ];
        
        // 模擬 API 函式
        window.getEmployeesAll = async () => mockEmployees;
        window.getEntitiesAll = async () => mockEntities;
        window.getEntityTypeName = (type) => {
            const typeNames = {
                'company': '公司',
                'individual': '個人',
                'government': '政府機關'
            };
            return typeNames[type] || '其他';
        };
        
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            
            let bgColor = 'bg-blue-50 border-blue-200 text-blue-800';
            let icon = 'fa-info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-50 border-green-200 text-green-800';
                icon = 'fa-check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-50 border-red-200 text-red-800';
                icon = 'fa-times-circle';
            } else if (type === 'warning') {
                bgColor = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                icon = 'fa-exclamation-triangle';
            }
            
            resultItem.className = `p-3 border rounded ${bgColor}`;
            resultItem.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icon} mr-2"></i>
                    <span class="text-sm">${message}</span>
                    <span class="ml-auto text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            
            resultsDiv.appendChild(resultItem);
            resultItem.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-600">測試結果已清除</p>';
        }
        
        function testSearchFunctionality() {
            addTestResult('開始搜尋功能測試...', 'info');
            
            // 測試搜尋輸入
            const searchInput = document.getElementById('demoCommonSearch');
            if (searchInput) {
                addTestResult('✓ 找到搜尋輸入框', 'success');
                
                // 模擬輸入
                searchInput.value = '台';
                searchInput.dispatchEvent(new Event('input'));
                addTestResult('✓ 模擬輸入「台」進行搜尋', 'success');
                
                setTimeout(() => {
                    const dropdown = searchInput.parentElement.querySelector('.entity-search-dropdown');
                    if (dropdown && !dropdown.classList.contains('hidden')) {
                        addTestResult('✓ 搜尋下拉選單正確顯示', 'success');
                    } else {
                        addTestResult('✗ 搜尋下拉選單未顯示', 'error');
                    }
                }, 500);
                
            } else {
                addTestResult('✗ 找不到搜尋輸入框', 'error');
            }
            
            addTestResult('搜尋功能測試完成', 'info');
        }
        
        function testKeyboardNavigation() {
            addTestResult('開始鍵盤導航測試...', 'info');
            
            const searchInput = document.getElementById('demoCommonSearch');
            if (searchInput) {
                // 模擬鍵盤事件
                const events = [
                    { key: 'ArrowDown', description: '向下箭頭' },
                    { key: 'ArrowUp', description: '向上箭頭' },
                    { key: 'Enter', description: 'Enter 鍵' },
                    { key: 'Escape', description: 'Escape 鍵' }
                ];
                
                events.forEach(event => {
                    const keyEvent = new KeyboardEvent('keydown', { key: event.key });
                    searchInput.dispatchEvent(keyEvent);
                    addTestResult(`✓ 測試 ${event.description} 事件`, 'success');
                });
                
            } else {
                addTestResult('✗ 找不到搜尋輸入框', 'error');
            }
            
            addTestResult('鍵盤導航測試完成', 'info');
        }
        
        function testEntitySelection() {
            addTestResult('開始選擇功能測試...', 'info');
            
            // 模擬選擇交易對象
            const testEntity = mockEntities[0];
            
            // 測試共同交易對象選擇
            const commonSearch = document.getElementById('demoCommonSearch');
            const commonIdInput = document.getElementById('demoCommonEntityId');
            const commonTypeInput = document.getElementById('demoCommonEntityType');
            
            if (commonSearch && commonIdInput && commonTypeInput) {
                commonSearch.value = testEntity.name;
                commonIdInput.value = testEntity.id;
                commonTypeInput.value = testEntity.type;
                
                document.getElementById('selectedCommonId').textContent = testEntity.id;
                document.getElementById('selectedCommonType').textContent = testEntity.type;
                
                addTestResult(`✓ 成功選擇共同交易對象：${testEntity.name}`, 'success');
            } else {
                addTestResult('✗ 共同交易對象選擇測試失敗', 'error');
            }
            
            addTestResult('選擇功能測試完成', 'info');
        }
        
        async function runAllTests() {
            addTestResult('開始執行所有測試...', 'info');
            
            testSearchFunctionality();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testKeyboardNavigation();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testEntitySelection();
            
            addTestResult('所有測試執行完成！', 'success');
        }
        
        function openBatchPage() {
            addTestResult('開啟批次交易頁面...', 'info');
            window.open('transactions-batch-create.html', '_blank');
        }
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('交易對象搜尋測試頁面載入完成', 'success');
            addTestResult('可以開始進行功能測試', 'info');
            
            // 設置示範搜尋功能（簡化版本）
            setupDemoSearch();
        });
        
        function setupDemoSearch() {
            // 為示範搜尋框添加基本功能
            const demoInputs = ['demoCommonSearch', 'demoIndividualSearch'];
            
            demoInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('input', function() {
                        const query = this.value.toLowerCase();
                        const dropdown = this.parentElement.querySelector('.entity-search-dropdown');
                        
                        if (query.length >= 1) {
                            // 模擬搜尋結果
                            const matchedEmployees = mockEmployees.filter(emp => 
                                emp.name.toLowerCase().includes(query)
                            );
                            const matchedEntities = mockEntities.filter(ent => 
                                ent.name.toLowerCase().includes(query)
                            );
                            
                            displayDemoResults(dropdown, matchedEmployees, matchedEntities, inputId);
                            dropdown.classList.remove('hidden');
                        } else {
                            dropdown.classList.add('hidden');
                        }
                    });
                    
                    input.addEventListener('focus', function() {
                        if (!this.value) {
                            const dropdown = this.parentElement.querySelector('.entity-search-dropdown');
                            displayDemoResults(dropdown, mockEmployees.slice(0, 2), mockEntities.slice(0, 2), inputId, true);
                            dropdown.classList.remove('hidden');
                        }
                    });
                    
                    input.addEventListener('blur', function() {
                        setTimeout(() => {
                            this.parentElement.querySelector('.entity-search-dropdown').classList.add('hidden');
                        }, 200);
                    });
                }
            });
        }
        
        function displayDemoResults(dropdown, employees, entities, inputId, isRecent = false) {
            let html = '';
            
            if (isRecent) {
                html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">最近使用</div>';
            }
            
            if (employees.length > 0) {
                if (!isRecent) {
                    html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">員工</div>';
                }
                employees.forEach(emp => {
                    html += `
                        <div class="entity-search-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100" 
                             onclick="selectDemoEntity('${inputId}', '${emp.id}', 'employee', '${emp.name}')">
                            <div class="font-medium text-sm">${emp.name}</div>
                            <div class="text-xs text-gray-500">${emp.code}</div>
                        </div>
                    `;
                });
            }
            
            if (entities.length > 0) {
                if (!isRecent && employees.length > 0) {
                    html += '<div class="px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700">交易對象</div>';
                }
                entities.forEach(ent => {
                    html += `
                        <div class="entity-search-item p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100" 
                             onclick="selectDemoEntity('${inputId}', '${ent.id}', 'entity', '${ent.name}')">
                            <div class="font-medium text-sm">${ent.name}</div>
                            <div class="text-xs text-gray-500">${ent.code}</div>
                        </div>
                    `;
                });
            }
            
            if (employees.length === 0 && entities.length === 0) {
                html = '<div class="p-2 text-gray-500 text-sm">找不到相符的交易對象</div>';
            }
            
            dropdown.innerHTML = html;
        }
        
        function selectDemoEntity(inputId, entityId, entityType, entityName) {
            const input = document.getElementById(inputId);
            const dropdown = input.parentElement.querySelector('.entity-search-dropdown');
            
            input.value = entityName;
            dropdown.classList.add('hidden');
            
            // 更新顯示
            if (inputId === 'demoCommonSearch') {
                document.getElementById('selectedCommonId').textContent = entityId;
                document.getElementById('selectedCommonType').textContent = entityType;
                document.getElementById('demoCommonEntityId').value = entityId;
                document.getElementById('demoCommonEntityType').value = entityType;
            } else {
                document.getElementById('selectedIndividualId').textContent = entityId;
                document.getElementById('selectedIndividualType').textContent = entityType;
                document.getElementById('demoIndividualEntityId').value = entityId;
                document.getElementById('demoIndividualEntityType').value = entityType;
            }
            
            addTestResult(`選擇了交易對象：${entityName} (${entityType})`, 'success');
        }
    </script>
</body>
</html>
