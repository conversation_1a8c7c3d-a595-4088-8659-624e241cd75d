/**
 * @file batchTransaction_Controller.js
 * @description 批次交易主控制器
 * 負責協調各個模組，處理頁面初始化和主要業務邏輯
 */

// 全域變數
let batchDataManager; // 資料管理器
let batchValidator; // 驗證器
let currentRowIndex = 0; // 當前行索引

/**
 * DOM 內容載入完成後執行的初始化函式
 */
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('批次交易頁面初始化開始...');
        
        // 初始化資料管理器
        batchDataManager = new BatchDataManager();
        
        // 初始化驗證器
        batchValidator = new BatchValidator();
        
        // 載入基礎資料選項
        await loadBatchOptions();
        
        // 初始化 UI
        initializeBatchUI();
        
        // 載入事件監聽器
        loadBatchEventListeners();
        
        // 添加第一行
        addBatchRow();
        
        console.log('批次交易頁面初始化完成');
        
    } catch (error) {
        console.error('批次交易頁面初始化失敗:', error);
        alert('頁面初始化失敗，請重新整理頁面');
    }
});

/**
 * 載入基礎資料選項
 */
async function loadBatchOptions() {
    try {
        // 載入帳戶選項
        const accounts = await getAccountsAll();
        const accountSelect = document.getElementById('batchAccountSelect');
        accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = account.name;
            accountSelect.appendChild(option);
        });
        
        // 載入稅別選項
        const taxTypes = await getTaxTypesRatesAll();
        const taxTypeSelect = document.getElementById('batchTaxTypeSelect');
        taxTypes.forEach(taxType => {
            const option = document.createElement('option');
            option.value = taxType.id;
            option.dataset.rate = taxType.rate;
            option.textContent = taxType.name;
            taxTypeSelect.appendChild(option);
        });
        
        // 設定預設值
        setBatchDefaults();
        
        console.log('基礎資料選項載入完成');
        
    } catch (error) {
        console.error('載入基礎資料選項失敗:', error);
        throw error;
    }
}

/**
 * 設定批次欄位預設值
 */
function setBatchDefaults() {
    // 設定預設稅別為營業稅（通常是第二個選項）
    const taxTypeSelect = document.getElementById('batchTaxTypeSelect');
    if (taxTypeSelect.options.length > 1) {
        taxTypeSelect.selectedIndex = 1;
    }
    
    // 設定預設到帳情形
    document.getElementById('batchPaymentStatusSelect').value = 'same_day';
    
    // 設定預設狀態
    document.getElementById('batchStatusSelect').value = 'completed';
}

/**
 * 初始化批次 UI
 */
function initializeBatchUI() {
    // 初始化表格
    updateRowCount();
    
    // 設定表格容器的最大高度
    const tableContainer = document.querySelector('.overflow-x-auto');
    tableContainer.style.maxHeight = '400px';
    tableContainer.style.overflowY = 'auto';
}

/**
 * 載入事件監聽器
 */
function loadBatchEventListeners() {
    // 監聽鍵盤事件
    document.addEventListener('keydown', handleKeyboardEvents);
    
    // 監聽點擊事件（用於隱藏下拉選單）
    document.addEventListener('click', handleDocumentClick);
}

/**
 * 處理鍵盤事件
 */
function handleKeyboardEvents(event) {
    // Tab 鍵切換欄位
    if (event.key === 'Tab') {
        // 讓瀏覽器處理預設的 Tab 行為
        return;
    }
    
    // Enter 鍵新增行（在表格輸入欄位中）
    if (event.key === 'Enter' && event.target.classList.contains('batch-input')) {
        event.preventDefault();
        
        // 檢查是否在最後一行
        const currentRow = event.target.closest('tr');
        const tbody = document.getElementById('batchTableBody');
        const rows = tbody.querySelectorAll('tr');
        
        if (currentRow === rows[rows.length - 1]) {
            addBatchRow();
            
            // 聚焦到新行的第一個輸入欄位
            setTimeout(() => {
                const newRow = tbody.lastElementChild;
                const firstInput = newRow.querySelector('.batch-input');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 100);
        }
    }
}

/**
 * 處理文件點擊事件
 */
function handleDocumentClick(event) {
    // 隱藏所有開啟的下拉選單
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    dropdowns.forEach(dropdown => {
        if (!dropdown.contains(event.target) && !event.target.closest('.dropdown-trigger')) {
            dropdown.classList.add('hidden');
        }
    });
}

/**
 * 處理批次交易類型變更
 */
function handleBatchTransactionTypeChange() {
    const transactionType = document.querySelector('input[name="batchTransactionType"]:checked').value;
    
    // 更新所有行的交易項目選單
    updateAllRowsPaymentDescription(transactionType);
    
    console.log('批次交易類型變更為:', transactionType);
}

/**
 * 處理批次到帳情形變更
 */
function handleBatchPaymentStatusChange() {
    const paymentStatus = document.getElementById('batchPaymentStatusSelect').value;
    
    // 這裡可以根據到帳情形調整其他欄位的顯示
    // 例如：應收付款時隱藏某些欄位等
    
    console.log('批次到帳情形變更為:', paymentStatus);
}

/**
 * 處理批次欄位變更
 */
function handleBatchFieldChange() {
    // 當批次設定變更時，可以在這裡處理相關邏輯
    console.log('批次欄位已變更');
}

/**
 * 新增批次資料行
 */
function addBatchRow() {
    const tbody = document.getElementById('batchTableBody');
    const rowIndex = currentRowIndex++;
    
    const row = createBatchRow(rowIndex);
    tbody.appendChild(row);
    
    // 更新行數統計
    updateRowCount();
    
    // 初始化該行的功能
    initializeRowFeatures(rowIndex);
    
    return rowIndex;
}

/**
 * 移除批次資料行
 */
function removeBatchRow(rowIndex) {
    const row = document.getElementById(`batchRow_${rowIndex}`);
    if (row) {
        row.remove();
        
        // 從資料管理器中移除
        batchDataManager.removeRow(rowIndex);
        
        // 更新行號
        updateRowNumbers();
        
        // 更新行數統計
        updateRowCount();
    }
}

/**
 * 清空所有行
 */
function clearAllRows() {
    if (confirm('確定要清空所有交易資料嗎？')) {
        const tbody = document.getElementById('batchTableBody');
        tbody.innerHTML = '';
        
        // 清空資料管理器
        batchDataManager.clearAll();
        
        // 重置索引
        currentRowIndex = 0;
        
        // 更新行數統計
        updateRowCount();
        
        // 添加一行新的空白行
        addBatchRow();
    }
}

/**
 * 更新行號顯示
 */
function updateRowNumbers() {
    const rows = document.querySelectorAll('#batchTableBody tr');
    rows.forEach((row, index) => {
        const indexCell = row.querySelector('td:first-child');
        if (indexCell) {
            indexCell.textContent = index + 1;
        }
    });
}

/**
 * 更新行數統計
 */
function updateRowCount() {
    const count = document.querySelectorAll('#batchTableBody tr').length;
    document.getElementById('totalRowsCount').textContent = count;
}

/**
 * 驗證所有交易資料
 */
async function validateAllTransactions() {
    try {
        const results = await batchValidator.validateAll();
        displayValidationResults(results);
        return results;
    } catch (error) {
        console.error('驗證失敗:', error);
        alert('資料驗證時發生錯誤');
        return [];
    }
}

/**
 * 提交批次交易
 */
async function submitBatchTransactions() {
    try {
        // 先進行驗證
        const validationResults = await validateAllTransactions();
        const validTransactions = validationResults.filter(r => r.isValid);
        const invalidCount = validationResults.length - validTransactions.length;
        
        if (invalidCount > 0) {
            if (!confirm(`有 ${invalidCount} 筆資料驗證失敗。\n是否只儲存 ${validTransactions.length} 筆有效資料？`)) {
                return;
            }
        }
        
        if (validTransactions.length === 0) {
            alert('沒有有效的交易資料可以儲存！');
            return;
        }
        
        // 執行批次儲存
        const results = await batchDataManager.saveAll(validTransactions);
        displayBatchResults(results);
        
    } catch (error) {
        console.error('批次提交失敗:', error);
        alert('批次提交過程中發生錯誤: ' + error.message);
    }
}

/**
 * 重設批次表單
 */
function resetBatchForm() {
    if (confirm('確定要重設表單嗎？所有資料將會清空。')) {
        // 清空表格
        clearAllRows();
        
        // 重設批次設定
        document.querySelector('input[name="batchTransactionType"][value="expense"]').checked = true;
        document.getElementById('batchAccountSelect').selectedIndex = 0;
        document.getElementById('batchPaymentStatusSelect').value = 'same_day';
        document.getElementById('batchTaxTypeSelect').selectedIndex = 1;
        document.getElementById('batchStatusSelect').value = 'completed';
        
        // 隱藏結果區域
        document.getElementById('resultSection').classList.add('hidden');
    }
}

/**
 * 處理取消按鈕
 */
function handleCancel() {
    if (confirm('確定要離開批次交易輸入頁面嗎？未儲存的資料將會遺失。')) {
        window.history.back();
    }
}

// 匯出全域函式供 HTML 使用
window.handleBatchTransactionTypeChange = handleBatchTransactionTypeChange;
window.handleBatchPaymentStatusChange = handleBatchPaymentStatusChange;
window.handleBatchFieldChange = handleBatchFieldChange;
window.addBatchRow = addBatchRow;
window.removeBatchRow = removeBatchRow;
window.clearAllRows = clearAllRows;
window.validateAllTransactions = validateAllTransactions;
window.submitBatchTransactions = submitBatchTransactions;
window.resetBatchForm = resetBatchForm;
window.handleCancel = handleCancel;
