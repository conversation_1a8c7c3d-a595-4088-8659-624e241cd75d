/**
 * @file transactionsBatch_Controller.js
 * @description 批次交易輸入控制器
 * 負責協調批次交易功能的各個模組，處理頁面初始化、事件監聽和主要業務邏輯
 */

// 全域變數
let batchTransactionData = []; // 儲存批次交易資料
let commonFields = {}; // 儲存共同欄位設定
let currentRowIndex = 0; // 當前行索引

/**
 * DOM 內容載入完成後執行的初始化函式
 */
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('批次交易頁面初始化開始...');
        
        // 載入基礎資料選項
        await loadBatchOptions();
        
        // 初始化共同欄位設定
        initializeCommonFields();
        
        // 載入事件監聽器
        loadBatchEventListeners();
        
        // 初始化表格
        initializeBatchTable();
        
        // 添加第一行
        addBatchRow();
        
        console.log('批次交易頁面初始化完成');
        
    } catch (error) {
        console.error('批次交易頁面初始化失敗:', error);
        alert('頁面初始化失敗，請重新整理頁面');
    }
});

/**
 * 載入基礎資料選項
 */
async function loadBatchOptions() {
    try {
        // 載入帳戶選項
        const accounts = await getAccountsAll();
        const accountSelect = document.getElementById('common_accountSelect');
        accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = account.name;
            accountSelect.appendChild(option);
        });
        
        // 載入稅別選項
        const taxTypes = await getTaxTypesRatesAll();
        const taxTypeSelect = document.getElementById('common_taxTypeSelect');
        taxTypes.forEach(taxType => {
            const option = document.createElement('option');
            option.dataset.rate = taxType.rate;
            option.value = taxType.id;
            option.textContent = taxType.name;
            taxTypeSelect.appendChild(option);
        });
        
        console.log('基礎資料選項載入完成');
        
    } catch (error) {
        console.error('載入基礎資料選項失敗:', error);
        throw error;
    }
}

/**
 * 初始化共同欄位設定
 */
function initializeCommonFields() {
    // 設定預設日期
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('common_paymentDate').value = today;
    document.getElementById('common_invoiceDate').value = today;
    
    // 設定預設稅別為營業稅（索引1）
    const taxTypeSelect = document.getElementById('common_taxTypeSelect');
    if (taxTypeSelect.options.length > 1) {
        taxTypeSelect.selectedIndex = 1;
    }
    
    // 設定預設帳款到帳情形
    document.getElementById('common_paymentStatusSelect').value = 'same_day';
    
    // 設定預設狀態
    document.getElementById('common_transactionStatusSelect').value = 'completed';
    
    // 初始化手續費控制
    const feeToggle = document.getElementById('common_feeToggle');
    const feeInput = document.getElementById('common_fee');
    
    feeToggle.addEventListener('change', function() {
        feeInput.disabled = !this.checked;
        if (!this.checked) {
            feeInput.value = '';
        }
    });
}

/**
 * 載入事件監聽器
 */
function loadBatchEventListeners() {
    // 共同欄位變更監聽
    document.addEventListener('change', function(e) {
        if (e.target.id && e.target.id.startsWith('common_')) {
            updateCommonFieldValue(e.target);
        }
    });
    
    // 交易類型變更監聽
    document.addEventListener('change', function(e) {
        if (e.target.name === 'common_transactionType') {
            handleTransactionTypeChange(e.target.value);
        }
    });
}

/**
 * 初始化批次表格
 */
function initializeBatchTable() {
    updateTableHeaders();
}

/**
 * 切換共同欄位顯示/隱藏
 */
function toggleCommonFieldsSection() {
    const section = document.getElementById('commonFieldsSection');
    const icon = document.getElementById('toggleIcon');
    
    if (section.classList.contains('hidden')) {
        section.classList.remove('hidden');
        icon.className = 'fas fa-chevron-up';
    } else {
        section.classList.add('hidden');
        icon.className = 'fas fa-chevron-down';
    }
}

/**
 * 切換共同欄位
 */
function toggleCommonField(fieldName) {
    const checkbox = document.getElementById(`commonField_${fieldName}`);
    const formSection = document.getElementById(`commonForm_${fieldName}`);
    
    if (checkbox.checked) {
        formSection.classList.remove('hidden');
        commonFields[fieldName] = true;
    } else {
        formSection.classList.add('hidden');
        delete commonFields[fieldName];
    }
    
    // 更新表格標題
    updateTableHeaders();
    
    // 更新所有現有行
    updateAllBatchRows();
}

/**
 * 更新共同欄位值
 */
function updateCommonFieldValue(element) {
    const fieldName = element.id.replace('common_', '');
    
    // 更新所有批次資料行中對應的欄位
    updateAllBatchRows();
}

/**
 * 處理交易類型變更
 */
async function handleTransactionTypeChange(transactionType) {
    // 這裡可以根據交易類型載入對應的交易項目選單
    // 暫時保留，後續實作交易項目選單時使用
    console.log('交易類型變更為:', transactionType);
}

/**
 * 處理取消按鈕
 */
function handleCancel() {
    if (confirm('確定要離開批次交易輸入頁面嗎？未儲存的資料將會遺失。')) {
        window.history.back();
    }
}

/**
 * 更新總行數顯示
 */
function updateRowCount() {
    const count = document.querySelectorAll('#batchTableBody tr').length;
    document.getElementById('totalRowsCount').textContent = count;
}

/**
 * 處理交易對象搜尋
 */
async function handleEntitySearch(searchTerm, rowIndex) {
    if (!searchTerm || searchTerm.length < 2) {
        hideEntitySearchResults(rowIndex);
        return;
    }

    try {
        // 搜尋員工
        const employees = await getEmployeesAll();
        const matchedEmployees = employees.filter(emp =>
            emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            emp.code.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 5);

        // 搜尋交易對象
        const entities = await getEntitiesAll();
        const matchedEntities = entities.filter(entity =>
            entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entity.code.toLowerCase().includes(searchTerm.toLowerCase())
        ).slice(0, 5);

        // 顯示搜尋結果
        showEntitySearchResults(rowIndex, matchedEmployees, matchedEntities);

    } catch (error) {
        console.error('搜尋交易對象失敗:', error);
    }
}

/**
 * 顯示交易對象搜尋結果
 */
function showEntitySearchResults(rowIndex, employees, entities) {
    const resultsDiv = document.getElementById(`batch_entity_${rowIndex}_results`);
    if (!resultsDiv) return;

    resultsDiv.innerHTML = '';

    // 添加員工結果
    if (employees.length > 0) {
        const employeeHeader = document.createElement('div');
        employeeHeader.className = 'px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700';
        employeeHeader.textContent = '員工';
        resultsDiv.appendChild(employeeHeader);

        employees.forEach(employee => {
            const item = document.createElement('div');
            item.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer text-xs';
            item.textContent = `${employee.name} (${employee.code})`;
            item.onclick = () => selectEntity(rowIndex, employee.id, 'employee', employee.name);
            resultsDiv.appendChild(item);
        });
    }

    // 添加交易對象結果
    if (entities.length > 0) {
        const entityHeader = document.createElement('div');
        entityHeader.className = 'px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700';
        entityHeader.textContent = '交易對象';
        resultsDiv.appendChild(entityHeader);

        entities.forEach(entity => {
            const item = document.createElement('div');
            item.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer text-xs';
            item.textContent = `${entity.name} (${entity.code})`;
            item.onclick = () => selectEntity(rowIndex, entity.id, 'entity', entity.name);
            resultsDiv.appendChild(item);
        });
    }

    if (employees.length === 0 && entities.length === 0) {
        const noResult = document.createElement('div');
        noResult.className = 'px-3 py-2 text-xs text-gray-500';
        noResult.textContent = '找不到相符的結果';
        resultsDiv.appendChild(noResult);
    }

    resultsDiv.classList.remove('hidden');
}

/**
 * 隱藏交易對象搜尋結果
 */
function hideEntitySearchResults(rowIndex) {
    const resultsDiv = document.getElementById(`batch_entity_${rowIndex}_results`);
    if (resultsDiv) {
        resultsDiv.classList.add('hidden');
    }
}

/**
 * 選擇交易對象
 */
function selectEntity(rowIndex, entityId, entityType, entityName) {
    const input = document.getElementById(`batch_entity_${rowIndex}`);
    const idInput = document.getElementById(`batch_entity_${rowIndex}_id`);
    const typeInput = document.getElementById(`batch_entity_${rowIndex}_type`);

    if (input && idInput && typeInput) {
        input.value = entityName;
        idInput.value = entityId;
        typeInput.value = entityType;
    }

    hideEntitySearchResults(rowIndex);
}

/**
 * 切換交易項目下拉選單
 */
async function togglePaymentDescriptionDropdown(rowIndex) {
    const dropdown = document.getElementById(`batch_paymentDescription_${rowIndex}_dropdown`);
    if (!dropdown) return;

    if (dropdown.classList.contains('hidden')) {
        // 載入交易項目選項
        await loadPaymentDescriptionOptions(rowIndex);
        dropdown.classList.remove('hidden');
    } else {
        dropdown.classList.add('hidden');
    }
}

/**
 * 載入交易項目選項
 */
async function loadPaymentDescriptionOptions(rowIndex) {
    try {
        const dropdown = document.getElementById(`batch_paymentDescription_${rowIndex}_dropdown`);
        if (!dropdown) return;

        // 獲取當前交易類型
        const transactionTypeRadio = document.querySelector(`input[name="batch_transactionType_${rowIndex}"]:checked`);
        const transactionType = transactionTypeRadio ? transactionTypeRadio.value : 'expense';

        // 載入交易分類和項目
        const categories = await getTransactionCategoriesAll();
        const items = await getTransactionCategoryItemsAll();

        dropdown.innerHTML = '';

        // 根據交易類型篩選分類
        const filteredCategories = categories.filter(cat => cat.type === transactionType);

        filteredCategories.forEach(category => {
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'px-3 py-1 bg-gray-100 text-xs font-medium text-gray-700';
            categoryHeader.textContent = category.name;
            dropdown.appendChild(categoryHeader);

            // 添加該分類下的項目
            const categoryItems = items.filter(item => item.categoryId === category.id);
            categoryItems.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer text-xs';
                itemDiv.textContent = `${item.accountingName} (${item.accountingCode})`;
                itemDiv.onclick = () => selectPaymentDescription(rowIndex, item.accountingCode, item.accountingName);
                dropdown.appendChild(itemDiv);
            });
        });

    } catch (error) {
        console.error('載入交易項目選項失敗:', error);
    }
}

/**
 * 選擇交易項目
 */
function selectPaymentDescription(rowIndex, code, name) {
    const button = document.getElementById(`batch_paymentDescription_${rowIndex}_btn`);
    const hiddenInput = document.getElementById(`batch_paymentDescription_${rowIndex}`);
    const dropdown = document.getElementById(`batch_paymentDescription_${rowIndex}_dropdown`);

    if (button && hiddenInput) {
        button.querySelector('span').textContent = name;
        hiddenInput.value = code;
    }

    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

// 匯出全域函式供 HTML 使用
window.toggleCommonFieldsSection = toggleCommonFieldsSection;
window.toggleCommonField = toggleCommonField;
window.handleCancel = handleCancel;
window.handleEntitySearch = handleEntitySearch;
window.selectEntity = selectEntity;
window.togglePaymentDescriptionDropdown = togglePaymentDescriptionDropdown;
