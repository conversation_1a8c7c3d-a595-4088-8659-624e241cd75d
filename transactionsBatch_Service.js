/**
 * @file transactionsBatch_Service.js
 * @description 批次交易服務層
 * 負責處理批次交易的資料驗證、儲存和業務邏輯
 */

/**
 * 驗證所有批次交易資料
 */
async function validateAllTransactions() {
    const validationResults = [];
    const rows = document.querySelectorAll('#batchTableBody tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const rowIndex = row.dataset.rowIndex;
        const result = await validateSingleTransaction(parseInt(rowIndex), i + 1);
        validationResults.push(result);
    }
    
    // 顯示驗證結果
    displayValidationResults(validationResults);
    
    return validationResults;
}

/**
 * 驗證單筆交易資料
 */
async function validateSingleTransaction(rowIndex, displayIndex) {
    const errors = [];
    const warnings = [];
    
    try {
        // 收集該行的資料
        const transactionData = await collectRowData(rowIndex);
        
        // 必填欄位驗證
        if (!transactionData.transactionType) {
            errors.push('交易類型為必填');
        }
        
        if (!transactionData.accountId) {
            errors.push('主要帳戶為必填');
        }
        
        if (!transactionData.paymentStatus) {
            errors.push('帳款到帳情形為必填');
        }
        
        if (!transactionData.entityId) {
            errors.push('交易對象為必填');
        }
        
        if (!transactionData.paymentDescription) {
            errors.push('交易項目為必填');
        }
        
        if (!transactionData.amount || transactionData.amount <= 0) {
            errors.push('金額必須大於0');
        }
        
        if (!transactionData.taxTypeId) {
            errors.push('稅別為必填');
        }
        
        if (!transactionData.transactionStatus) {
            errors.push('狀態為必填');
        }
        
        // 日期驗證
        if (!transactionData.paymentDate) {
            errors.push('付款日期為必填');
        }
        
        if (!transactionData.invoiceDate) {
            errors.push('發票日期為必填');
        }
        
        // 業務邏輯驗證
        if (transactionData.paymentDate && transactionData.invoiceDate) {
            const paymentDate = new Date(transactionData.paymentDate);
            const invoiceDate = new Date(transactionData.invoiceDate);
            
            if (paymentDate < invoiceDate) {
                warnings.push('付款日期早於發票日期，請確認是否正確');
            }
        }
        
        // 稅額驗證
        if (transactionData.amount && transactionData.taxTypeId) {
            const expectedTaxAmount = await calculateExpectedTaxAmount(transactionData.amount, transactionData.taxTypeId);
            const actualTaxAmount = parseFloat(transactionData.taxAmount) || 0;
            
            if (Math.abs(expectedTaxAmount - actualTaxAmount) > 0.01) {
                warnings.push(`稅額可能不正確，預期: ${expectedTaxAmount.toFixed(2)}，實際: ${actualTaxAmount.toFixed(2)}`);
            }
        }
        
        return {
            rowIndex,
            displayIndex,
            isValid: errors.length === 0,
            errors,
            warnings,
            data: transactionData
        };
        
    } catch (error) {
        console.error(`驗證第 ${displayIndex} 行時發生錯誤:`, error);
        return {
            rowIndex,
            displayIndex,
            isValid: false,
            errors: ['資料驗證時發生系統錯誤'],
            warnings: [],
            data: null
        };
    }
}

/**
 * 收集單行資料
 */
async function collectRowData(rowIndex) {
    const data = createEmptyTransactionData();
    
    // 從共同欄位獲取資料
    Object.keys(commonFields).forEach(fieldKey => {
        const commonElement = document.getElementById(`common_${fieldKey}`);
        if (commonElement) {
            if (commonElement.type === 'radio') {
                const checkedRadio = document.querySelector(`input[name="common_${fieldKey}"]:checked`);
                if (checkedRadio) {
                    data[getDataFieldName(fieldKey)] = checkedRadio.value;
                }
            } else if (fieldKey === 'fee') {
                const feeToggle = document.getElementById('common_feeToggle');
                if (feeToggle && feeToggle.checked) {
                    data.fee = parseFloat(commonElement.value) || 0;
                }
            } else {
                data[getDataFieldName(fieldKey)] = commonElement.value;
            }
        }
    });
    
    // 從個別欄位獲取資料（覆蓋共同欄位）
    const allFields = [
        'transactionType', 'account', 'paymentStatus', 'paymentDate', 'invoiceDate',
        'entity', 'paymentDescription', 'amount', 'taxType', 'taxAmount',
        'invoiceNumber', 'fee', 'status', 'notes'
    ];
    
    allFields.forEach(fieldKey => {
        if (!commonFields[fieldKey]) { // 只處理非共同欄位
            const element = document.getElementById(`batch_${fieldKey}_${rowIndex}`);
            if (element) {
                if (fieldKey === 'transactionType') {
                    const checkedRadio = document.querySelector(`input[name="batch_${fieldKey}_${rowIndex}"]:checked`);
                    if (checkedRadio) {
                        data.transactionType = checkedRadio.value;
                    }
                } else if (fieldKey === 'entity') {
                    const idElement = document.getElementById(`batch_${fieldKey}_${rowIndex}_id`);
                    const typeElement = document.getElementById(`batch_${fieldKey}_${rowIndex}_type`);
                    if (idElement && typeElement) {
                        data.entityId = idElement.value;
                        data.entityType = typeElement.value;
                    }
                } else if (fieldKey === 'fee') {
                    const feeToggle = document.getElementById(`batch_${fieldKey}_${rowIndex}_toggle`);
                    if (feeToggle && feeToggle.checked) {
                        data.fee = parseFloat(element.value) || 0;
                    }
                } else {
                    const dataFieldName = getDataFieldName(fieldKey);
                    if (element.type === 'number') {
                        data[dataFieldName] = parseFloat(element.value) || 0;
                    } else {
                        data[dataFieldName] = element.value;
                    }
                }
            }
        }
    });
    
    return data;
}

/**
 * 將 UI 欄位名稱轉換為資料欄位名稱
 */
function getDataFieldName(fieldKey) {
    const mapping = {
        'account': 'accountId',
        'taxType': 'taxTypeId',
        'status': 'transactionStatus'
    };
    
    return mapping[fieldKey] || fieldKey;
}

/**
 * 計算預期稅額
 */
async function calculateExpectedTaxAmount(amount, taxTypeId) {
    try {
        const taxTypes = await getTaxTypesRatesAll();
        const taxType = taxTypes.find(t => t.id == taxTypeId);
        
        if (taxType && taxType.rate) {
            // 使用與原系統相同的稅額計算邏輯
            const amountNoTax = Math.round(amount / (1 + Number(taxType.rate)));
            return amount - amountNoTax;
        }
        
        return 0;
    } catch (error) {
        console.error('計算預期稅額失敗:', error);
        return 0;
    }
}

/**
 * 顯示驗證結果
 */
function displayValidationResults(results) {
    const validCount = results.filter(r => r.isValid).length;
    const invalidCount = results.length - validCount;
    
    // 更新表格行的樣式
    results.forEach(result => {
        const row = document.getElementById(`batchRow_${result.rowIndex}`);
        if (row) {
            row.classList.remove('error-row', 'success-row');
            if (result.isValid) {
                row.classList.add('success-row');
            } else {
                row.classList.add('error-row');
            }
            
            // 添加錯誤提示
            if (result.errors.length > 0) {
                row.title = '錯誤: ' + result.errors.join(', ');
            } else if (result.warnings.length > 0) {
                row.title = '警告: ' + result.warnings.join(', ');
            } else {
                row.title = '';
            }
        }
    });
    
    // 顯示總結訊息
    if (invalidCount > 0) {
        alert(`驗證完成！\n有效資料: ${validCount} 筆\n無效資料: ${invalidCount} 筆\n\n請檢查標示為紅色的行並修正錯誤。`);
    } else {
        alert(`驗證完成！所有 ${validCount} 筆資料都有效，可以進行批次儲存。`);
    }
}

/**
 * 批次提交交易
 */
async function submitBatchTransactions() {
    try {
        // 先進行驗證
        const validationResults = await validateAllTransactions();
        const validTransactions = validationResults.filter(r => r.isValid);
        const invalidCount = validationResults.length - validTransactions.length;
        
        if (invalidCount > 0) {
            if (!confirm(`有 ${invalidCount} 筆資料驗證失敗。\n是否只儲存 ${validTransactions.length} 筆有效資料？`)) {
                return;
            }
        }
        
        if (validTransactions.length === 0) {
            alert('沒有有效的交易資料可以儲存！');
            return;
        }
        
        // 顯示處理進度
        showProcessingProgress(validTransactions.length);
        
        const results = [];
        
        // 逐筆處理交易
        for (let i = 0; i < validTransactions.length; i++) {
            const validation = validTransactions[i];
            
            try {
                updateProcessingProgress(i + 1, validTransactions.length, `處理第 ${validation.displayIndex} 筆交易...`);
                
                // 儲存交易
                const savedTransactionId = await saveSingleBatchTransaction(validation.data);
                
                results.push({
                    displayIndex: validation.displayIndex,
                    success: true,
                    transactionId: savedTransactionId,
                    message: '儲存成功'
                });
                
            } catch (error) {
                console.error(`儲存第 ${validation.displayIndex} 筆交易失敗:`, error);
                results.push({
                    displayIndex: validation.displayIndex,
                    success: false,
                    error: error.message || '儲存失敗',
                    message: '儲存失敗: ' + (error.message || '未知錯誤')
                });
            }
        }
        
        // 隱藏進度並顯示結果
        hideProcessingProgress();
        displayBatchResults(results);
        
    } catch (error) {
        console.error('批次提交失敗:', error);
        hideProcessingProgress();
        alert('批次提交過程中發生錯誤: ' + error.message);
    }
}

/**
 * 儲存單筆批次交易
 */
async function saveSingleBatchTransaction(transactionData) {
    try {
        // 準備交易資料
        const formData = {
            ...transactionData,
            createdAt: new Date().toISOString()
        };

        // 儲存交易記錄
        const savedTransactionId = await saveTransactionToDB(formData);

        if (!savedTransactionId) {
            throw new Error('交易記錄儲存失敗');
        }

        // 儲存會計分錄
        await saveBatchJournal(savedTransactionId, formData);

        return savedTransactionId;

    } catch (error) {
        console.error('儲存單筆交易失敗:', error);
        throw error;
    }
}

/**
 * 儲存批次交易的會計分錄
 */
async function saveBatchJournal(transactionId, formData) {
    try {
        // 轉換為會計分錄所需的格式
        const journalFormData = await transformBatchDataToJournalData(formData);

        // 生成會計分錄
        const mjournal = new Journal(journalFormData);
        const journalList = mjournal.getJournal();

        // 儲存會計分錄到資料庫
        const saveResult = await saveJournalEntriesToDB(transactionId, journalList);

        if (!saveResult) {
            throw new Error('會計分錄儲存失敗');
        }

        return true;

    } catch (error) {
        console.error('儲存會計分錄失敗:', error);
        throw error;
    }
}

/**
 * 轉換批次資料為會計分錄格式
 */
async function transformBatchDataToJournalData(formData) {
    // 獲取帳戶資訊
    const account = await getAccountById(formData.accountId);
    const mainAccountName = account ? account.name : '未知帳戶';
    const mainAccountType = account ? account.type : 'unknown';

    // 獲取交易對象資訊
    let counterpartyName = '未指定';
    if (formData.entityId && formData.entityType) {
        try {
            if (formData.entityType === 'employee') {
                const employee = await getEmployeeById(formData.entityId);
                counterpartyName = employee ? employee.name : '未知員工';
            } else {
                const entity = await getEntityById(formData.entityId);
                counterpartyName = entity ? entity.name : '未知交易對象';
            }
        } catch (error) {
            console.warn('獲取交易對象資訊失敗:', error);
        }
    }

    // 獲取交易項目描述
    let description = '未選擇';
    if (formData.paymentDescription) {
        try {
            description = await getPaymentDescriptionName(formData.paymentDescription) || '未選擇';
        } catch (error) {
            console.warn('獲取交易項目描述失敗:', error);
        }
    }

    return {
        type: formData.transactionType,
        status: formData.paymentStatus,
        mainAccountName: mainAccountName,
        mainAccountType: mainAccountType,
        counterpartyName: counterpartyName,
        mainAmount: parseFloat(formData.amount),
        taxAmount: parseFloat(formData.taxAmount) || 0,
        feeAmount: parseFloat(formData.fee) || 0,
        totalAmount: formData.amount - (formData.taxAmount || 0),
        paymentDate: formData.paymentDate || formData.invoiceDate,
        invoiceDate: formData.invoiceDate || formData.paymentDate,
        description: description,
        descriptionCode: Number(formData.paymentDescription) || 'XXXX'
    };
}

/**
 * 顯示處理進度
 */
function showProcessingProgress(totalCount) {
    // 創建進度對話框
    const progressModal = document.createElement('div');
    progressModal.id = 'progressModal';
    progressModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

    progressModal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold mb-4">批次處理中...</h3>
            <div class="mb-4">
                <div class="bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
            <div id="progressText" class="text-sm text-gray-600">準備開始...</div>
            <div id="progressCount" class="text-xs text-gray-500 mt-2">0 / ${totalCount}</div>
        </div>
    `;

    document.body.appendChild(progressModal);
}

/**
 * 更新處理進度
 */
function updateProcessingProgress(current, total, message) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const progressCount = document.getElementById('progressCount');

    if (progressBar && progressText && progressCount) {
        const percentage = (current / total) * 100;
        progressBar.style.width = `${percentage}%`;
        progressText.textContent = message;
        progressCount.textContent = `${current} / ${total}`;
    }
}

/**
 * 隱藏處理進度
 */
function hideProcessingProgress() {
    const progressModal = document.getElementById('progressModal');
    if (progressModal) {
        progressModal.remove();
    }
}

/**
 * 顯示批次處理結果
 */
function displayBatchResults(results) {
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    // 更新結果統計
    document.getElementById('successCount').textContent = successCount;
    document.getElementById('failureCount').textContent = failureCount;
    document.getElementById('totalCount').textContent = results.length;

    // 顯示詳細結果
    const resultDetails = document.getElementById('resultDetails');
    resultDetails.innerHTML = '';

    results.forEach(result => {
        const resultItem = document.createElement('div');
        resultItem.className = `p-3 rounded border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`;

        resultItem.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas ${result.success ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500'} mr-2"></i>
                    <span class="font-medium">第 ${result.displayIndex} 筆交易</span>
                </div>
                <span class="text-sm ${result.success ? 'text-green-600' : 'text-red-600'}">${result.message}</span>
            </div>
            ${result.transactionId ? `<div class="text-xs text-gray-500 mt-1">交易ID: ${result.transactionId}</div>` : ''}
        `;

        resultDetails.appendChild(resultItem);
    });

    // 顯示結果區域
    document.getElementById('resultSection').classList.remove('hidden');

    // 滾動到結果區域
    document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
}

/**
 * 匯出處理結果
 */
function exportResults() {
    // 這裡可以實作匯出功能，例如生成 CSV 或 Excel 檔案
    alert('匯出功能開發中...');
}

/**
 * 重設批次表單
 */
function resetBatchForm() {
    if (confirm('確定要重設表單嗎？所有資料將會清空。')) {
        // 清空表格
        clearAllRows();

        // 重設共同欄位
        document.querySelectorAll('#commonFieldsForm input, #commonFieldsForm select').forEach(element => {
            if (element.type === 'checkbox') {
                element.checked = false;
            } else if (element.type === 'radio') {
                if (element.value === 'expense') {
                    element.checked = true;
                } else {
                    element.checked = false;
                }
            } else {
                element.value = '';
            }
        });

        // 重設共同欄位選擇
        document.querySelectorAll('.common-field-checkbox').forEach(checkbox => {
            checkbox.checked = false;
            const fieldName = checkbox.id.replace('commonField_', '');
            const formSection = document.getElementById(`commonForm_${fieldName}`);
            if (formSection) {
                formSection.classList.add('hidden');
            }
        });

        // 清空共同欄位設定
        commonFields = {};

        // 隱藏結果區域
        document.getElementById('resultSection').classList.add('hidden');

        // 重新初始化
        initializeCommonFields();
        updateTableHeaders();
    }
}

// 匯出函式供其他模組使用
window.validateAllTransactions = validateAllTransactions;
window.submitBatchTransactions = submitBatchTransactions;
window.exportResults = exportResults;
window.resetBatchForm = resetBatchForm;
