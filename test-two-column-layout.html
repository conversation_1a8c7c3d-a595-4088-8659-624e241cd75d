<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兩列佈局修正測試</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">📊 兩列佈局修正測試</h1>
        
        <!-- 修正說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-blue-600">🔧 修正說明</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">❌ 原來的錯誤邏輯</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 兩列顯示相同的輸入欄位</li>
                        <li>• 重複的欄位造成資料混亂</li>
                        <li>• 沒有真正減少表格寬度</li>
                        <li>• 使用者體驗不佳</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-3">✅ 修正後的正確邏輯</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 將欄位分成兩列，每個欄位只出現一次</li>
                        <li>• 第一列顯示前半部分欄位</li>
                        <li>• 第二列顯示後半部分欄位</li>
                        <li>• 有效減少表格寬度，提升可讀性</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 佈局示例 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">📋 佈局示例</h2>
            
            <!-- 模擬表格 -->
            <div class="overflow-x-auto">
                <table class="w-full border-collapse border border-gray-300">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="p-2 border text-center" rowspan="2">序號</th>
                            <th class="p-2 border text-center">金額 (含稅)</th>
                            <th class="p-2 border text-center">稅額</th>
                            <th class="p-2 border text-center">發票號碼</th>
                            <th class="p-2 border text-center" rowspan="2">操作</th>
                        </tr>
                        <tr class="bg-gray-50">
                            <th class="p-2 border text-center">手續費</th>
                            <th class="p-2 border text-center">標籤</th>
                            <th class="p-2 border text-center">備註</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b-0">
                            <td class="p-2 border text-center align-middle" rowspan="2">1</td>
                            <td class="p-2 border">
                                <input type="number" class="w-full p-1 border rounded" placeholder="1000" value="1000">
                            </td>
                            <td class="p-2 border">
                                <input type="number" class="w-full p-1 border rounded bg-gray-50" placeholder="50" value="50" readonly>
                            </td>
                            <td class="p-2 border">
                                <input type="text" class="w-full p-1 border rounded" placeholder="INV001">
                            </td>
                            <td class="p-2 border text-center align-middle" rowspan="2">
                                <button class="px-2 py-1 bg-red-500 text-white rounded text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <tr class="border-t-0">
                            <td class="p-2 border">
                                <input type="number" class="w-full p-1 border rounded" placeholder="10">
                            </td>
                            <td class="p-2 border">
                                <div class="flex flex-wrap gap-1">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">辦公用品</span>
                                </div>
                            </td>
                            <td class="p-2 border">
                                <textarea class="w-full p-1 border rounded" rows="1" placeholder="備註"></textarea>
                            </td>
                        </tr>
                        
                        <tr class="border-b-0">
                            <td class="p-2 border text-center align-middle" rowspan="2">2</td>
                            <td class="p-2 border">
                                <input type="number" class="w-full p-1 border rounded" placeholder="2000" value="2000">
                            </td>
                            <td class="p-2 border">
                                <input type="number" class="w-full p-1 border rounded bg-gray-50" placeholder="100" value="100" readonly>
                            </td>
                            <td class="p-2 border">
                                <input type="text" class="w-full p-1 border rounded" placeholder="INV002">
                            </td>
                            <td class="p-2 border text-center align-middle" rowspan="2">
                                <button class="px-2 py-1 bg-red-500 text-white rounded text-sm">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <tr class="border-t-0">
                            <td class="p-2 border">
                                <input type="number" class="w-full p-1 border rounded" placeholder="20">
                            </td>
                            <td class="p-2 border">
                                <div class="flex flex-wrap gap-1">
                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">設備採購</span>
                                </div>
                            </td>
                            <td class="p-2 border">
                                <textarea class="w-full p-1 border rounded" rows="1" placeholder="備註"></textarea>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 技術實現 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🔧 技術實現</h2>
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">1. 欄位分配邏輯</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 將欄位分成兩列，每個欄位只出現一次
const fieldsPerRow = Math.ceil(individualPropertyFields.length / 2);
const firstRowFields = individualPropertyFields.slice(0, fieldsPerRow);
const secondRowFields = individualPropertyFields.slice(fieldsPerRow);

// 儲存欄位分配資訊
window.tableFieldsLayout = {
    firstRowFields: firstRowFields,
    secondRowFields: secondRowFields
};</code></pre>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">2. 表格標題生成</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 添加第一列標題
firstRowFields.forEach(field => {
    tableHeader.innerHTML += `&lt;th class="p-2 border text-center"&gt;${getFieldLabel(field)}&lt;/th&gt;`;
});

// 添加第二列標題
secondRowFields.forEach(field => {
    tableSubHeader.innerHTML += `&lt;th class="p-2 border text-center"&gt;${getFieldLabel(field)}&lt;/th&gt;`;
});</code></pre>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">3. 行生成邏輯</h3>
                    <pre class="text-sm text-gray-600 bg-white p-3 rounded border overflow-x-auto"><code>// 創建兩行：第一行和第二行
const firstRow = document.createElement('tr');
const secondRow = document.createElement('tr');

// 添加第一行的欄位
layout.firstRowFields.forEach(field => {
    const cell = document.createElement('td');
    cell.innerHTML = generateFieldInput(field, rowCounter);
    firstRow.appendChild(cell);
});

// 添加第二行的欄位
layout.secondRowFields.forEach(field => {
    const cell = document.createElement('td');
    cell.innerHTML = generateFieldInput(field, rowCounter);
    secondRow.appendChild(cell);
});</code></pre>
                </div>
            </div>
        </div>
        
        <!-- 優勢對比 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">📊 優勢對比</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-red-600">❌ 修正前問題</h3>
                    <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <ul class="text-sm text-red-700 space-y-2">
                            <li>• 表格過寬，需要水平滾動</li>
                            <li>• 欄位重複，資料混亂</li>
                            <li>• 使用者需要在兩列中重複輸入</li>
                            <li>• 視覺上混亂，難以理解</li>
                            <li>• 浪費螢幕空間</li>
                        </ul>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-green-600">✅ 修正後優勢</h3>
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <ul class="text-sm text-green-700 space-y-2">
                            <li>• 表格寬度減半，無需滾動</li>
                            <li>• 每個欄位只出現一次，邏輯清晰</li>
                            <li>• 使用者只需輸入一次</li>
                            <li>• 視覺整潔，易於理解</li>
                            <li>• 有效利用螢幕空間</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 測試按鈕 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">🧪 測試驗證</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    開啟批次頁面
                </button>
                <button onclick="showLayoutComparison()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    <i class="fas fa-columns mr-2"></i>
                    佈局對比
                </button>
                <button onclick="showImplementationDetails()" 
                    class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                    <i class="fas fa-code mr-2"></i>
                    實現細節
                </button>
            </div>
            
            <!-- 佈局對比 -->
            <div id="layoutComparison" class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg hidden">
                <h3 class="font-medium text-blue-800 mb-3">📐 佈局對比</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="text-sm font-medium text-blue-700 mb-2">修正前 (錯誤)</h4>
                        <div class="text-xs text-blue-600 bg-white p-2 rounded border">
                            第一列：金額 | 稅額 | 發票號碼 | 手續費<br>
                            第二列：金額 | 稅額 | 發票號碼 | 手續費 ❌
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-blue-700 mb-2">修正後 (正確)</h4>
                        <div class="text-xs text-blue-600 bg-white p-2 rounded border">
                            第一列：金額 | 稅額 | 發票號碼<br>
                            第二列：手續費 | 標籤 | 備註 ✅
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 實現細節 -->
            <div id="implementationDetails" class="mt-6 p-4 bg-purple-50 border border-purple-200 rounded-lg hidden">
                <h3 class="font-medium text-purple-800 mb-3">💻 關鍵修正點</h3>
                <ul class="text-sm text-purple-700 space-y-2">
                    <li>• <strong>欄位分配：</strong>使用 slice() 方法將欄位陣列分成兩部分</li>
                    <li>• <strong>標題生成：</strong>第一列和第二列分別對應不同的欄位</li>
                    <li>• <strong>行生成：</strong>每行包含兩個 tr 元素，分別對應兩列欄位</li>
                    <li>• <strong>資料一致性：</strong>確保每個欄位只出現一次，避免重複</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function openBatchPage() {
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function showLayoutComparison() {
            const element = document.getElementById('layoutComparison');
            element.classList.toggle('hidden');
        }
        
        function showImplementationDetails() {
            const element = document.getElementById('implementationDetails');
            element.classList.toggle('hidden');
        }
        
        // 頁面載入時顯示成功訊息
        document.addEventListener('DOMContentLoaded', function() {
            // 顯示成功通知
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>兩列佈局邏輯已修正完成！</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            // 3秒後自動隱藏
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        });
    </script>
</body>
</html>
