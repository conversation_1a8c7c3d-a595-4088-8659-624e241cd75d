
/**
 * @file transactionCreate_PaymentDescriptionMegaMenu_Module.js
 * @description 這個檔案負責管理交易描述（會計科目）的 Mega Menu 選單。
 * - 它包括了選單的渲染、選項的篩選、以及選項點擊後的顯示邏輯。
 * - 作為一個獨立的模組，它與其他部分解耦，方便維護和擴展。
 */

// 更新交易描述下拉選單顯示，
// 與transactionCreate_Service.js 函數類似 getPaymentDescriptionName(paymentDescriptionCode) ***需進行整合***
async function updatePaymentDescriptionMenuDisplay(transactionType, paymentDescriptionCode) {
    if (!paymentDescriptionCode) return;
    
    const selectedSpan = document.getElementById('paymentDescriptionSelected');
    const categories = await getTransactionCategoriesAll();
    const items = await getTransactionCategoryItemsAll();
    const accountingItems = await getAccountingItemsAll();
    
    // 找到對應的會計項目
    const item = items.find(item => String(item.accountingCode) === String(paymentDescriptionCode));
    if (item) {
        // 找到所屬分類
        const category = categories.find(cat => cat.id == item.categoryId && cat.type === transactionType);
        if (category) {
            selectedSpan.textContent = `${category.name}/${item.accountingName}(${item.accountingCode})`;
            return;
        }
    } 
    const accountingItem = accountingItems.find(item => String(item.code) === String(paymentDescriptionCode));
    if (accountingItem) {
        if(transactionType === 'transfer'){
            selectedSpan.textContent = `轉移-${accountingItem.type}-${accountingItem.name}(${accountingItem.code})`;
            return;
        }else{
            selectedSpan.textContent = `尚未分類-${accountingItem.type}-${accountingItem.name}(${accountingItem.code})`;
            return;
        }
    } else {
        // 如果找不到對應項目，顯示編碼
        selectedSpan.textContent = paymentDescriptionCode;
        return;
    }
}


/**
 * @description 渲染會計項目多層下拉式清單
 * @param 交易類型(收入income /支出expense)
 * */
async function renderPaymentDescriptionMegaMenu(transactionType='expense',checkType = false) {

    // 獲取必要的 DOM 元素
    const menuBtn = document.getElementById('paymentDescriptionMenuBtn');
    const menuDropdown = document.getElementById('paymentDescriptionMenuDropdown');
    const selectedSpan = document.getElementById('paymentDescriptionSelected');
    const hiddenInput = document.getElementById('paymentDescription');
    let currentType ;
    // 檢查交易類型是否符合
    if(checkType){
        currentType = document.querySelector('input[name="transactionType"]:checked').value;
        if(currentType !== transactionType) return;//輸入渲染與頁面選擇交易類型不符時，則不渲染
    }else{
        currentType = transactionType;
    }
    try {
        // 首先，移除舊的事件監聽器（如果有）
        const oldMenuBtn = menuBtn.cloneNode(true);
        menuBtn.parentNode.replaceChild(oldMenuBtn, menuBtn);
        
        // 從 IndexedDB 獲取資料
        const categories = await getTransactionCategoriesAll();
        const items = await getTransactionCategoryItemsAll();
        
        // 依據目前的交易類型（支出/收入）過濾分類
        const filteredCategories = categories.filter(category => category.type === currentType);

        //排序
        filteredCategories.sort((a, b) => a.name.localeCompare(b.name));

        // 建立第一層 - 分類
        let html = '<div class="flex">';
        html += '<ul class="w-1/3 border-r">';
        filteredCategories.forEach(cat => {
            html += `<li class="px-4 py-2 hover:bg-blue-100 cursor-pointer category-item" data-category-id="${cat.id}">${cat.name}</li>`;
        });
        html += '</ul>';
        html += '<div class="w-2/3 h-full overflow-y-auto"><ul class="w-full" id="itemList"></ul></div>';
        html += '</div>';
        menuDropdown.innerHTML = html;

        // 重新獲取下拉選單元素（避免事件綁定問題）
        const updatedMenuDropdown = document.getElementById('paymentDescriptionMenuDropdown');
        const updatedSelectedSpan = document.getElementById('paymentDescriptionSelected');

        // 互動邏輯
        const itemList = updatedMenuDropdown.querySelector('#itemList');
        let currentCategoryId = null;

        // 第一層點擊 - 顯示該分類下的項目
        updatedMenuDropdown.querySelectorAll('.category-item').forEach(li => {
            li.addEventListener('mouseenter', function() {
                currentCategoryId = this.dataset.categoryId;
                
                // 依據所選分類過濾項目
                const categoryItems = items.filter(item => item.categoryId == currentCategoryId);
                
                // 顯示項目列表
                itemList.innerHTML = categoryItems.length > 0 
                    ? categoryItems.map(item => `<li class="px-4 py-2 hover:bg-blue-200 cursor-pointer item" data-code="${item.accountingCode}" data-name="${item.accountingName}">${item.accountingCode} - ${item.accountingName}</li>`).join('')
                    : '<li class="px-4 py-2 text-gray-500">無項目</li>';
                
                // 給新產生的項目添加點擊事件
                attachItemClickEvents(itemList, currentCategoryId, filteredCategories, updatedSelectedSpan, hiddenInput, updatedMenuDropdown);
            });
        });

        // 點擊外部關閉
        
        document.addEventListener('mousedown', function(e) {
            if (!menuDropdown.contains(e.target) && !menuBtn.contains(e.target)) {
                menuDropdown.classList.add('hidden');
            }
        });

        // 重新獲取按鈕元素（因為我們剛才替換了它）
        const newMenuBtn = document.getElementById('paymentDescriptionMenuBtn');

        
        // 按鈕點擊展開/收合
        newMenuBtn.addEventListener('click', function(e) {

            e.stopPropagation();
            updatedMenuDropdown.classList.toggle('hidden');
        });
    } catch (error) {
        console.error("載入會計選單時發生錯誤:", error);
    }
}

// 抽取出來的項目點擊事件附加函數
function attachItemClickEvents(itemList, currentCategoryId, filteredCategories, selectedSpan, hiddenInput, menuDropdown) {
    itemList.querySelectorAll('.item').forEach(itemLi => {
        itemLi.addEventListener('click', function(e) {

            // 找到所選分類的名稱
            const categoryName = filteredCategories.find(c => c.id == currentCategoryId)?.name || '';
            
            // 選定並顯示
            const displayText = `${categoryName}/${this.dataset.name}(${this.dataset.code})`;

            selectedSpan.textContent = displayText;
            hiddenInput.value = this.dataset.code;
            
            // 關閉選單
            menuDropdown.classList.add('hidden');
            
            e.stopPropagation(); // 阻止事件冒泡
        });
    });
}
