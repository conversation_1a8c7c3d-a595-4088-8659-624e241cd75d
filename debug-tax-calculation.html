<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>稅額計算調試工具</title>
    
    <!-- Tailwind CSS -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🔍 稅額計算調試工具</h1>
        
        <!-- 問題說明 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-red-600">❌ 問題描述</h2>
            <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 class="font-medium text-red-800 mb-2">稅額沒有顯示在輸入框中</h3>
                <ul class="text-red-700 text-sm space-y-1">
                    <li>• 稅額計算函式執行了，但輸入框沒有顯示計算結果</li>
                    <li>• 可能的原因：稅額輸入框不存在或選擇器錯誤</li>
                    <li>• 需要檢查稅額欄位是否為個別屬性</li>
                    <li>• 需要驗證稅率是否正確獲取</li>
                </ul>
            </div>
        </div>
        
        <!-- 調試步驟 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🔧 調試步驟</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 1: 檢查稅額欄位設置</h3>
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <ol class="text-sm text-blue-700 space-y-1">
                            <li>1. 開啟批次交易頁面</li>
                            <li>2. 檢查"稅額"是否勾選為個別屬性</li>
                            <li>3. 如果是共同屬性，取消勾選使其成為個別屬性</li>
                            <li>4. 確認表格中出現稅額欄位</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 2: 檢查稅別設置</h3>
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <ol class="text-sm text-green-700 space-y-1">
                            <li>1. 確認稅別欄位有正確的選項</li>
                            <li>2. 選擇一個有稅率的稅別（如5%營業稅）</li>
                            <li>3. 檢查稅別選項是否有 data-rate 屬性</li>
                            <li>4. 確認稅率值不為0</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 3: 測試稅額計算</h3>
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <ol class="text-sm text-yellow-700 space-y-1">
                            <li>1. 在金額欄位輸入一個數值（如1000）</li>
                            <li>2. 檢查稅額欄位是否自動更新</li>
                            <li>3. 如果沒有更新，在控制台執行調試命令</li>
                            <li>4. 檢查是否有錯誤訊息</li>
                        </ol>
                    </div>
                </div>
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-800">步驟 4: 手動觸發計算</h3>
                    <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <ol class="text-sm text-purple-700 space-y-1">
                            <li>1. 在控制台手動執行稅額計算函式</li>
                            <li>2. 檢查函式執行過程中的變數值</li>
                            <li>3. 確認稅額輸入框是否被找到</li>
                            <li>4. 驗證計算結果是否正確</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 調試命令 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibent mb-4">💻 調試命令</h2>
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <h3 class="font-medium text-gray-800 mb-2">在批次頁面控制台中執行：</h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">1. 檢查稅額欄位是否存在</h4>
                            <code class="text-xs text-gray-600 block">
                                // 檢查第1行的稅額輸入框<br>
                                const taxInput = document.querySelector('input[name="batch_taxAmount_1"]');<br>
                                console.log('稅額輸入框:', taxInput);<br>
                                if (taxInput) {<br>
                                &nbsp;&nbsp;console.log('稅額輸入框值:', taxInput.value);<br>
                                &nbsp;&nbsp;console.log('是否只讀:', taxInput.readOnly);<br>
                                } else {<br>
                                &nbsp;&nbsp;console.log('❌ 稅額輸入框不存在，請確認稅額是個別屬性');<br>
                                }
                            </code>
                        </div>
                        
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">2. 檢查稅率設置</h4>
                            <code class="text-xs text-gray-600 block">
                                // 檢查稅別選擇器和稅率<br>
                                const taxSelect = document.querySelector('select[name="batch_taxTypeId_1"]') || document.getElementById('batch_taxTypeId');<br>
                                console.log('稅別選擇器:', taxSelect);<br>
                                if (taxSelect && taxSelect.value) {<br>
                                &nbsp;&nbsp;const option = taxSelect.options[taxSelect.selectedIndex];<br>
                                &nbsp;&nbsp;console.log('選中的稅別:', option.text);<br>
                                &nbsp;&nbsp;console.log('稅率:', option.dataset.rate);<br>
                                } else {<br>
                                &nbsp;&nbsp;console.log('❌ 沒有選擇稅別或稅別選擇器不存在');<br>
                                }
                            </code>
                        </div>
                        
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">3. 手動執行稅額計算</h4>
                            <code class="text-xs text-gray-600 block">
                                // 手動觸發第1行的稅額計算<br>
                                console.log('開始計算第1行稅額...');<br>
                                calculateRowTaxAmount(1);<br>
                                console.log('稅額計算完成');<br><br>
                                // 檢查計算後的結果<br>
                                const taxInputAfter = document.querySelector('input[name="batch_taxAmount_1"]');<br>
                                if (taxInputAfter) {<br>
                                &nbsp;&nbsp;console.log('計算後的稅額:', taxInputAfter.value);<br>
                                }
                            </code>
                        </div>
                        
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">4. 檢查金額輸入框</h4>
                            <code class="text-xs text-gray-600 block">
                                // 檢查金額輸入框<br>
                                const amountInput = document.querySelector('input[name="batch_amount_1"]');<br>
                                console.log('金額輸入框:', amountInput);<br>
                                if (amountInput) {<br>
                                &nbsp;&nbsp;console.log('金額值:', amountInput.value);<br>
                                &nbsp;&nbsp;// 設置測試金額<br>
                                &nbsp;&nbsp;amountInput.value = '1000';<br>
                                &nbsp;&nbsp;// 觸發計算<br>
                                &nbsp;&nbsp;calculateRowTaxAmount(1);<br>
                                }
                            </code>
                        </div>
                        
                        <div class="p-3 bg-white border rounded">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">5. 完整的調試流程</h4>
                            <code class="text-xs text-gray-600 block">
                                // 完整調試流程<br>
                                function debugTaxCalculation(rowNum = 1) {<br>
                                &nbsp;&nbsp;console.log(`=== 調試第${rowNum}行稅額計算 ===`);<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;// 1. 檢查金額<br>
                                &nbsp;&nbsp;const amountInput = document.querySelector(`input[name="batch_amount_${rowNum}"]`);<br>
                                &nbsp;&nbsp;console.log('金額輸入框:', amountInput?.value || '不存在');<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;// 2. 檢查稅率<br>
                                &nbsp;&nbsp;const taxSelect = document.querySelector(`select[name="batch_taxTypeId_${rowNum}"]`) || document.getElementById('batch_taxTypeId');<br>
                                &nbsp;&nbsp;const taxRate = taxSelect?.options[taxSelect.selectedIndex]?.dataset.rate || 0;<br>
                                &nbsp;&nbsp;console.log('稅率:', taxRate);<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;// 3. 檢查稅額輸入框<br>
                                &nbsp;&nbsp;const taxInput = document.querySelector(`input[name="batch_taxAmount_${rowNum}"]`);<br>
                                &nbsp;&nbsp;console.log('稅額輸入框:', taxInput ? '存在' : '不存在');<br>
                                &nbsp;&nbsp;<br>
                                &nbsp;&nbsp;// 4. 執行計算<br>
                                &nbsp;&nbsp;if (amountInput && taxRate > 0) {<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;calculateRowTaxAmount(rowNum);<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;console.log('計算後稅額:', taxInput?.value || '無法顯示');<br>
                                &nbsp;&nbsp;} else {<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;console.log('❌ 計算條件不滿足');<br>
                                &nbsp;&nbsp;}<br>
                                }<br><br>
                                // 執行調試<br>
                                debugTaxCalculation(1);
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 常見問題解決方案 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibent mb-4">🔧 常見問題解決方案</h2>
            <div class="space-y-4">
                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h3 class="font-medium text-yellow-800 mb-2">問題 1: 稅額輸入框不存在</h3>
                    <p class="text-yellow-700 text-sm mb-2">
                        <strong>原因：</strong>稅額欄位被設為共同屬性，沒有在表格中顯示個別的稅額輸入框。
                    </p>
                    <p class="text-yellow-700 text-sm">
                        <strong>解決方案：</strong>取消勾選"稅額"的共同屬性，使其成為個別屬性。
                    </p>
                </div>
                
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 class="font-medium text-blue-800 mb-2">問題 2: 稅率為0或未設置</h3>
                    <p class="text-blue-700 text-sm mb-2">
                        <strong>原因：</strong>稅別選項沒有正確的 data-rate 屬性，或者沒有選擇稅別。
                    </p>
                    <p class="text-blue-700 text-sm">
                        <strong>解決方案：</strong>確認稅別選項有正確的稅率設置，並選擇一個有效的稅別。
                    </p>
                </div>
                
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-medium text-green-800 mb-2">問題 3: 事件監聽器未正確綁定</h3>
                    <p class="text-green-700 text-sm mb-2">
                        <strong>原因：</strong>金額輸入框的事件監聽器沒有正確設置，或者被重複綁定。
                    </p>
                    <p class="text-green-700 text-sm">
                        <strong>解決方案：</strong>重新載入頁面，或者手動執行 setupRowEventListeners(rowNum)。
                    </p>
                </div>
            </div>
            
            <div class="mt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                <button onclick="openBatchPage()" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    開啟批次頁面
                </button>
                <button onclick="copyDebugCode()" 
                    class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                    <i class="fas fa-copy mr-2"></i>
                    複製調試代碼
                </button>
                <button onclick="showQuickFix()" 
                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                    <i class="fas fa-wrench mr-2"></i>
                    快速修復
                </button>
            </div>
        </div>
    </div>

    <script>
        function openBatchPage() {
            window.open('transactions-batch-create.html', '_blank');
        }
        
        function copyDebugCode() {
            const debugCode = `
function debugTaxCalculation(rowNum = 1) {
    console.log(\`=== 調試第\${rowNum}行稅額計算 ===\`);
    
    const amountInput = document.querySelector(\`input[name="batch_amount_\${rowNum}"]\`);
    console.log('金額輸入框:', amountInput?.value || '不存在');
    
    const taxSelect = document.querySelector(\`select[name="batch_taxTypeId_\${rowNum}"]\`) || document.getElementById('batch_taxTypeId');
    const taxRate = taxSelect?.options[taxSelect.selectedIndex]?.dataset.rate || 0;
    console.log('稅率:', taxRate);
    
    const taxInput = document.querySelector(\`input[name="batch_taxAmount_\${rowNum}"]\`);
    console.log('稅額輸入框:', taxInput ? '存在' : '不存在');
    
    if (amountInput && taxRate > 0) {
        calculateRowTaxAmount(rowNum);
        console.log('計算後稅額:', taxInput?.value || '無法顯示');
    } else {
        console.log('❌ 計算條件不滿足');
    }
}

debugTaxCalculation(1);
            `;
            
            navigator.clipboard.writeText(debugCode).then(() => {
                alert('調試代碼已複製到剪貼板！請在批次頁面的控制台中貼上執行。');
            });
        }
        
        function showQuickFix() {
            alert('快速修復步驟：\n1. 確認稅額欄位為個別屬性\n2. 選擇有稅率的稅別\n3. 在金額欄位輸入數值\n4. 檢查稅額是否自動計算');
        }
        
        // 頁面載入時顯示說明
        document.addEventListener('DOMContentLoaded', function() {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-bug mr-2"></i>
                    <span>稅額計算調試工具已準備就緒</span>
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        });
    </script>
</body>
</html>
